package com.newland.detb.exceptionmanage.service.impl;

import com.newland.detb.exceptionmanage.entity.DictInfo;
import com.newland.detb.exceptionmanage.service.DictInfoService;
import com.newland.detb.exceptionmanage.mapper.DictInfoMapper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【HE_DICT_INFO(字典表)】的数据库操作Service实现
* @createDate 2023-05-18 15:49:31
*/
@Service
public class DictInfoServiceImpl implements DictInfoService{

    @Resource
    private DictInfoMapper dictInfoMapper;

    /**
     * 保存字典数据
     *
     * @param dictInfo
     * @return
     */
    @Override
    public int insert(DictInfo dictInfo) {
        return dictInfoMapper.insert(dictInfo);
    }

    /**
     * 根据name查询字典数据
     *
     * @param name
     * @return
     */
    @Override
    public DictInfo selectDictInfo(String name) {
        return dictInfoMapper.selectDictInfo(name);
    }

    /**
     * 查询全部字段数据
     *
     * @return
     */
    @Override
    public List<DictInfo> selectAllDictInfo(String name, String value) {
        return dictInfoMapper.selectAllDictInfo(name, value);
    }

    /**
     * 根据name删除字典表
     *
     * @param name
     * @return
     */
    @Override
    public int deleteDictInfoByName(String name) {
        return dictInfoMapper.deleteDictInfoByName(name);
    }

    /**
     * 更新字典数据
     *
     * @param dictInfo
     * @return
     */
    @Override
    public int update(DictInfo dictInfo) {
        return dictInfoMapper.update(dictInfo);
    }
}




