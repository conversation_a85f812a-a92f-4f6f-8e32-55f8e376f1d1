<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.newland.detb.exceptionmanage.mapper.BusinessExceptionOrderMapper">
    <!-- bossmain.pkg_order -->
    <select id="invokeInspection" statementType="CALLABLE">
        {call bossmain.pkg_order.err_order_inspec_for_warn(#{p_order_table,mode=IN,jdbcType=VARCHAR},#{p_warning_id,mode=IN,jdbcType=VARCHAR},#{p_exec_msg,mode=OUT,jdbcType=VARCHAR})}
    </select>
    <select id="invokeAnalyze" statementType="CALLABLE">
        {call bossmain.pkg_order.err_order_analyse_for_warn(#{p_warning_id,mode=IN,jdbcType=VARCHAR},#{p_exec_msg,mode=OUT,jdbcType=VARCHAR})}
    </select>
    <select id="invokeRecovery" statementType="CALLABLE">
        {call bossmain.pkg_order.err_order_repaired_for_warn(#{p_warning_id,mode=IN,jdbcType=VARCHAR},#{p_exec_msg,mode=OUT,jdbcType=VARCHAR})}
    </select>
    <select id="retryOperation">
        {call bossmain.retry_operation()}
    </select>
    <select id="repairBusinessExceptionOrder" statementType="CALLABLE">
        {call ${name}}
    </select>
</mapper>