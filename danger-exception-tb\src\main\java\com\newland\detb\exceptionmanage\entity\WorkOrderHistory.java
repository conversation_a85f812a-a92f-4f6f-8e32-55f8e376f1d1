package com.newland.detb.exceptionmanage.entity;

import java.io.Serializable;
import java.util.Date;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 治理历史工单信息表
 * @TableName HE_WORK_ORDER_HISTORY
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class WorkOrderHistory implements Serializable {
    /**
     * id
     */
    @ExcelProperty("工单ID")
    private Long id;

    /**
     * 异常工单表数据id
     */
    @ExcelProperty("异常告警ID")
    private Long dataId;

    /**
     * 异常场景编码
     */
    @ExcelProperty("异常场景编码")
    private String sceneCode;

    /**
     * 治理策略描述
     */
    @ExcelProperty("治理策略")
    private String strategyRemark;

    /**
     * 环境
     */
    @ExcelProperty("环境")
    private String env;

    /**
     * 主机名
     */
    @ExcelProperty("主机名")
    private String hostName;

    /**
     * 应用名
     */
    @ExcelProperty("应用名")
    private String appName;

    /**
     * 是否完成授权确认操作，默认0：没有完成，1：已经完成
     */
    @ExcelProperty("授权是否完成")
    private Integer isAuth;

    /**
     * 是否触发单项巡检是否判断完成，默认0：未判断，1：判断完成
     */
    @ExcelProperty("是否触发单项巡检检查")
    private Integer isInspection;

    /**
     * 是否删除，默认0：表示未删除
     */
    @ExcelIgnore
    private Integer deleted;

    /**
     * 创建时间
     */
    @ExcelProperty("创建时间")
    private Date createTime;

    /**
     * 最终的治理结果，可以用于汇总同步消息
     */
    @ExcelProperty("治理结果")
    private String governanceRes;

    /**
     * 恢复命令
     */
    @ExcelProperty("恢复命令")
    private String recoveryCommand;

    private static final long serialVersionUID = 1L;
}