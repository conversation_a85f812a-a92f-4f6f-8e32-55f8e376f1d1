package com.newland.detb.exceptionmanage.service.impl;

import com.newland.detb.exception.UpdateProInfoException;
import com.newland.detb.exceptionmanage.entity.NoncloudInfo;
import com.newland.detb.exceptionmanage.service.NoncloudInfoService;
import com.newland.detb.exceptionmanage.mapper.NoncloudInfoMapper;
import com.newland.detb.lock.DatabaseLock;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.CopyOnWriteArrayList;

/**
* <AUTHOR>
* @description 针对表【HE_NONCLOUD_INFO(非云生产资料信息表)】的数据库操作Service实现
* @createDate 2023-05-04 15:56:12
*/
@Slf4j
@Service
public class NoncloudInfoServiceImpl implements NoncloudInfoService {

    @Resource
    private NoncloudInfoMapper noncloudInfoMapper;

    @Resource
    private DatabaseLock databaseLock;

    /**
     * 保存非云生产资料信息
     *
     * @param noncloudInfo
     * @return
     */
    @Override
    public int insert(NoncloudInfo noncloudInfo) {
        return noncloudInfoMapper.insert(noncloudInfo);
    }

    /**
     * 根据id查询非云生产资料信息
     *
     * @param id
     * @return
     */
    @Override
    public NoncloudInfo selectNonCloudInfoById(Long id) {
        return noncloudInfoMapper.selectNonCloudInfoById(id);
    }

    /**
     * 查询全部的非云生产资料信息
     *
     * @return
     */
    @Override
    public List<NoncloudInfo> selectAllNonCloudInfo() {
        return noncloudInfoMapper.selectAllNonCloudInfo();
    }

    /**
     * 根据id删除非云生产资料信息
     *
     * @param id
     * @return
     */
    @Override
    public int deleteNonCloudInfoById(Long id) {
        return noncloudInfoMapper.deleteNonCloudInfoById(id);
    }

    /**
     * 更新非云生产资料信息
     *
     * @param noncloudInfo
     * @return
     */
    @Override
    public int updateNonCloudInfoById(NoncloudInfo noncloudInfo) {
        return noncloudInfoMapper.updateNonCloudInfoById(noncloudInfo);
    }

    /**
     * 根据条件查询是否对应的异常告警的恢复策略
     *
     * @param processName
     * @param hostIP
     * @param hostName
     * @return
     */
    @Override
    public List<NoncloudInfo> selectMatchByCondition(String processName, String hostIP, String hostName) {
        return noncloudInfoMapper.selectMatchByCondition(processName, hostIP, hostName);
    }

    @Override
    public List<NoncloudInfo> findPage(String hostIp, String processName) {
        return noncloudInfoMapper.findPage(hostIp, processName);
    }

    @Override
    public List<NoncloudInfo> selectNonCloudInfoByIds(List<Long> exportList) {
        return noncloudInfoMapper.selectNonCloudInfoByIds(exportList);
    }

    /**
     * 定期同步EBOSS上线工具应用部署信息到生产资料表中
     */
    @Transactional(rollbackFor = UpdateProInfoException.class)
    @Override
    public void updateProInfo() {
        //先执行查询操作，判断是否可以从视图表中查询到最新的数据，再进行更新操作，否则不进行操作
        List<NoncloudInfo> proInfoFromViewList = noncloudInfoMapper.getProInfoFromView();
        if (proInfoFromViewList != null) {
            try {
                //加锁
                databaseLock.lock("定期同步生产资料信息","updateProInfo");
                //再执行旧表数据清空操作
                int clearResult = noncloudInfoMapper.clearNonCloudInfo();
                int batchResult = 0;
                if (clearResult != 0) {
                    //清空成功，再执行数据插入操作
                    batchResult = noncloudInfoMapper.insertBatch(proInfoFromViewList);
                }
                log.info("[定期更新生产资料信息]-更新操作完成，更新条数：{}", batchResult);
            } catch (Exception e) {
                e.printStackTrace();
                log.error("[定期更新生产资料信息]-出现异常，进行回滚：{}",e.getMessage());
                throw new UpdateProInfoException(515,"定期更新生产资料信息]-出现异常");
            } finally {
                databaseLock.unlock("定期同步生产资料信息","updateProInfo");
            }
        }else {
            log.info("[定期更新生产资料信息]-未查询到最新应用部署信息，操作终止！");
        }
    }


}




