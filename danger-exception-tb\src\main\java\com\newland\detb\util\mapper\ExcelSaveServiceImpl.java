package com.newland.detb.util.mapper;

import com.newland.detb.common.entity.HeExceptionDataBean;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-04-17 017 23:02:35
 * @description 用于测试excel导入到数据库进行保存功能，在此自定义实现类中需要实现父接口，重写save方法，根据自己的业务逻辑去保存
 */
@Service
public class ExcelSaveServiceImpl implements ExcelBaseService {

    /**
     * 根据自己业务去实现对应的保存逻辑
     * @param data
     * @return
     */
    @Override
    public Integer save(List<?> data) {
        data.forEach(item -> {
            System.out.println("item:" + item);
        });
        return 0;
    }
}
