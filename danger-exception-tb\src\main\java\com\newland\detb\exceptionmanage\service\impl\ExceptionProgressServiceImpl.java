package com.newland.detb.exceptionmanage.service.impl;

import com.newland.detb.exceptionmanage.entity.ExceptionProgress;
import com.newland.detb.exceptionmanage.entity.ProgressDTO;
import com.newland.detb.exceptionmanage.service.ExceptionProgressService;
import com.newland.detb.exceptionmanage.mapper.ExceptionProgressMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.CopyOnWriteArrayList;

/**
* <AUTHOR>
* @description 针对表【HE_EXCEPTION_PROGRESS(异常治理进度表)】的数据库操作Service实现
* @createDate 2023-04-20 11:19:09
*/
@Slf4j
@Service
public class ExceptionProgressServiceImpl implements ExceptionProgressService{

    @Resource
    private ExceptionProgressMapper exceptionProgressMapper;

    /**
     * 定时任务扫描工单表时，当扫描到异常后，需要同步初始化进度表，需要初始化创建6条数据
     *
     * @param id   工单表数据的id
     * list:保存着每一个ProgressDTO实体类的对象，有两个属性，parentProgress和childProgress
     * @return
     */
    @Override
    public int insertInitData(Long id) {
        List<ProgressDTO> list = new CopyOnWriteArrayList<>();

        //0-0：告警接收等待处理； 0-1：告警接收完成；0-2：告警入库；0-3：治理标志位判断完成;0-4：告警解析---新增2个节点：告警入库、告警解析（因此重新调整0父节点的含义）
        ProgressDTO progress00 = new ProgressDTO(0, 0);
        ProgressDTO progress01 = new ProgressDTO(0, 1);
        ProgressDTO progress02 = new ProgressDTO(0, 2);
        ProgressDTO progress03 = new ProgressDTO(0, 3);
        ProgressDTO progress04 = new ProgressDTO(0, 4);
        list.add(progress00);
        list.add(progress01);
        list.add(progress02);
        list.add(progress03);
        list.add(progress04);
        //1-0：异常匹配等待处理；1-1：提取关键字；1-2：异常场景匹配；1-3：异常二次确认（使用salt工具，将130/131设置为master）；1-4：确认异常场景
        ProgressDTO progress10 = new ProgressDTO(1, 0);
        ProgressDTO progress12 = new ProgressDTO(1, 2);
        ProgressDTO progress13 = new ProgressDTO(1, 3);
        ProgressDTO progress14 = new ProgressDTO(1, 4);
        list.add(progress10);
        list.add(progress12);
        list.add(progress13);
        list.add(progress14);
        //2-0：生成治理策略等待处理；2-1：查询治理策略；2-2：生成治理步骤；2-3：生成治理工单
        ProgressDTO progress20 = new ProgressDTO(2, 0);
        ProgressDTO progress21 = new ProgressDTO(2, 1);
        ProgressDTO progress22 = new ProgressDTO(2, 2);
        ProgressDTO progress23 = new ProgressDTO(2, 3);
        list.add(progress20);
        list.add(progress21);
        list.add(progress22);
        list.add(progress23);
        //3-0：异常治理执行等待处理；3-1：扫描治理工单；3-2：授权确认*；3-3：执行治理操作；3-4：是否触发巡检
        ProgressDTO progress30 = new ProgressDTO(3, 0);
        ProgressDTO progress31 = new ProgressDTO(3, 1);
        ProgressDTO progress32 = new ProgressDTO(3, 2);
        ProgressDTO progress33 = new ProgressDTO(3, 3);
        ProgressDTO progress34 = new ProgressDTO(3, 4);
        list.add(progress30);
        list.add(progress31);
        list.add(progress32);
        list.add(progress33);
        list.add(progress34);
        //4-0：信息同步等待处理；4-1：汇总治理过程；4-2：同步信息发送；4-3：处理结束
        ProgressDTO progress40 = new ProgressDTO(4, 0);
        ProgressDTO progress41 = new ProgressDTO(4, 1);
        ProgressDTO progress42 = new ProgressDTO(4, 2);
        ProgressDTO progress43 = new ProgressDTO(4, 3);
        list.add(progress40);
        list.add(progress41);
        list.add(progress42);
        list.add(progress43);
        //5-0：异常治理还未结束，等待中，5-1：异常治理流程结束
        ProgressDTO progress50 = new ProgressDTO(5, 0);
        ProgressDTO progress51 = new ProgressDTO(5, 1);
        list.add(progress50);
        list.add(progress51);
        return exceptionProgressMapper.insertInitData(id, list);
    }
}
