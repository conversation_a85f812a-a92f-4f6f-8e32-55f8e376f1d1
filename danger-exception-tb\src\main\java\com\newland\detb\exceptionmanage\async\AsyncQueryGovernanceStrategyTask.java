package com.newland.detb.exceptionmanage.async;

import com.newland.detb.common.entity.HeExceptionDataBean;
import com.newland.detb.exceptionmanage.entity.BusinessType;
import com.newland.detb.exceptionmanage.entity.ExceptionScenario;
import com.newland.detb.exceptionmanage.entity.GovernanceStrategy;
import com.newland.detb.exceptionmanage.mapper.ExceptionProgressMapper;
import com.newland.detb.exceptionmanage.service.GovernanceStrategyService;
import com.newland.detb.quartz.mapper.HeExceptionDataMapper;
import com.newland.detb.quartz.service.HeExceptionDataService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-04-26 026 15:59:21
 * @description 查询治理策略的异步任务
 */
// @Lazy
@Slf4j
@Component
public class AsyncQueryGovernanceStrategyTask extends BaseAsyncTask{

    @Resource
    private GovernanceStrategyService governanceStrategyService;

    @Lazy
    @Resource
    private AsyncProduceGovernanceStepTask asyncProduceGovernanceStepTask;

    /**
     * 查询治理策略操作
     * @param heExceptionDataBean 工单表数据
     * @param exceptionScenario 异常场景库数据
     */
    @Async("taskExecutor")
    @Override
    public void executeAsync(String traceId,HeExceptionDataBean heExceptionDataBean, ExceptionScenario exceptionScenario) {
        asyncPrintLogInfo(traceId,"查询治理策略");
        updateHeExceptionDataWithRemark(heExceptionDataBean,2,"正在执行查询治理策略模块");
        updateProgress(heExceptionDataBean,2,1,"doing","正在执行查询治理策略模块");
        asyncPrintLogInfo(traceId,2,1,"正在查询异常治理策略");
        List<GovernanceStrategy> governanceStrategies = governanceStrategyService.queryBySceneCode(exceptionScenario.getSceneCode().toString());
        if (governanceStrategies != null && governanceStrategies.size() == 1) {
            //正常执行后续生成治理步骤操作
            GovernanceStrategy strategy = governanceStrategies.get(0);
            asyncPrintLogInfo(traceId,2,1,"已经查询到异常治理策略，编码：" + strategy.getStrategyCode() + "，治理策略：" + strategy.getRemark());
            updateProgress(heExceptionDataBean,2,1,"success","治理策略查询成功:" + governanceStrategies.get(0).getRemark());
            //调用生成治理策略异步任务处理
            asyncProduceGovernanceStepTask.executeAsync(traceId,heExceptionDataBean,exceptionScenario,strategy);
        }else {
            asyncPrintErrorLogInfo(traceId,2,1,"查询到的异常治理策略不唯一，请管理员检查");
            updateHeExceptionDataWithRemark(heExceptionDataBean,6,"查询到的治理策略不唯一，请手动处理，治理结束");
            updateProgress(heExceptionDataBean,2,1,"fail","查询到的治理策略不唯一，请手动处理");
            updateProgress(heExceptionDataBean,5,1,"fail","查询到的治理策略不唯一，请手动处理，治理结束");
            updateWorkOrderWhenFailCase(heExceptionDataBean.getDataId(), "查询到的治理策略不唯一，需要手动处理");
        }
    }

    /**
     * 业务异常单-查询治理策略操作（暂时写死）
     * @param heExceptionDataBean 工单表数据
     */
    @Async("taskExecutor")
    public void executeBusinessAsync(String traceId, HeExceptionDataBean heExceptionDataBean, BusinessType businessType) {
        asyncPrintLogInfo(traceId,"业务异常单-查询治理策略");
        updateHeExceptionDataWithRemark(heExceptionDataBean,2,"业务异常单-执行查询治理策略模块");
        updateProgress(heExceptionDataBean,2,1,"doing","正在执行查询治理策略模块");
        asyncPrintLogInfo(traceId,2,1,"业务异常单-正在查询异常治理策略");
        updateProgress(heExceptionDataBean,2,1,"success","业务异常单-治理策略查询成功:重发");
        //调用生成治理策略异步任务处理
        asyncProduceGovernanceStepTask.executeBusinessAsync(traceId,heExceptionDataBean,businessType);
    }
}
