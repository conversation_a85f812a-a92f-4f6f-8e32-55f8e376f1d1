package com.newland.detb.troubleshooting.service.impl;

import com.newland.detb.troubleshooting.entity.CgocDataDetailBean;
import com.newland.detb.troubleshooting.mapper.HeCgocDataDetailMapper;
import com.newland.detb.troubleshooting.service.CgocDataService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

/**
 * @Author: xumengfan
 * @CreateTime: 2023-04-20  13:27
 * @Description: TODO
 */
@Service
public class CgocDataServiceImpl implements CgocDataService {

    @Resource
    private HeCgocDataDetailMapper heCgocDataDetailMapper;

    @Override
    @Transactional
    public int insertHeCgocDataDetailMapper(CgocDataDetailBean cgocDataDetailBean) {
        return heCgocDataDetailMapper.insertHeCgocDataDetailMapper(cgocDataDetailBean);
    }

    @Override
    public int ifExistsCgocData(CgocDataDetailBean cgocDataDetailBean, String time) {
        return heCgocDataDetailMapper.ifExistsCgocData(cgocDataDetailBean, time);
    }

    @Override
    public int updateCgocDataDetailMapper(CgocDataDetailBean cgocDataDetailBean,String time) {
        return heCgocDataDetailMapper.updateCgocDataDetailMapper(cgocDataDetailBean,time);
    }
}
