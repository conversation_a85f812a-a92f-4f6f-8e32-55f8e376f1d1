<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <!-- 定义日志存放目录 -->
    <property name="logPath" value="/home/<USER>/liuning/exception_hidden/log"/>

    <appender name="FileAppender" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>/home/<USER>/liuning/exception_hidden/log/kafka-consumer.log</file>
        <encoder>
            <pattern>%date %level [%thread] %logger{10} [%file:%line] : %msg%n</pattern>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>DEBUG</level>
        </filter>
        <!--滚动策略，按照时间滚动 TimeBasedRollingPolicy-->
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <!--日志文件输出的文件名-->
            <FileNamePattern>${logPath}/kafka-consumer-%d{yyyy-MM-dd}.log</FileNamePattern>
            <!--日志文件保留天数-->
            <MaxHistory>30</MaxHistory>
            <!--用来指定日志文件的上限大小，那么到了这个值，就会删除旧的日志-->
            <!-- <totalSizeCap>3GB</totalSizeCap> -->
        </rollingPolicy>
        <triggeringPolicy class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
            <MaxFileSize>3GB</MaxFileSize>
        </triggeringPolicy>
    </appender>

    <appender name="FileAppenderInfo" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>/home/<USER>/liuning/exception_hidden/log/governance-info.log</file>
        <encoder>
            <pattern>%date %level [%thread] %logger{10} [%file:%line] : %msg%n</pattern>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>DEBUG</level>
        </filter>
        <!--滚动策略，按照时间滚动 TimeBasedRollingPolicy-->
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <!--日志文件输出的文件名-->
            <FileNamePattern>${logPath}/governance-info-%d{yyyy-MM-dd}.log</FileNamePattern>
            <!--日志文件保留天数-->
            <MaxHistory>30</MaxHistory>
            <!--用来指定日志文件的上限大小，那么到了这个值，就会删除旧的日志-->
            <!-- <totalSizeCap>3GB</totalSizeCap> -->
        </rollingPolicy>
        <triggeringPolicy class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
            <MaxFileSize>3GB</MaxFileSize>
        </triggeringPolicy>
    </appender>

    <appender name="KafkaAppender" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>/home/<USER>/liuning/exception_hidden/log/kafka-running-info.log</file>
        <encoder>
            <pattern>%date %level [%thread] %logger{10} [%file:%line] : %msg%n</pattern>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>DEBUG</level>
        </filter>
        <!--滚动策略，按照时间滚动 TimeBasedRollingPolicy-->
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <!--日志文件输出的文件名-->
            <FileNamePattern>${logPath}/kafka-running-info-%d{yyyy-MM-dd}.log</FileNamePattern>
            <!--日志文件保留天数-->
            <MaxHistory>30</MaxHistory>
            <!--用来指定日志文件的上限大小，那么到了这个值，就会删除旧的日志-->
            <!-- <totalSizeCap>3GB</totalSizeCap> -->
        </rollingPolicy>
        <triggeringPolicy class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
            <MaxFileSize>1GB</MaxFileSize>
        </triggeringPolicy>
    </appender>

    <appender name="ConsoleAppender" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%date %level [%thread] %logger{10} [%file:%line] : %msg%n</pattern>
        </encoder>
    </appender>

    <logger name="com.newland.detb.troubleshooting.log.KafkaLogger" level="DEBUG" additivity="false">
        <appender-ref ref="FileAppender"/>
    </logger>

    <logger name="com.newland.detb.exceptionmanage.log.GovernanceLogger" level="DEBUG" additivity="false">
        <appender-ref ref="FileAppenderInfo"/>
    </logger>

    <logger name="org.apache.kafka" level="INFO" additivity="false">
        <appender-ref ref="KafkaAppender"/>
    </logger>

    <root level="INFO">
        <appender-ref ref="ConsoleAppender"/>
    </root>

</configuration>
