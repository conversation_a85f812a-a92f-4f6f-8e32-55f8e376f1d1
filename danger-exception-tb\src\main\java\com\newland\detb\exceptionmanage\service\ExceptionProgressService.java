package com.newland.detb.exceptionmanage.service;

import com.newland.detb.exceptionmanage.entity.ExceptionProgress;
import com.newland.detb.exceptionmanage.entity.ProgressDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【HE_EXCEPTION_PROGRESS(异常治理进度表)】的数据库操作Service
* @createDate 2023-04-20 11:19:09
*/
public interface ExceptionProgressService {

    /**
     * 定时任务扫描工单表时，当扫描到异常后，需要同步初始化进度表，需要初始化创建6条数据
     * @param id 工单表数据的id
     * list 保存着每一个ProgressDTO实体类的对象，有两个属性，parentProgress和childProgress
     * @return
     */
    int insertInitData( Long id);

}
