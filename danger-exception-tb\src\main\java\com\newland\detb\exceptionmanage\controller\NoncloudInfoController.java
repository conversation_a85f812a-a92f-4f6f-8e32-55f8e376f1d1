package com.newland.detb.exceptionmanage.controller;

import cn.com.nlsoft.cas.client.annotation.CasPermissionRequired;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;

import com.newland.detb.common.ExcelListener;
import com.newland.detb.common.ResEnum;
import com.newland.detb.common.Result;

import com.newland.detb.exception.FileCheckException;
import com.newland.detb.exception.UpdateProInfoException;
import com.newland.detb.exceptionmanage.entity.ExceptionScenario;
import com.newland.detb.exceptionmanage.entity.NoncloudInfo;
import com.newland.detb.exceptionmanage.service.NoncloudInfoService;
import com.newland.detb.exceptionmanage.service.impl.NoncloudInfoExcelServiceImpl;
import com.newland.detb.log.annotation.SysLog;

import com.newland.detb.util.CheckFileSuffixUtil;
import com.newland.detb.util.ExcelUtil;
import com.newland.detb.util.TimeFormatUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.validator.constraints.Length;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;


import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;

import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Slf4j
@RestController
@RequestMapping(value = "/notcloud")
@Validated
public class NoncloudInfoController {

    @Resource
    private NoncloudInfoService noncloudInfoService;

    @Resource
    private NoncloudInfoExcelServiceImpl noncloudInfoExcelService;

    @GetMapping (value="findPage")
    @CasPermissionRequired("aimt.pro.query")
    public Object findPage(@RequestParam(value = "hostIp",defaultValue = "") @Length(min = 0,max = 15,message = "请求的主机IP长度不得超过15位") String hostIp,
                           @RequestParam(value = "processName",defaultValue = "") @Length(min = 0,max = 50,message = "请求的进程名长度不得超过20位") String processName,
                           @RequestParam(value = "pageNow",defaultValue = "1") int pageNow,
                           @RequestParam(value = "pageSize",defaultValue = "10") int pageSize) {
        log.info("接收到的生产资料参数信息：{}，{}，{}，{}",pageNow,pageSize,hostIp,processName);
        PageHelper.startPage(pageNow,pageSize);
        List<NoncloudInfo> page =  noncloudInfoService.findPage(hostIp,processName);
        PageInfo<NoncloudInfo> pageInfo=new PageInfo<>(page);
        // log.info("非云生产资料分页查询结果：{}",pageInfo);
        //List<String> createTimeList24 = page.stream().map(workOrders -> new SimpleDateFormat("HH:mm:ss").format(workOrders.getCreateTime())).collect(Collectors.toList());
        return Result.success(pageInfo);
    }

    /**
     * 手动更新生产资料信息
     * @return
     */
    @GetMapping (value="handleUpdate")
    @CasPermissionRequired("aimt.pro.change")
    public Result handleUpdateProInfo() {
        try {
            //调用更新方法
            noncloudInfoService.updateProInfo();
        } catch (UpdateProInfoException e) {
            e.printStackTrace();
            return Result.error(515,"应用部署信息手动更新失败");
        }
        //调用更新方法
        // noncloudInfoService.updateProInfo();
        return Result.success("应用部署信息手动更新成功");
    }

    @SysLog("删除生产资料信息")
    @GetMapping (value="delNotCloud")
    @CasPermissionRequired("aimt.pro.change")
    public Result delNotCloud(@RequestParam(value = "id",defaultValue = "") @Min(value = 0,message = "ID只能大于0") @Max(value = Long.MAX_VALUE,message = "ID值不得超过最大值") Long id) {
        log.info("接收到删除参数信息：{}",id);
        int i = noncloudInfoService.deleteNonCloudInfoById(id);
        return Result.success(i);
    }

    @SysLog("更新生产资料信息")
    @PostMapping (value="editNotCloud")
    @CasPermissionRequired("aimt.pro.change")
    public Result editNotCloud(@RequestBody @Validated(NoncloudInfo.Update.class) NoncloudInfo noncloudInfo) {
        log.info("接收到的编辑参数信息：{}",noncloudInfo.toString());
        int i = noncloudInfoService.updateNonCloudInfoById(noncloudInfo);
        return Result.success(i);
    }

    @SysLog("添加生产资料信息")
    @PostMapping (value="addNotCloud")
    @CasPermissionRequired("aimt.pro.change")
    public Result addNotCloud(@RequestBody @Validated(NoncloudInfo.Save.class) NoncloudInfo noncloudInfo) {
        log.info("接收到的添加参数信息：{}",noncloudInfo.toString());
        int i = noncloudInfoService.insert(noncloudInfo);
        return Result.success(i);
    }

    /**
     * 导出异常场景库信息
     * @param response
     */
    @SysLog("导出生产资料数据信息")
    @GetMapping("export")
    @CasPermissionRequired("aimt.pro.query")
    public void exportNotCloud(HttpServletResponse response, @RequestParam List<Long> exportList) {
        System.out.println("exportList:" + exportList);
        List<NoncloudInfo> noCloudList;
        if (exportList.size() == 0) {
            noCloudList = noncloudInfoService.selectAllNonCloudInfo();
        }else {
            noCloudList = noncloudInfoService.selectNonCloudInfoByIds(exportList);
        }
        if (Optional.ofNullable(noCloudList).isPresent()) {
            log.info("开始导出非云数据，共计条数：{}",noCloudList.size());
            String date = TimeFormatUtil.getDate(new Date());
            ExcelUtil.exportDataToExcel(response,"导出生产资料数据信息","生产资料数据信息_"+date,"生产资料数据明细",NoncloudInfo.class,noCloudList);
        }else {
            log.info("非云数据信息为空不需要导出");
        }
    }


    /**
     * 导入操作
     * @param file
     * @return
     */
    @SysLog("导入生产资料数据信息")
    @PostMapping("import")
    @CasPermissionRequired("aimt.pro.change")
    public Result importExceptionScenario(MultipartFile file) {
        //校验文件格式
        boolean excelFile = false;
        try {
            excelFile = CheckFileSuffixUtil.isExcelFile(file.getInputStream());
            //避免出现，文件是excel文件，但是后缀不是xls或xlsx后缀
            if (!Objects.requireNonNull(file.getOriginalFilename()).endsWith(".xls") && !file.getOriginalFilename().endsWith(".xlsx")) {
                excelFile = false;
            }
        } catch (IOException e) {
            e.printStackTrace();
            throw new FileCheckException(ResEnum.file_check_error_code,"校验文件后缀获取文件流失败");
        }
        if (excelFile) {
            if (Optional.ofNullable(file).isPresent()) {
                log.info("开始导入生产资料数据信息，文件大小：{}KB", file.getSize() / 1024);
                ExcelUtil.importData(file,NoncloudInfo.class,"生产资料数据信息导入",new ExcelListener<>(noncloudInfoExcelService));
                log.info("非云资料数据导入完成：{}",file.getOriginalFilename());
                return Result.success("非云资料数据信息导入成功");
            }else {
                log.info("上传的生产资料数据信息为空");
                return Result.error(ResEnum.scenario_error_code,"生产资料数据导入失败");
            }
        }else {
            return Result.error(ResEnum.scenario_error_code,"只能上传文件格式为.xls或.xlsx");
        }
    }
}
