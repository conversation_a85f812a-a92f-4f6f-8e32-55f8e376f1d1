package com.newland.detb.troubleshooting.mapper;

import com.newland.detb.troubleshooting.entity.SingleInspectionBean;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface SingleInspectionMapper {

    //单项巡检
    List<SingleInspectionBean> querySingleInspection();

    /**
     * 分页查询用户
     * @return
     */
    List<SingleInspectionBean> selectPage(String kpi_template_id);
}
