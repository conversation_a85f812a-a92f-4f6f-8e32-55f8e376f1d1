package com.newland.detb.exceptionmanage.controller;

import cn.com.nlsoft.cas.client.annotation.CasPermissionRequired;
import com.newland.detb.common.Result;
import com.newland.detb.exceptionmanage.entity.EchartsData;
import com.newland.detb.exceptionmanage.service.ExceptionEchartsService;
import com.newland.detb.log.annotation.SysLog;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023-06-02 002 15:25:41
 * @description Echarts图形化展示
 */
@Slf4j
@RestController
@RequestMapping(value = "/echarts")
public class ExceptionEchartsController {

    @Resource
    private ExceptionEchartsService exceptionEchartsService;

    private static List<String> checkList;

    /**
     * 概述
     * @return
     */
    @CasPermissionRequired("aimt.echarts.query")
    @GetMapping("summaryOverview")
    public Result getAlarmLevelData() {
        String summaryOverview = exceptionEchartsService.summaryOverview();
        return Result.success(summaryOverview);
    }

    /**
     * 最近30天每天的治理次数
     * 折线图
     * @return
     */
    @GetMapping("getEveryDayCount")
    @CasPermissionRequired("aimt.echarts.query")
    public Result getDataInMonth() {
        List<EchartsData> dataList = exceptionEchartsService.getDataByDay();
        return Result.success(dataList);
    }

    /**
     * 近七天每天的业务异常单治理次数
     * 条形图
     * @return
     */
    @GetMapping("getBusinessExceptionCount")
    @CasPermissionRequired("aimt.echarts.query")
    public Result getBusinessExceptionCount() {
        List<EchartsData> dataList = exceptionEchartsService.getBusinessExceptionCount();
        return Result.success(dataList);
    }

    /**
     * 近七天对业务异常单的治理分组情况
     * 条形图
     * @return
     */
    @GetMapping("getBusinessExceptionGroup")
    @CasPermissionRequired("aimt.echarts.query")
    public Result getBusinessExceptionGroup() {
        List<EchartsData> dataList = exceptionEchartsService.getBusinessExceptionGroup();
        return Result.success(dataList);
    }

    /**
     * 根据异常场景库名称分组查询异常场景库个数
     * 饼状图
     * @return
     */
    @GetMapping("getSceneByScene")
    @CasPermissionRequired("aimt.echarts.query")
    public Result getSceneGroup() {
        List<EchartsData> dataList = exceptionEchartsService.getSceneGroup();
        return Result.success(dataList);
    }

    /**
     * 查询当月治理前十的天数对应的次数
     * 折线图
     * @return
     */
    @GetMapping("getTopTen")
    @CasPermissionRequired("aimt.echarts.query")
    public Result getCountTopTen() {
        List<EchartsData> dataList = exceptionEchartsService.getCountTopTen();
        return Result.success(dataList);
    }

    /**
     * 查询治理结果分组的当天治理次数
     * 饼状图
     * @return
     */
    @GetMapping("getCountByResInDay")
    @CasPermissionRequired("aimt.echarts.query")
    public Result getGovernanceResGroupInDay() {
        List<EchartsData> dataList = exceptionEchartsService.getGovernanceResGroupInDay();
        return Result.success(dataList);
    }

    /**
     * 查询治理结果分组的当月治理次数
     * 饼状图
     * @return
     */
    @GetMapping("getCountByResInMonth")
    @CasPermissionRequired("aimt.echarts.query")
    public Result getGovernanceResGroupInMonth() {
        List<EchartsData> dataList = exceptionEchartsService.getGovernanceResGroupInMonth();
        return Result.success(dataList);
    }

    /**
     * 查询治理结果分组的全部治理次数
     * 饼状图
     * @return
     */
    @GetMapping("getCountByResInAll")
    @CasPermissionRequired("aimt.echarts.query")
    public Result getGovernanceResGroupInAll() {
        List<EchartsData> dataList = exceptionEchartsService.getGovernanceResGroupInAll();
        return Result.success(dataList);
    }

    /**
     * 准备异常治理月报数据
     * @return
     */
    @PostMapping("exportData")
    @CasPermissionRequired("aimt.echarts.query")
    public Result prepareData(@RequestBody List<String> dataList) {
        checkList = dataList;
        return Result.success();
    }

    /**
     * 导出异常治理月报数据
     * @return
     */
    @SysLog("导出异常治理月报信息")
    @CasPermissionRequired("aimt.echarts.export")
    @GetMapping("export")
    public void exportData(HttpServletResponse response) {
        exceptionEchartsService.exportData(response,checkList);
        checkList.clear();
    }

    @GetMapping("exceptionData")
    @CasPermissionRequired("aimt.echarts.query")
    public Result getExceptionDataInTime(@RequestParam("type") String type, @RequestParam("time") String time) {
        Integer timeValue = null;
        if (time != null) {
            switch (time) {
                case "week":
                    timeValue = 7;
                    break;
                case "month":
                    timeValue = 30;
                    break;
                case "day":
                    timeValue = 0;
                    break;
                default:
                    break;
            }
        }
        List<EchartsData> exceptionDataCountInTimeRangeList = exceptionEchartsService.getExceptionDataCountInTimeRange(type, timeValue);
        return Result.success(exceptionDataCountInTimeRangeList);
    }

    @GetMapping("workOrderData")
    @CasPermissionRequired("aimt.echarts.query")
    public Result getWorkOrderDataInTime(@RequestParam("type") String type, @RequestParam("time") String time) {
        Integer timeValue = null;
        if (time != null) {
            switch (time) {
                case "week":
                    timeValue = 7;
                    break;
                case "month":
                    timeValue = 30;
                    break;
                case "day":
                    timeValue = 0;
                    break;
                default:
                    break;
            }
        }
        List<EchartsData> exceptionDataCountInTimeRangeList = exceptionEchartsService.getWorkOrderCountInTimeRange(type, timeValue);
        return Result.success(exceptionDataCountInTimeRangeList);
    }

}
