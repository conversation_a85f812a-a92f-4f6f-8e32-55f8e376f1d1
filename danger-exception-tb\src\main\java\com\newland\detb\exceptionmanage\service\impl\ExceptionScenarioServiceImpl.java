package com.newland.detb.exceptionmanage.service.impl;

import com.newland.detb.common.ResEnum;
import com.newland.detb.common.Result;
import com.newland.detb.exception.ExceptionScenarioException;
import com.newland.detb.exceptionmanage.entity.ExceptionScenario;
import com.newland.detb.exceptionmanage.entity.GovernanceStrategy;
import com.newland.detb.exceptionmanage.entity.SceneInspection;
import com.newland.detb.exceptionmanage.mapper.GovernanceStrategyMapper;
import com.newland.detb.exceptionmanage.mapper.SceneInspectionMapper;
import com.newland.detb.exceptionmanage.service.ExceptionScenarioService;
import com.newland.detb.exceptionmanage.mapper.ExceptionScenarioMapper;
import com.newland.detb.util.mapper.ExcelBaseService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【HE_EXCEPTION_SCENARIO(异常场景库信息表)】的数据库操作Service实现
* @createDate 2023-04-18 14:56:30
*/
@Slf4j
@Service
public class ExceptionScenarioServiceImpl implements ExceptionScenarioService {

    @Resource
    private ExceptionScenarioMapper exceptionScenarioMapper;

    @Resource
    private GovernanceStrategyMapper governanceStrategyMapper;

    @Resource
    private SceneInspectionMapper sceneInspectionMapper;

    /**
     * 保存或者更新异常场景库信息
     *
     * @param exceptionScenario
     * @return
     */
    @Transactional
    @Override
    public Result saveOrUpdate(ExceptionScenario exceptionScenario) {
        //判断是保存还是更新操作
        if (exceptionScenario.getId() == null) {
            //异常编码自动生成，无需确认；在保存之前，去确认异常场景库中是否已经存在对应的异常场景编码
            // ExceptionScenario scenario = exceptionScenarioService.selectBySceneCode(exceptionScenario.getSceneCode());
            int res = exceptionScenarioMapper.insert(exceptionScenario);
            if (res < 0) {
                log.error("异常场景库保存失败！");
                throw new ExceptionScenarioException(ResEnum.scenario_error_code,"异常场景库录入失败");
            }else {
                //同步保存异常治理策略信息
                GovernanceStrategy strategy = new GovernanceStrategy();
                strategy.setSceneCode(exceptionScenario.getSceneCode().toString());
                strategy.setStrategyCode(exceptionScenario.getSceneCode().toString());
                strategy.setRemark(exceptionScenario.getStrategy());
                int strategyRes = governanceStrategyMapper.insert(strategy);
                if (strategyRes < 0) {
                    log.error("在保存异常治理策略时发生异常，当前数据：{}",strategy);
                    throw new ExceptionScenarioException(ResEnum.scenario_error_code,"同步保存异常治理策略时异常");
                }
                //继续同步保存在异常场景和单项巡检关系映射表中（如果需要触发单项巡检时，才需要在关联表中保存对应数据）0：需要，1：不需要
                if (exceptionScenario.getIsInspection() == 0) {
                    SceneInspection sceneInspection = new SceneInspection();
                    sceneInspection.setSceneCode(exceptionScenario.getSceneCode());
                    sceneInspection.setSceneName(exceptionScenario.getType());
                    sceneInspection.setInspectionCode(exceptionScenario.getSceneCode());
                    int saveResult = sceneInspectionMapper.insert(sceneInspection);
                    if (saveResult < 1) {
                        log.error("异常场景和单项巡检关联表保存失败");
                    }
                }
                return Result.success("异常场景库信息录入成功");
            }
        }else {
            int res = exceptionScenarioMapper.update(exceptionScenario);
            if (res < 0) {
                log.error("异常场景库更新失败！");
                throw new ExceptionScenarioException(ResEnum.scenario_error_code,"异常场景库更新失败");
            }else {
                return Result.success("异常场景库信息更新成功");
            }
        }
    }

    /**
     * 保存数据
     *
     * @param exceptionScenario
     * @return
     */
    @Override
    public int insert(ExceptionScenario exceptionScenario) {
        return exceptionScenarioMapper.insert(exceptionScenario);
    }

    /**
     * 根据id查询
     *
     * @param id
     * @return
     */
    @Override
    public ExceptionScenario findById(Long id) {
        return exceptionScenarioMapper.findById(id);
    }

    /**
     * 倒序查询全部数据
     *
     * @return
     */
    @Override
    public List<ExceptionScenario> findAll() {
        return exceptionScenarioMapper.findAll();
    }

    /**
     * 条件查询
     *
     * @param env
     * @param type
     * @return
     */
    @Override
    public List<ExceptionScenario> findByCondition(String type, Integer env, String subClass, Integer isAuth,Integer isInspection) {
        return exceptionScenarioMapper.findByCondition(type, env, subClass,isAuth,isInspection);
    }

    /**
     * 更新
     *
     * @param exceptionScenario
     * @return
     */
    @Override
    public int update(ExceptionScenario exceptionScenario) {
        //不更新异常编码
        return exceptionScenarioMapper.update(exceptionScenario);
    }

    /**
     * 根据id删除
     *
     * @param id
     * @return
     */
    @Transactional
    @Override
    public int delete(Long id) {
        //需要同步删除治理策略中的信息
        ExceptionScenario scenario = exceptionScenarioMapper.findById(id);
        if (scenario != null) {
            //如果需要触发单项巡检操作，则需要同步删除异常场景和单项巡检关联表中的数据
            if (scenario.getIsInspection() == 0) {
                int res = sceneInspectionMapper.deleteBySceneCode(scenario.getSceneCode());
                if (res < 1) {
                    log.error("删除异常场景和单项巡检关联表中的数据失败");
                }
            }
            int res = governanceStrategyMapper.delBySceneCode(scenario.getSceneCode().toString());
            if (res < 1) {
                log.error("删除异常场景时，同步删除治理策略失败");
            }
        }
        return exceptionScenarioMapper.delete(id);
    }

    /**
     * 根据异常场景编码查询异常场景库
     *
     * @param sceneCode
     * @return
     */
    @Override
    public ExceptionScenario selectBySceneCode(int sceneCode) {
        return exceptionScenarioMapper.selectBySceneCode(sceneCode);
    }

    /**
     * 根据id查询异常场景库信息
     *
     * @param exportList
     * @return
     */
    @Override
    public List<ExceptionScenario> findByIds(List<Long> exportList) {
        return exceptionScenarioMapper.findByIds(exportList);
    }

    /**
     * 获取最大的异常场景库编码号
     *
     * @return
     */
    @Override
    public Integer getMaxSceneCode() {
        return exceptionScenarioMapper.getMaxSceneCode();
    }

    /**
     * 根据id修改是否授权状态
     *
     * @param id
     * @param isAuth
     * @return
     */
    @Override
    public int updateIsAuthById(Long id, int isAuth) {
        return exceptionScenarioMapper.updateIsAuthById(id, isAuth);
    }

    /**
     * 根据id修改是否需要触发单项巡检
     *
     * @param id
     * @param isInspection
     * @return
     */
    @Override
    public int updateIsInspectionById(Long id, int isInspection) {
        return exceptionScenarioMapper.updateIsInspectionById(id, isInspection);
    }

    /**
     * 根据id修改是否已经覆盖
     *
     * @param id
     * @param isOverride
     * @return
     */
    @Override
    public int updateIsOverrideById(Long id, int isOverride) {
        return exceptionScenarioMapper.updateIsOverrideById(id, isOverride);
    }


}
