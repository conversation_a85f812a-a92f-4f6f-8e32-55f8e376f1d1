package com.newland.detb.troubleshooting.kafka;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;
import java.util.concurrent.CopyOnWriteArrayList;

/**
 * <AUTHOR>
 * @date 2023-03-17 017 16:36:34
 * @description
 */
@Slf4j
@Component
public class ScheduleTestKafka {

    @Autowired
    private KafkaTemplate<String,String> kafkaOneTemplate;

    // @Scheduled(cron = "0/15 * * * * ?")
    public void sendMsg() {
        List<String> list = new CopyOnWriteArrayList<>();
        list.add("hello");
        list.add("world");
        list.add(new Date().toString());
        String jsonString = JSON.toJSONString(list);
        log.info("向kafka中发送一条消息：{}",jsonString);
        kafkaOneTemplate.send("test-demo",jsonString);
    }
}
