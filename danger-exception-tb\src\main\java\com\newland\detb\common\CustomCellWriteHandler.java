package com.newland.detb.common;

import com.alibaba.excel.metadata.CellData;
import com.alibaba.excel.metadata.Head;
import com.alibaba.excel.write.handler.CellWriteHandler;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.WriteTable;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.metadata.holder.WriteTableHolder;
import org.apache.poi.hssf.usermodel.HSSFDataFormat;
import org.apache.poi.ss.usermodel.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-01-05 005 21:00:48
 * @description 导出业务异常单周报进行占比计算
 */
public class CustomCellWriteHandler implements CellWriteHandler {
    @Override
    public void beforeCellCreate(WriteSheetHolder writeSheetHolder, WriteTableHolder writeTableHolder, Row row, Head head, Integer integer, Integer integer1, Boolean aBoolean) {

    }

    @Override
    public void afterCellCreate(WriteSheetHolder writeSheetHolder, WriteTableHolder writeTableHolder, Cell cell, Head head, Integer integer, Boolean aBoolean) {

    }

    @Override
    public void afterCellDataConverted(WriteSheetHolder writeSheetHolder, WriteTableHolder writeTableHolder, CellData cellData, Cell cell, Head head, Integer integer, Boolean aBoolean) {

    }

    @Override
    public void afterCellDispose(WriteSheetHolder writeSheetHolder, WriteTableHolder writeTableHolder, List<CellData> list, Cell cell, Head head, Integer integer, Boolean aBoolean) {
        // System.out.println("进入第" + cell.getRowIndex() + "行,第" + cell.getColumnIndex() + "列数据...");
        if (cell.getRowIndex() != 0 && cell.getColumnIndex() == 7) {
            //公式为：=G*/SUM(G,G)
            int actualCellRowNum = cell.getRowIndex() + 1;
            cell.setCellFormula("=G" + actualCellRowNum +"/SUM(G:G)");

            // 获取工作簿对象
            Workbook workbook = writeSheetHolder.getSheet().getWorkbook();
            // 创建百分比格式
            CellStyle percentageStyle = workbook.createCellStyle();
            percentageStyle.setDataFormat(workbook.createDataFormat().getFormat("0.00%"));
            // 设置单元格样式
            cell.setCellStyle(percentageStyle);

            System.out.println("第" +  cell.getRowIndex() + "行,第" + cell.getColumnIndex() + "占比写入公式完成");
        }
    }
}
