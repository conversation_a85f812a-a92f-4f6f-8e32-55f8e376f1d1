<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.newland.detb.exceptionmanage.mapper.ExceptionEchartsMapper">


    <select id="summaryOverview" resultType="java.lang.String">
        SELECT
            CONCAT('当月共计治理：', an, ' 次异常告警；其中，治理完成：', bn, ' 个；等待授权：', cn,
                   '个；拒绝授权：', en, ' 个；未匹配到异常场景定义，需手动处理：', dn, ' 个。当月共计治理业务异常单：', fn, '个。') AS 描述信息
        FROM (
                 SELECT COUNT(1) AS an
                 FROM he_work_order a
                 WHERE DATE_FORMAT(a.create_time, '%Y-%m') = DATE_FORMAT(CURDATE(), '%Y-%m')
             ) a,
             (
                 SELECT COUNT(1) AS bn
                 FROM he_work_order b
                 WHERE b.governance_res = '治理完成'
                   AND DATE_FORMAT(b.create_time, '%Y-%m') = DATE_FORMAT(CURDATE(), '%Y-%m')
             ) b,
             (
                 SELECT COUNT(1) AS cn
                 FROM he_work_order b
                 WHERE b.governance_res = '正在等待授权完成'
                   AND DATE_FORMAT(b.create_time, '%Y-%m') = DATE_FORMAT(CURDATE(), '%Y-%m')
             ) c,
             (
                 SELECT COUNT(1) AS dn
                 FROM he_work_order b
                 WHERE b.governance_res LIKE '未匹配到%'
                   AND DATE_FORMAT(b.create_time, '%Y-%m') = DATE_FORMAT(CURDATE(), '%Y-%m')
             ) d,
             (
                 SELECT COUNT(1) AS en
                 FROM he_work_order b
                 WHERE b.governance_res LIKE '拒绝授权%'
                   AND DATE_FORMAT(b.create_time, '%Y-%m') = DATE_FORMAT(CURDATE(), '%Y-%m')
             ) e,
             (
                 SELECT COUNT(1) AS fn
                 FROM he_work_order a
                 WHERE a.strategy_remark = '执行异常单恢复处理'
                   AND DATE_FORMAT(a.create_time, '%Y%m') = DATE_FORMAT(CURDATE(), '%Y%m')
             ) f
    </select>
    <select id="getDataByDay" resultType="com.newland.detb.exceptionmanage.entity.EchartsData">
        SELECT
            DATE_FORMAT(a.create_time, '%Y-%m-%d') AS name,
            COUNT(1) AS value
        FROM
            he_work_order a
        WHERE
            DATE_FORMAT(a.create_time, '%Y-%m-%d') IN (
            SELECT
            DATE_FORMAT(a.create_time, '%Y-%m-%d')
            FROM
            he_work_order a
            WHERE
            a.create_time BETWEEN
            DATE_SUB(CURDATE(), INTERVAL 30 DAY) AND
            DATE_ADD(CURDATE(), INTERVAL 1 DAY)
            GROUP BY
            DATE_FORMAT(a.create_time, '%Y-%m-%d')
            )
        GROUP BY
            DATE_FORMAT(a.create_time, '%Y-%m-%d')
        ORDER BY
            DATE_FORMAT(a.create_time, '%Y-%m-%d')
    </select>
    <select id="getBusinessExceptionCount" resultType="com.newland.detb.exceptionmanage.entity.EchartsData">
        SELECT
            DATE_FORMAT(a.create_time, '%Y%m%d') AS name,
            COUNT(1) AS value
        FROM
            he_work_order a
        WHERE
            a.strategy_remark = '执行异常单恢复处理'
          AND a.create_time BETWEEN
            DATE_SUB(CURDATE(), INTERVAL 7 DAY) AND
            DATE_ADD(CURDATE(), INTERVAL 1 DAY)
        GROUP BY
            DATE_FORMAT(a.create_time, '%Y%m%d')
        ORDER BY
            DATE_FORMAT(a.create_time, '%Y%m%d')
    </select>
    <select id="getBusinessExceptionGroup" resultType="com.newland.detb.exceptionmanage.entity.EchartsData">
        SELECT
            a.process_name AS name,
            COUNT(1) AS value
        FROM
            he_excepion_data a
        WHERE
            a.alarm_type = 'BUSINESS'
          AND a.create_time BETWEEN
            DATE_SUB(CURDATE(), INTERVAL 7 DAY) AND
            DATE_ADD(CURDATE(), INTERVAL 1 DAY)
          AND a.status != 0
        GROUP BY
            a.process_name
    </select>
    <select id="getSceneGroup" resultType="com.newland.detb.exceptionmanage.entity.EchartsData">
        select a.type as name,count(1) as value from he_exception_scenario a group by a.type
    </select>
    <select id="getCountTopTen" resultType="com.newland.detb.exceptionmanage.entity.EchartsData">
        SELECT
            DATE_FORMAT(w.create_time, '%Y-%m-%d') AS name,
            COUNT(1) AS value
        FROM
            HE_WORK_ORDER w
        WHERE
            DATE_FORMAT(w.create_time, '%Y-%m') = DATE_FORMAT(CURDATE(), '%Y-%m')
        GROUP BY
            DATE_FORMAT(w.create_time, '%Y-%m-%d')
        ORDER BY
            value DESC
            LIMIT 10
    </select>
    <select id="getGovernanceResGroupInDay" resultType="com.newland.detb.exceptionmanage.entity.EchartsData">
        SELECT
            a.governance_res AS name,
            COUNT(1) AS value
        FROM
            he_work_order a
        WHERE
            DATE_FORMAT(a.create_time, '%Y-%m-%d') = DATE_FORMAT(CURDATE(), '%Y-%m-%d')
        GROUP BY
            a.governance_res
    </select>
    <select id="getGovernanceResGroupInMonth" resultType="com.newland.detb.exceptionmanage.entity.EchartsData">
        SELECT
            a.governance_res AS name,
            COUNT(1) AS value
        FROM
            he_work_order a
        WHERE
            DATE_FORMAT(a.create_time, '%Y-%m') = DATE_FORMAT(CURDATE(), '%Y-%m')
        GROUP BY
            a.governance_res
    </select>
    <select id="getGovernanceResGroupInAll" resultType="com.newland.detb.exceptionmanage.entity.EchartsData">
        SELECT
            c.governance_res AS name,
            COUNT(1) AS value
        FROM (
            SELECT a.governance_res
            FROM he_work_order a
            UNION ALL
            SELECT b.governance_res
            FROM he_work_order_history b
            ) c
        GROUP BY c.governance_res
    </select>
    <select id="getExceptionDataCountInTimeRange"
            resultType="com.newland.detb.exceptionmanage.entity.EchartsData">
        SELECT
        a.alarm_type AS name,
        COUNT(1) AS value
        FROM
        he_excepion_data a
        <where>
            <if test="type != null and type == 'system'">
                a.alarm_type != 'BUSINESS'
            </if>
            <if test="type != null and type == 'business'">
                AND a.alarm_type = 'BUSINESS'
            </if>
            <if test="time != null and time == 7">
                AND a.create_time BETWEEN DATE_SUB(CURDATE(), INTERVAL #{time} DAY) AND CURDATE()
            </if>
            <if test="time != null and time == 30">
                AND a.create_time BETWEEN DATE_SUB(CURDATE(), INTERVAL #{time} DAY) AND CURDATE()
            </if>
            <if test="time != null and time == 0">
                AND DATE_FORMAT(a.create_time, '%Y%m%d') = DATE_FORMAT(CURDATE(), '%Y%m%d')
            </if>
        </where>
        GROUP BY
        a.alarm_type
    </select>
    <select id="getWorkOrderCountInTimeRange" resultType="com.newland.detb.exceptionmanage.entity.EchartsData">
        SELECT
        a.governance_res AS name,
        COUNT(1) AS value
        FROM
        he_work_order a
        <where>
            <if test="type != null and type == 'system'">
                a.strategy_remark != '执行异常单恢复处理'
            </if>
            <if test="type != null and type == 'business'">
                a.strategy_remark = '执行异常单恢复处理'
            </if>
            <if test="time != null and time == 7">
                AND a.create_time BETWEEN DATE_SUB(CURDATE(), INTERVAL #{time} DAY) AND CURDATE()
            </if>
            <if test="time != null and time == 30">
                AND a.create_time BETWEEN DATE_SUB(CURDATE(), INTERVAL #{time} DAY) AND CURDATE()
            </if>
            <if test="time != null and time == 0">
                AND DATE_FORMAT(a.create_time, '%Y%m%d') = DATE_FORMAT(CURDATE(), '%Y%m%d')
            </if>
        </where>
        GROUP BY
        a.governance_res
    </select>
</mapper>