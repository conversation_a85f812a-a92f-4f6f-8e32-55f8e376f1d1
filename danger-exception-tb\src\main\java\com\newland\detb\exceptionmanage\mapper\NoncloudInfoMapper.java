package com.newland.detb.exceptionmanage.mapper;

import com.newland.detb.exceptionmanage.entity.NoncloudInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【HE_NONCLOUD_INFO(非云生产资料信息表)】的数据库操作Mapper
* @createDate 2023-05-04 15:56:12
* @Entity com.newland.detb.exceptionmanage.entity.NoncloudInfo
*/
@Mapper
public interface NoncloudInfoMapper {

    /**
     * 保存非云生产资料信息
     * @param noncloudInfo
     * @return
     */
    int insert(NoncloudInfo noncloudInfo);

    /**
     * 批量插入生产资料数据
     * @param noncloudInfoList
     * @return
     */
    int insertBatch(List<NoncloudInfo> noncloudInfoList);

    /**
     * 根据id查询非云生产资料信息
     * @param id
     * @return
     */
    NoncloudInfo selectNonCloudInfoById(Long id);

    /**
     * 查询全部的非云生产资料信息
     * @return
     */
    List<NoncloudInfo> selectAllNonCloudInfo();

    /**
     * 根据id删除非云生产资料信息
     * @param id
     * @return
     */
    int deleteNonCloudInfoById(Long id);

    /**
     * 更新非云生产资料信息
     * @param noncloudInfo
     * @return
     */
    int updateNonCloudInfoById(NoncloudInfo noncloudInfo);

    /**
     * 根据条件查询是否对应的异常告警的恢复策略
     * @param processName
     * @param hostIp
     * @param hostName
     * @return
     */
    List<NoncloudInfo> selectMatchByCondition(@Param("processName") String processName, @Param("hostIp") String hostIp, @Param("hostName") String hostName);

    List<NoncloudInfo> findPage(@Param("hostIp") String hostIp, @Param("processName") String processName);

    List<NoncloudInfo> selectNonCloudInfoByIds(@Param("exportList") List<Long> exportList);

    NoncloudInfo qryNoncloudInfo(String hostIp, String hostName, String processName, String backupIp);

    /**
     * 从rmc.mt_eboss_bushuinfo中获取最新生产资料信息
     * @return
     */
    List<NoncloudInfo> getProInfoFromView();

    /**
     * 清空生产资料表
     * @return
     */
    int clearNonCloudInfo();

    /**
     * 通过进程名搜索日志关键字对应的恢复脚本type参数
     * @param processName
     * @param envSuffix 后缀带-，如：云-
     * @param envPrefix 前缀带-，如：-云
     * @return
     */
    List<String> getTypeNameByProcessName(@Param("processName") String processName,@Param("envSuffix") String envSuffix,@Param("envPrefix") String envPrefix);
}




