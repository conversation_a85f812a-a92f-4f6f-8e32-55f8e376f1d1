package com.newland.detb.exception;

import com.newland.detb.common.ResEnum;
import com.newland.detb.common.Result;
import com.sun.org.apache.bcel.internal.generic.NEW;
import lombok.extern.slf4j.Slf4j;
import org.apache.tomcat.util.http.fileupload.impl.FileSizeLimitExceededException;
import org.springframework.validation.BindingResult;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;

import javax.validation.ConstraintViolationException;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-02-22 022 18:03:40
 * @description 全局异常处理
 */
@Slf4j
@RestControllerAdvice
public class GlobalExceptionHandler {

    // @ExceptionHandler(RuntimeException.class)
    // public Result runException(RuntimeException e) {
    //     return Result.error(ResEnum.error_code,e.getMessage());
    // }

    /**
     * 远程连接获取服务器标志位文件信息异常-510
     * @param e
     * @return
     */
    @ExceptionHandler(JschException.class)
    public Result jschException(JschException e) {
        return Result.error(ResEnum.jsch_error_code,e.getMessage());
    }

    /**
     * EasyExcel导入导出异常-511
     * @param e
     * @return
     */
    @ExceptionHandler(EasyExcelException.class)
    public Result easyExcelException(EasyExcelException e) {
        return Result.error(ResEnum.excel_error_code,e.getMessage());
    }

    /**
     * 异常场景库操作异常-512
     * @param e
     * @return
     */
    @ExceptionHandler(ExceptionScenarioException.class)
    public Result exceptionScenarioException(ExceptionScenarioException e) {
        return  Result.error(ResEnum.scenario_error_code,e.getMessage());
    }

    /**
     * 异常治理工单表操作异常-513
     * @param e
     * @return
     */
    @ExceptionHandler(ExceptionDataException.class)
    public Result exceptionDataException(ExceptionDataException e) {
        return  Result.error(ResEnum.exception_data_error,e.getMessage());
    }

    /**
     * 定时任务异常-515
     * @param e
     * @return
     */
    @ExceptionHandler(QuartzException.class)
    public Result quartException(QuartzException e) {
        return  Result.error(ResEnum.quartz_error,e.getMessage());
    }

    /**
     * 数据校验异常-方法参数无效异常
     * @param e
     * @return
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public Result methodArgumentNotValidException(MethodArgumentNotValidException e) {
        BindingResult bindingResult = e.getBindingResult();
        List<FieldError> fieldErrors = bindingResult.getFieldErrors();
        fieldErrors.forEach(System.out::println);
        StringBuilder sb = new StringBuilder("校验失败：");
        for (FieldError fieldError : bindingResult.getFieldErrors()) {
            sb.append(fieldError.getDefaultMessage()).append("\n");
        }
        String msg = sb.toString();
        return Result.error(ResEnum.check_error_code,msg);
    }

    /**
     * 数据校验异常-违反约束异常
     * @param e
     * @return
     */
    @ExceptionHandler(ConstraintViolationException.class)
    public Result constraintViolationException(ConstraintViolationException e) {
        log.info("ConstraintViolationException异常：{}",e.getMessage());
        String[] split = e.getMessage().split(":");
        return Result.error(ResEnum.check_error_code,split.length > 1 ? split[1] : split[0]);
    }

    /**
     * 前端请求后端的参数类型异常。参数类型不匹配
     * @param e
     * @return
     */
    @ExceptionHandler(MethodArgumentTypeMismatchException.class)
    public Result methodArgumentTypeMismatchException(MethodArgumentTypeMismatchException e) {
        return Result.error(ResEnum.argument_error_code,"请求的参数类型异常");
    }

    /**
     * 文件格式校验异常
     * @param e
     * @return
     */
    @ExceptionHandler(FileCheckException.class)
    public Result fileCheckException(FileCheckException e) {
        return Result.error(ResEnum.file_check_error_code,"文件格式校验失败");
    }

    @ExceptionHandler(FileSizeLimitExceededException.class)
    public Result fileSizeLimitExceededException(FileSizeLimitExceededException e) {
        return Result.error(ResEnum.file_size_error,"文件大小超出最大限制");
    }

    /**
     * 原子操作信息获取异常
     * @param e
     * @return
     */
    @ExceptionHandler(AtomicException.class)
    public Result atomicException(AtomicException e) {
        return Result.error(ResEnum.atomic_error,"原子信息获取异常");
    }

    /**
     * 认证失败
     * @param e
     * @return
     */
    @ExceptionHandler(ServiceException.class)
    public Result serviceException(ServiceException e) {
        return Result.error(ResEnum.service_error,"认证失败");
    }

}
