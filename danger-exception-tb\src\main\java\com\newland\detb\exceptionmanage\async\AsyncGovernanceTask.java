package com.newland.detb.exceptionmanage.async;

import com.newland.detb.common.entity.HeExceptionDataBean;
import com.newland.detb.exceptionmanage.entity.*;
import com.newland.detb.exceptionmanage.mapper.BusinessExceptionOrderMapper;
import com.newland.detb.exceptionmanage.mapper.NoncloudInfoMapper;
import com.newland.detb.exceptionmanage.service.DictInfoService;
import com.newland.detb.exceptionmanage.service.ScriptInfoService;
import com.newland.detb.exceptionmanage.service.WorkOrderService;
import com.newland.detb.exceptionmanage.util.ProcessUtil;
import com.newland.detb.lock.DatabaseLock;
import com.newland.detb.util.JschScriptUtil;
import com.newland.detb.util.JschUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @date 2023-05-11 011 14:34:14
 * @description 执行治理操作
 */
// @Lazy
@Slf4j
@Component
public class AsyncGovernanceTask extends BaseAsyncTask{

    @Resource
    private WorkOrderService workOrderService;

    @Resource
    private ScriptInfoService scriptInfoService;

    @Lazy
    @Resource
    private AsyncSingleInspectionTask asyncSingleInspectionTask;

    @Resource
    private DictInfoService dictInfoService;

    @Resource
    private BusinessExceptionOrderMapper businessExceptionOrderMapper;

    @Resource
    private NoncloudInfoMapper noncloudInfoMapper;

    @Resource
    private DatabaseLock databaseLock;

    /**
     * 执行治理操作
     * @param heExceptionDataBean 异常数据工单表
     * @param exceptionScenario 异常场景库信息
     * @param workOrder 治理工单
     */
    @Async("taskExecutor")
    @Override
    public void executeAsync(String traceId,HeExceptionDataBean heExceptionDataBean, ExceptionScenario exceptionScenario, WorkOrder workOrder) {
        asyncPrintLogInfo(traceId,"异常治理");
        updateHeExceptionDataWithRemark(heExceptionDataBean,3,"正在执行异常治理操作");
        updateProgress(heExceptionDataBean,3,3,"doing","正在执行异常治理操作");

        try {
            //调用脚本执行异常治理操作（传递必要参数：xx.sh 应用名 type 环境 机器）
            asyncPrintLogInfo(traceId,3,3,"调用脚本进行异常治理操作的参数，类型：" + exceptionScenario.getType() + "环境：" + heExceptionDataBean.getEnvironment() + "应用名：" + heExceptionDataBean.getHomeModule() + "机器名：" + (heExceptionDataBean.getHostName() == null ? "" : heExceptionDataBean.getHostName()));

            //调用脚本执行异常治理操作（传递必要参数：类型（type）、应用名、机器名）
            boolean flag ;
            // flag = jschScriptUtil.executeGovernScript(command);
            //改为本地执行
            // flag = ProcessUtil.executeScript(command);
            //获取恢复命令
            String recoveryCommand = workOrder.getRecoveryCommand();
            if (recoveryCommand != null && !"".equals(recoveryCommand)) {
                flag = getProcessUtil().executeRecoverScript(recoveryCommand,traceId);
                asyncPrintLogInfo(traceId,3,3,"治理结果获取成功：" + flag);
                //根据脚本调用的结果，更新进度表和异常数据工单表（调整异常治理成功/失败为治理完成）
                if (flag) {
                    updateHeExceptionDataWithRemark(heExceptionDataBean,3,"执行异常治理完成");
                    updateProgress(heExceptionDataBean,3,3,"success","异常治理操作完成");
                    workOrderService.updateGovernanceResInt("治理完成", workOrder.getId());
                    workOrder.setGovernanceRes("治理完成");
                }else {
                    updateHeExceptionDataWithRemark(heExceptionDataBean,3,"执行异常治理完成");
                    updateProgress(heExceptionDataBean,3,3,"success","异常治理操作完成");
                    workOrderService.updateGovernanceResInt("治理完成", workOrder.getId());
                    workOrder.setGovernanceRes("治理完成");
                }
            }else {
                asyncPrintErrorLogInfo(traceId,3,3,"异常治理执行时，从workOrder中获取到的命令为空");
                updateHeExceptionDataWithRemark(heExceptionDataBean,3,"执行异常治理完成");
                updateProgress(heExceptionDataBean,3,3,"success","异常治理操作完成");
                workOrderService.updateGovernanceResInt("治理完成", workOrder.getId());
                workOrder.setGovernanceRes("治理完成");
            }
            //治理完成后，判断是否需要触发单项巡检操作
            asyncSingleInspectionTask.executeAsync(traceId,heExceptionDataBean, exceptionScenario, workOrder);
        } catch (Exception e) {
            e.printStackTrace();
            asyncPrintErrorLogInfo(traceId,3,3,"异常治理执行时出现异常：" + e.getMessage());
            updateHeExceptionDataWithRemark(heExceptionDataBean,3,"执行异常治理完成");
            updateProgress(heExceptionDataBean,3,3,"success","异常治理操作完成");
        }
    }

    /**
     * 业务异常单-执行治理操作
     * @param heExceptionDataBean 异常数据工单表
     * @param workOrder 治理工单
     */
    @Async("taskExecutor")
    public void executeBusinessAsync(String traceId, HeExceptionDataBean heExceptionDataBean, WorkOrder workOrder, BusinessType businessType) {
        asyncPrintLogInfo(traceId,"业务异常单-异常治理");
        updateHeExceptionDataWithRemark(heExceptionDataBean,3,"业务异常单-正在执行异常治理操作");
        updateProgress(heExceptionDataBean,3,3,"doing","业务异常单-正在执行异常治理操作");

        if (businessType.getParentName().equals("反向工单") && businessType.getChildName().equals("【EBOSS云业务】反向工单失败量")) {
            //调用存储过程执行异常治理操作(传递参数：唯一id)
            asyncPrintLogInfo(traceId,3,3,"业务异常单-调用存储过程继续业务异常单恢复，环境：" + heExceptionDataBean.getEnvironment() + "应用名：" + heExceptionDataBean.getHomeModule() + "机器名：" + (heExceptionDataBean.getHostName() == null ? "" : heExceptionDataBean.getHostName()));
            Map<String,Object> paramsMap = new ConcurrentHashMap<>();
            paramsMap.put("p_warning_id",heExceptionDataBean.getDataId());
            businessExceptionOrderMapper.invokeRecovery(paramsMap);
            String execMsg = (String) paramsMap.get("p_exec_msg");
            // Map<String, Object> parametersMap = procedureResultCheck(execMsg);
            Map<String, Object> resultMap = procedureResultCheck(execMsg,traceId,heExceptionDataBean.getDataId());
            boolean checkResult = false;
            try {
                checkResult = (Boolean) resultMap.get("flag") && (Integer.parseInt((String) resultMap.get("count"))  > 0);
                asyncPrintLogInfo(traceId,1,2,"业务异常单-[异常治理模块]调用恢复存储过程[成功]，状态码：" + resultMap.get("flag") + ",恢复异常订单数：" + Integer.parseInt((String) resultMap.get("count")));
            } catch (NumberFormatException e) {
                e.printStackTrace();
                checkResult = false;
                asyncPrintErrorLogInfo(traceId,1,2,"业务异常单-[异常治理模块]调用恢复存储过程[失败]，状态码：" + resultMap.get("flag") + ",恢复异常订单数：" + Integer.parseInt((String) resultMap.get("count")) + ";异常原因：" + e.getMessage());
            }
            if (checkResult) {
                ///说明异常单恢复成功
                updateHeExceptionDataWithRemark(heExceptionDataBean,3,"业务异常单恢复完成");
                updateProgress(heExceptionDataBean,3,3,"success","业务异常单恢复完成");
                workOrderService.updateGovernanceResInt("治理完成", workOrder.getId());
                workOrder.setGovernanceRes("治理完成");
            }else {
                //说明异常单恢复数量为0
                updateHeExceptionDataWithRemark(heExceptionDataBean,3,"业务异常单恢复完成");
                updateProgress(heExceptionDataBean,3,3,"success","业务异常单恢复完成");
                workOrderService.updateGovernanceResInt("治理完成", workOrder.getId());
                workOrder.setGovernanceRes("治理完成");
            }
            //治理完成后，判断是否需要触发单项巡检操作（跳过）
            asyncSingleInspectionTask.executeBusinessAsync(traceId,heExceptionDataBean, workOrder, (String) resultMap.get("count"));
        } else {
            //调用数据库中配置的修复方式进行修复：先判断是调用存储过程还是脚本|修复类型，0：存储过程，1：脚本；默认0
            if (businessType.getRepairType() == 0) {
                //拼接存储过程名称
                businessExceptionOrderMapper.repairBusinessExceptionOrder(businessType.getRepairWay().trim());
            }else {
                //调用脚本进行修复
                boolean flag = getProcessUtil().executeBusinessScript(businessType.getRepairWay().trim(),traceId);
                asyncPrintLogInfo(traceId,3,3,"业务异常单-治理结果获取成功：" + flag);
            }
//            businessExceptionOrderMapper.retryOperation();
            asyncPrintLogInfo(traceId,1,2,"业务异常单-[异常治理模块]调用脚本[成功]，异常场景：" + heExceptionDataBean.getChildProcessName() + "-" + heExceptionDataBean.getChildProcessName());
            ///说明异常单恢复成功
            updateHeExceptionDataWithRemark(heExceptionDataBean,3,"业务异常单恢复完成");
            updateProgress(heExceptionDataBean,3,3,"success","业务异常单恢复完成");
            workOrderService.updateGovernanceResInt("治理完成", workOrder.getId());
            workOrder.setGovernanceRes("治理完成");
            //治理完成后，判断是否需要触发单项巡检操作（跳过）
            asyncSingleInspectionTask.executeBusinessAsync(traceId,heExceptionDataBean, workOrder, "");
        }
    }

    /**
     * 执行存储过程后，对结果进行校验，判断是否需要继续执行后续流程
     * @param msg
     * @return
     */
    private Map<String, Object> procedureResultCheck(String msg,String traceId,Long dataId) {
        Map<String, Object> parametersMap = new ConcurrentHashMap<>();
        // 解析 p_exec_msg，以获取执行是否成功(flag为0是正常，99为异常) 0|7
        boolean flag = false;
        String countFlag = "";
        if (msg != null && msg.matches("\\d+\\|\\d+")) {
            String[] parts = msg.split("\\|");
            String successFlag = parts[0];
            countFlag = parts[1];
            asyncPrintLogInfo("异常序号：" + dataId + "," + traceId + "业务异常单-[恢复存储过程]调用返回状态码：" + successFlag + "|异常订单数：" + countFlag);
            // asyncPrintLogInfo("业务异常单-[巡检存储过程]调用返回异常订单数：" + countFlag);
            // 如果巡检成功标记是 "0"，则将 inspectionSuccess 设置为 true
            flag = "0".equals(successFlag);
        }
        parametersMap.put("flag", flag);
        parametersMap.put("count", countFlag);
        return parametersMap;
    }
}
