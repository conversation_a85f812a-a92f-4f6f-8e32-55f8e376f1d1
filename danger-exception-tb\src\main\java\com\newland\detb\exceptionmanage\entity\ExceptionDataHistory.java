package com.newland.detb.exceptionmanage.entity;

import java.io.Serializable;
import java.util.Date;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 告警历史工单信息表
 * @TableName HE_EXCEPTION_DATA_HISTORY
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ExceptionDataHistory implements Serializable {
    /**
     * 异常数据编码
     */
    @ExcelProperty("异常数据编码")
    private Long dataId;

    /**
     * 业务环境, 云/非云
     */
    @ExcelProperty("业务环境")
    private String environment;

    /**
     * 告警关键字
     */
    @ExcelProperty("告警关键字")
    private String alarmType;

    /**
     * 归属模块
     */
    @ExcelProperty("归属模块")
    private String homeModule;

    /**
     * 节点名称
     */
    @ExcelProperty("节点名称")
    private String processName;

    /**
     * 异常信息描述
     */
    @ExcelProperty("异常描述")
    private String exceptionDesc;

    /**
     * 处理状态
     */
    @ExcelProperty("处理状态")
    private Integer status;

    /**
     * 发生时间
     */
    @ExcelProperty("创建时间")
    private Date createTime;

    /**
     * 备注
     */
    @ExcelProperty("备注")
    private String remark;

    /**
     * 告警级别,严重告警:1  重要告警：2
     */
    @ExcelProperty("告警级别")
    private String alarmLevel;

    /**
     * 子节点名称
     */
    @ExcelProperty("子节点名称")
    private String childProcessName;

    /**
     * 主机名
     */
    @ExcelProperty("主机名")
    private String hostName;

    /**
     * 主机IP
     */
    @ExcelProperty("主机IP")
    private String hostIp;

    private static final long serialVersionUID = 1L;
}