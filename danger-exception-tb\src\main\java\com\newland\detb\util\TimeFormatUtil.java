package com.newland.detb.util;

import lombok.extern.slf4j.Slf4j;

import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023-04-21 021 15:04:32
 * @description 对相关的时间进行格式化操作，比如将时间格式化为：2002-02-02 08:08:00
 */
@Slf4j
public class TimeFormatUtil {

    /**
     * 对时间进行格式化：yyyy-MM-dd HH:mm:ss
     * @param date
     * @return
     */
    public static String getStringTime(Date date) {
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return format.format(date);
    }

    /**
     * 对时间进行格式化：yyyyMMddHHmmss
     * @param date
     * @return
     */
    public static String getDate(Date date) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        String format = sdf.format(date);
        return format;
    }

    /**
     * 对时间进行格式化：yyyyMMdd
     * @param date
     * @return
     */
    public static String getStringDate(Date date) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        String format = sdf.format(date);
        return format;
    }
    /**
     * 将数字转换为String字符串，实现00001、00002这样的操作
     * @param digit 生成的字符串总位数
     * @param number 要转换的数字
     * @return
     */
    public static String numberToString(int digit, int number) {
        return String.format("%0" + digit + "d", number);
    }
}
