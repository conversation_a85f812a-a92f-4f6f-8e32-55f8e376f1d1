package com.newland.detb.quartz.mapper;

import com.newland.detb.quartz.entity.QuartzTask;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-03-17 017 10:32:16
 * @description 定时任务mapper接口
 */
@Mapper
public interface QuartzJobMapper {

    /**
     * 分页查询全部定时任务
     * @param jobName
     * @param jobGroup
     * @param jobStatus
     * @return
     */
    List<QuartzTask> selectAllTasks(@Param("jobName") String jobName,@Param("jobGroup") String jobGroup,@Param("jobStatus") String jobStatus);

    /**
     * 根据id查询定时任务
     * @param id
     * @return
     */
    QuartzTask selectById(int id);

    /**
     * 新增定任务
     * @param quartzTask
     * @return
     */
    int insertTask(QuartzTask quartzTask);

    /**
     * 根据id删除定时任务
     * @param id
     * @return
     */
    int deleteTaskById(int id);

    /**
     * 更新定时任务的cron表达式，本质上是直接从数据库中删除一个重新创建（从调度器中重新创建一个）
     * @param cron
     * @param id
     * @return
     */
    int updateTaskCron(@Param("cron") String cron, @Param("id") int id);

    /**
     * 根据id修改定时任务状态
     * 0：运行
     * 1：暂停
     * @param jobStatus
     * @return
     */
    int updateJobStatus(@Param("jobStatus") String jobStatus,@Param("id") int id);

}
