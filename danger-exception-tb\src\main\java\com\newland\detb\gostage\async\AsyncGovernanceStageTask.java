package com.newland.detb.gostage.async;

import com.newland.detb.common.entity.HeExceptionDataBean;
import com.newland.detb.exceptionmanage.async.AsyncSingleInspectionTask;
import com.newland.detb.exceptionmanage.async.BaseAsyncTask;
import com.newland.detb.exceptionmanage.entity.BusinessType;
import com.newland.detb.exceptionmanage.entity.ExceptionScenario;
import com.newland.detb.exceptionmanage.entity.WorkOrder;
import com.newland.detb.exceptionmanage.mapper.BusinessExceptionOrderMapper;
import com.newland.detb.exceptionmanage.mapper.NoncloudInfoMapper;
import com.newland.detb.exceptionmanage.service.DictInfoService;
import com.newland.detb.exceptionmanage.service.ScriptInfoService;
import com.newland.detb.exceptionmanage.service.WorkOrderService;
import com.newland.detb.gostage.entity.ExceptionScenarioStage;
import com.newland.detb.lock.DatabaseLock;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;
import java.util.Random;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2023-05-11 011 14:34:14
 * @description 执行治理操作
 */
// @Lazy
@Slf4j
@Component
public class AsyncGovernanceStageTask extends BaseAsyncTask {

    @Resource
    private WorkOrderService workOrderService;

    @Resource
    private ScriptInfoService scriptInfoService;

    @Lazy
    @Resource
    private AsyncSingleInspectionStageTask asyncSingleInspectionStageTask;

    @Resource
    private DictInfoService dictInfoService;

    @Resource
    private BusinessExceptionOrderMapper businessExceptionOrderMapper;

    @Resource
    private NoncloudInfoMapper noncloudInfoMapper;

    @Resource
    private DatabaseLock databaseLock;

    @Value("${stage.time}")
    private int stageTime;

    // 创建Random对象用于生成随机数
    private final Random random = new Random();

    /**
     * 生成0到7之间的随机整数（包含0和7）
     * @return 返回0-7之间的随机数
     */
    public int generateRandomNumber() {
        // nextInt(8)生成0-7之间的随机数
//        return 30 + random.nextInt(150);
        return random.nextInt(stageTime);
    }

    /**
     * 执行治理操作
     * @param heExceptionDataBean 异常数据工单表
     * @param exceptionScenario 异常场景库信息
     * @param workOrder 治理工单
     */
    @Async("taskExecutor")
    @Override
    public void executeAsync(String traceId, HeExceptionDataBean heExceptionDataBean, ExceptionScenarioStage exceptionScenario, WorkOrder workOrder,int randomNumber) {
        asyncPrintLogInfo(traceId,"异常治理");
        updateHeExceptionDataWithRemark(heExceptionDataBean,3,"正在执行异常治理操作");
        updateProgress(heExceptionDataBean,3,3,"doing","正在执行异常治理操作");

        try {
            //调用脚本执行异常治理操作（传递必要参数：xx.sh 应用名 type 环境 机器）
            asyncPrintLogInfo(traceId,3,3,"调用脚本进行异常治理操作的参数，类型：" + exceptionScenario.getType() + "环境：" + heExceptionDataBean.getEnvironment() + "应用名：" + heExceptionDataBean.getHomeModule() + "机器名：" + (heExceptionDataBean.getHostName() == null ? "" : heExceptionDataBean.getHostName()));
            //随机数休眠
            int randomNum = generateRandomNumber();
            log.info("--------------随机数休眠时间：{} 秒", randomNum);
            TimeUnit.SECONDS.sleep(randomNum);
            updateHeExceptionDataWithRemark(heExceptionDataBean,3,"执行异常治理完成");
            updateProgress(heExceptionDataBean,3,3,"success","异常治理操作完成");
            workOrderService.updateGovernanceResInt("治理完成", workOrder.getId());
            workOrder.setGovernanceRes("治理完成");
            //治理完成后，判断是否需要触发单项巡检操作
            asyncSingleInspectionStageTask.executeAsync(traceId,heExceptionDataBean, exceptionScenario, workOrder,randomNumber);
        } catch (Exception e) {
            e.printStackTrace();
            asyncPrintErrorLogInfo(traceId,3,3,"异常治理执行时出现异常：" + e.getMessage());
            updateHeExceptionDataWithRemark(heExceptionDataBean,3,"执行异常治理完成");
            updateProgress(heExceptionDataBean,3,3,"success","异常治理操作完成");
        }
    }
}
