package com.newland.detb.util;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.read.builder.ExcelReaderBuilder;
import com.newland.detb.common.CustomCellHandler;
import com.newland.detb.common.ExcelListener;
import com.newland.detb.common.ResEnum;
import com.newland.detb.common.converter.ExceptionStatusConverter;
import com.newland.detb.common.entity.HeExceptionDataBean;
import com.newland.detb.exception.EasyExcelException;
import com.newland.detb.util.mapper.ExcelBaseService;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.formula.functions.T;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-04-17 017 16:09:29
 * @description EasyExcel导入导出工具类
 */
@Slf4j
public class ExcelUtil {

    /**
     * 导出工具类
     * @param response 响应
     * @param descName 描述信息，用于打印日志
     * @param fileName 导出后的文件名
     * @param sheetName sheet页名称
     * @param clazz 实体类class
     * @param list list数据集合
     */
    public static void exportDataToExcel(HttpServletResponse response, String descName, String fileName, String sheetName, Class<?> clazz, List<?> list) {
        //设置文本内省
        // response.setContentType("application/vnd.ms-excel");
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        //设置字符编码
        response.setCharacterEncoding("utf-8");
        try {
            //设置响应头
            fileName = URLEncoder.encode(fileName, "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
            log.info("开始使用EasyExcel导出数据，实现的功能信息：{} 导出",descName);
            EasyExcel.write(response.getOutputStream(), clazz).registerWriteHandler(new CustomCellHandler()).sheet(sheetName).doWrite(list);
        } catch (IOException e) {
            e.printStackTrace();
            throw new EasyExcelException(ResEnum.excel_error_code,descName + " 导出异常");
        }
    }

    /**
     * 导入数据工具类
     * @param file 导入的文件
     * @param clazz 实体类class
     * @param descName 功能描述信息
     * @param listener 监听器
     */
    public static void importData(MultipartFile file, Class<?> clazz, String descName,ExcelListener<T> listener) {
        try {
            log.info("开始使用EasyExcel导入数据，实现的功能:{}",descName);
            EasyExcel.read(file.getInputStream(),clazz,listener).sheet().doRead();
            // EasyExcel.read(file.getInputStream()).sheet().doRead();
        } catch (IOException e) {
            e.printStackTrace();
            throw new EasyExcelException(ResEnum.excel_error_code,descName + " 导入异常");
        }

    }
}
