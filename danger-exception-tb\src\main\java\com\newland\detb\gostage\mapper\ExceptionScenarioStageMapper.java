package com.newland.detb.gostage.mapper;

import com.newland.detb.exceptionmanage.entity.ExceptionScenario;
import com.newland.detb.gostage.entity.ExceptionScenarioStage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【HE_EXCEPTION_SCENARIO(异常场景库信息表)】的数据库操作Mapper
* @createDate 2023-04-18 14:56:30
* @Entity com.newland.detb.exceptionmanage.entity.ExceptionScenario
*/
@Mapper
public interface ExceptionScenarioStageMapper {

    /**
     * 保存数据
     * @param exceptionScenario
     * @return
     */
    int insert(ExceptionScenarioStage exceptionScenario);

    /**
     * 根据id查询
     * @param id
     * @return
     */
    ExceptionScenarioStage findById(Long id);

    /**
     * 倒序查询全部数据
     * @return
     */
    List<ExceptionScenarioStage>  findAll();

    /**
     * 条件查询
     * @param env
     * @param type
     * @return
     */
    List<ExceptionScenarioStage> findByCondition(@Param("type") String type,@Param("env") Integer env, @Param("subClass") String subClass,@Param("isAuth") Integer isAuth,@Param("isInspection") Integer isInspection);

    /**
     * 更新
     * @param exceptionScenario
     * @return
     */
    int update(ExceptionScenarioStage exceptionScenario);

    /**
     * 根据id删除
     * @param id
     * @return
     */
    int delete(Long id);

    /**
     * 执行异常场景匹配操作
     * 根据三个查询条件：环境、父节点、子节点
     * @param env 环境
     * @param parentNode 父节点
     * @param childNode 子节点
     * @return
     */
    List<ExceptionScenarioStage> findByThreeCondition(@Param("env") int env, @Param("parentNode") String parentNode, @Param("childNode") String childNode);

    /**
     * 异常场景匹配，使用tpye进行匹配，即使用异常关键字进行查询
     * @return
     */
    List<ExceptionScenarioStage> getScenarioByType(@Param("attribution") String attribution,@Param("subClass") String subClass);

    /**
     * 根据异常场景编码查询异常场景库
     * @param sceneCode
     * @return
     */
    ExceptionScenarioStage selectBySceneCode(int sceneCode);

    /**
     * 根据ids查询异常场景库信息
     * @param exportList
     * @return
     */
    List<ExceptionScenarioStage> findByIds(@Param("exportList") List<Long> exportList);

    /**
     * 获取最大的异常场景库编码号
     * @return
     */
    Integer getMaxSceneCode();

    /**
     * 根据id修改是否授权状态
     * @param id
     * @param isAuth
     * @return
     */
    int updateIsAuthById(@Param("id")Long id,@Param("isAuth")int isAuth);

    /**
     * 根据id修改是否需要触发单项巡检
     * @param id
     * @param isInspection
     * @return
     */
    int updateIsInspectionById(@Param("id")Long id,@Param("isInspection")int isInspection);

    /**
     * 根据id修改是否已经覆盖
     * @param id
     * @param isOverride
     * @return
     */
    int updateIsOverrideById(@Param("id") Long id,@Param("isOverride") int isOverride);


    /**
     * 根据条件查询场景ID列表
     */
    List<Long> findScenarioIdsByCondition(@Param("type") String type, @Param("env") Integer env,
                                          @Param("subClass") String subClass, @Param("isAuth") Integer isAuth,
                                          @Param("isInspection") Integer isInspection);

    /**
     * 根据ID列表查询场景数据（包含处理方案）
     */
    List<ExceptionScenarioStage> findByIdList(@Param("ids") List<Long> ids);

    /**
     * 根据归属和类型查询
     * @param attribution
     * @param type
     * @return
     */
    List<ExceptionScenarioStage> queryByAttributionAndType(@Param("attribution") String attribution,@Param("type") String type);
}
