package com.newland.detb.exceptionmanage.service;

import com.newland.detb.exceptionmanage.entity.GovernanceStrategy;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【HE_GOVERNANCE_STRATEGY(异常治理策略信息表)】的数据库操作Service
* @createDate 2023-04-26 16:37:44
*/
public interface GovernanceStrategyService {

    /**
     * 插入异常治理策略
     * @param strategy
     * @return
     */
    int insert(GovernanceStrategy strategy);

    /**
     * 根据异常场景编码查询异常治理策略
     * @param sceneCode
     * @return
     */
    List<GovernanceStrategy> queryBySceneCode(String sceneCode);

    /**
     * 根据异常场景编码删除治理策略
     * @param sceneCode
     * @return
     */
    int delBySceneCode(String sceneCode);

    /**
     * 查询全部治理策略信息
     * @return
     */
    List<GovernanceStrategy> getAllGovernanceStrategies(String remark);

    /**
     * 更新治理策略
     * @param remark
     * @return
     */
    int updateGovernanceStrategy(@Param("id") Long id,@Param("remark") String remark);

}
