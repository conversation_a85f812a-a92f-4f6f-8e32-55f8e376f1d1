package com.newland.detb.log.mapper;

import com.newland.detb.log.entity.LogInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface LogInfoMapper {

    /**
     * 保存日志
     * @param logInfo
     * @return
     */
    int save(LogInfo logInfo);

    /**
     * 根据id删除
     * @param id
     * @return
     */
    int deleteLogInfoById(Long id);

    /**
     * 批量删除
     * @param ids
     * @return
     */
    int deleteLogInfoBatch(Long[] ids);

    /**
     * 根据id查询日志
     * @param id
     * @return
     */
    LogInfo queryLogInfoById(Long id);

    /**
     * 查询全部日志信息
     * @return
     */
    List<LogInfo> queryLogInfo(@Param("operation") String operation);
}
