package com.newland.detb.exceptionmanage.service.impl;

import com.newland.detb.exceptionmanage.entity.EchartsData;
import com.newland.detb.exceptionmanage.entity.ExceptionScenario;
import com.newland.detb.exceptionmanage.mapper.ExceptionEchartsMapper;
import com.newland.detb.exceptionmanage.service.ExceptionEchartsService;
import com.newland.detb.util.ExcelUtil;
import com.newland.detb.util.TimeFormatUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023-06-02 002 15:43:52
 * @description
 */
@Slf4j
@Service
public class ExceptionEchartsServiceImpl implements ExceptionEchartsService {

    @Resource
    private ExceptionEchartsMapper exceptionEchartsMapper;


    /**
     * 查询异常治理概述
     * 例如：当月共计治理：4 次异常告警；其中，治理完成：1 个；等待授权：10个；拒绝授权：1 个；异常场景匹配失败，需手动处理：1 个
     *
     * @return
     */
    @Override
    public String summaryOverview() {
        return exceptionEchartsMapper.summaryOverview();
    }

    /**
     * 30天内每天的治理次数
     *
     * @return
     */
    @Override
    public List<EchartsData> getDataByDay() {
        return exceptionEchartsMapper.getDataByDay();
    }

    /**
     * 查询当月按照场景分组的个数
     *
     * @return
     */
    @Override
    public List<EchartsData> getBusinessExceptionCount() {
        return exceptionEchartsMapper.getBusinessExceptionCount();
    }

    /**
     * 查询当天的按照场景分组的个数
     *
     * @return
     */
    @Override
    public List<EchartsData> getBusinessExceptionGroup() {
        return exceptionEchartsMapper.getBusinessExceptionGroup();
    }

    /**
     * 获取异常场景库根据场景分类占比
     * 饼状图
     *
     * @return
     */
    @Override
    public List<EchartsData> getSceneGroup() {
        return exceptionEchartsMapper.getSceneGroup();
    }

    /**
     * 当月治理场景次数前10的天是哪些天
     *
     * @return
     */
    @Override
    public List<EchartsData> getCountTopTen() {
        return exceptionEchartsMapper.getCountTopTen();
    }

    /**
     * 获取治理结果分类-当天
     * 饼状图
     *
     * @return
     */
    @Override
    public List<EchartsData> getGovernanceResGroupInDay() {
        return exceptionEchartsMapper.getGovernanceResGroupInDay();
    }

    /**
     * 获取治理结果分类-当天
     * 饼状图
     *
     * @return
     */
    @Override
    public List<EchartsData> getGovernanceResGroupInMonth() {
        return exceptionEchartsMapper.getGovernanceResGroupInMonth();
    }

    /**
     * 获取治理结果分类-全部
     * 包含历史表
     * 饼状图
     *
     * @return
     */
    @Override
    public List<EchartsData> getGovernanceResGroupInAll() {
        return exceptionEchartsMapper.getGovernanceResGroupInAll();
    }

    /**
     * 导入异常治理数据
     *
     * @param dataLists
     */
    @Override
    public void exportData(HttpServletResponse response,List<String> dataLists) {
        //循环判断前端传递过来的导出信息
        for (String dataList : dataLists) {
            if (Objects.equals("最近30天治理数据",dataList)) {
                List<EchartsData> everyDayList = exceptionEchartsMapper.getDataByDay();
                if (Optional.ofNullable(everyDayList).isPresent()) {
                    log.info("开始导出异常治理月报数据，共计条数：{}",everyDayList.size());
                    ExcelUtil.exportDataToExcel(response,"开始导出异常治理月报数据","异常治理30天治理月报_" + TimeFormatUtil.getDate(new Date()),"30天治理月报明细", EchartsData.class,everyDayList);
                }else {
                    log.info("异常治理月报信息为空不需要导出");
                }
            }else if (Objects.equals("本月治理次数前10的日期数据",dataList)) {
                //查询本月治理前10的日期数据
                List<EchartsData> topTenList = exceptionEchartsMapper.getCountTopTen();
                if (Optional.ofNullable(topTenList).isPresent()) {
                    log.info("开始导出异常治理月报数据，共计条数：{}",topTenList.size());
                    ExcelUtil.exportDataToExcel(response,"开始导出异常治理月报数据","异常治理本月前10治理月报_" + TimeFormatUtil.getDate(new Date()),"本月治理前10明细", EchartsData.class,topTenList);
                }else {
                    log.info("异常治理月报信息为空不需要导出");
                }
            }else if (Objects.equals("近七天业务异常单治理次数",dataList)) {
                //查询近七天业务异常单治理次数
                List<EchartsData> dataBySceneInMonthList = exceptionEchartsMapper.getBusinessExceptionCount();
                if (Optional.ofNullable(dataBySceneInMonthList).isPresent()) {
                    log.info("开始导出近七天业务异常单治理次数月报数据，共计条数：{}",dataBySceneInMonthList.size());
                    ExcelUtil.exportDataToExcel(response,"开始导出近七天业务异常单月报数据","近七天业务异常单治理次数月报_" + TimeFormatUtil.getDate(new Date()),"本月场景分组治理明细", EchartsData.class,dataBySceneInMonthList);
                }else {
                    log.info("近七天业务异常单治理次数信息为空不需要导出");
                }
            }else {
                log.error("近七天业务异常单治理次数信息导出前置信息识别错误");
                return;
            }
        }
    }

    /**
     * 获取历史告警数据在不同时间段内的治理数据
     *
     * @param type 用于判断是获取系统数据还是业务数据
     * @param time 开始时间
     */
    @Override
    public List<EchartsData> getExceptionDataCountInTimeRange(String type, Integer time) {
        return exceptionEchartsMapper.getExceptionDataCountInTimeRange(type, time);
    }

    /**
     * 获取工单表中数据在不同时间段内的治理数据
     *
     * @param type 判断是系统异常还是业务异常
     * @param time 开始时间
     * @return
     */
    @Override
    public List<EchartsData> getWorkOrderCountInTimeRange(String type, Integer time) {
        return exceptionEchartsMapper.getWorkOrderCountInTimeRange(type, time);
    }
}
