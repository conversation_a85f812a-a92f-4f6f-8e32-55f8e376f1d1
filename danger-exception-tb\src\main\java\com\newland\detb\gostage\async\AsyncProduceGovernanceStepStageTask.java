package com.newland.detb.gostage.async;

import com.newland.detb.common.entity.HeExceptionDataBean;
import com.newland.detb.exceptionmanage.async.AsyncGenerateWorkOrderTask;
import com.newland.detb.exceptionmanage.async.BaseAsyncTask;
import com.newland.detb.exceptionmanage.entity.*;
import com.newland.detb.exceptionmanage.mapper.NoncloudInfoMapper;
import com.newland.detb.exceptionmanage.service.DictInfoService;
import com.newland.detb.exceptionmanage.service.NoncloudInfoService;
import com.newland.detb.exceptionmanage.service.ScriptInfoService;
import com.newland.detb.exceptionmanage.service.WorkOrderService;
import com.newland.detb.gostage.entity.ExceptionScenarioStage;
import com.newland.detb.lock.DatabaseLock;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023-04-26 026 17:07:02
 * @description 生成治理步骤
 */
// @Lazy
@Slf4j
@Component
public class AsyncProduceGovernanceStepStageTask extends BaseAsyncTask {

    @Resource
    private NoncloudInfoService noncloudInfoService;

    //生成治理工单异步任务
    @Lazy
    @Resource
    private AsyncGenerateWorkOrderStageTask asyncGenerateWorkOrderStageTask;

    @Resource
    private ScriptInfoService scriptInfoService;

    @Resource
    private DictInfoService dictInfoService;

    @Resource
    private DatabaseLock databaseLock;

    @Resource
    private NoncloudInfoMapper noncloudInfoMapper;

    @Resource
    private WorkOrderService workOrderService;


    /**
     * 生成治理步骤
     * @param heExceptionDataBean 异常数据工单
     * @param exceptionScenario 异常场景库
     */
    @Async("taskExecutor")
    @Override
    public void executeAsync(String traceId, HeExceptionDataBean heExceptionDataBean, ExceptionScenarioStage exceptionScenario, int randomNumber) {
        asyncPrintLogInfo(traceId,"拼接生成治理恢复步骤");
        updateHeExceptionDataWithRemark(heExceptionDataBean,2,"正在拼接生成治理恢复步骤");
        updateProgress(heExceptionDataBean,2,2,"doing","正在拼接生成治理恢复步骤");

        //治理命令拼接成功
        asyncPrintLogInfo(traceId,2,2,"治理步骤拼接成功:" + exceptionScenario.getBusinessHandles().get(randomNumber).getHandle());
        updateHeExceptionDataWithRemark(heExceptionDataBean,2,"治理步骤拼接成功");
        updateProgress(heExceptionDataBean,2,2,"success","治理步骤拼接成功");
        //去生成治理工单
        asyncGenerateWorkOrderStageTask.executeAsync(traceId,heExceptionDataBean, exceptionScenario, randomNumber);
    }

}
