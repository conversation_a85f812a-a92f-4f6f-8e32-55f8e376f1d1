package com.newland.detb.exceptionmanage.service.impl;

import com.newland.detb.exceptionmanage.entity.WorkOrderHistory;
import com.newland.detb.exceptionmanage.mapper.WorkOrderMapper;
import com.newland.detb.exceptionmanage.service.WorkOrderHistoryService;
import com.newland.detb.exceptionmanage.mapper.WorkOrderHistoryMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【HE_WORK_ORDER_HISTORY(治理历史工单信息表)】的数据库操作Service实现
* @createDate 2023-06-06 10:43:31
*/
@Slf4j
@Service
public class WorkOrderHistoryServiceImpl implements WorkOrderHistoryService{

    @Resource
    private WorkOrderHistoryMapper workOrderHistoryMapper;

    @Resource
    private WorkOrderMapper workOrderMapper;

    /**
     * 保存治理工单表信息
     *
     * @param workOrderHistory
     * @return
     */
    @Override
    public int insert(WorkOrderHistory workOrderHistory) {
        return workOrderHistoryMapper.insert(workOrderHistory);
    }

    /**
     * 迁移历史数据
     *
     * @param historyList
     * @return
     */
    @Transactional
    @Override
    public int migrateData(List<WorkOrderHistory> historyList) {
        //先在历史表中插入数据，然后再清除原表数据
        for (WorkOrderHistory history : historyList) {
            //先插入数据
            int saveRes = workOrderHistoryMapper.insert(history);
            int delRes = workOrderMapper.deleteById(history.getId());
            log.info("[迁移治理工单表历史数据]---历史表保存条数：{}，删除数据条数:{}", saveRes, delRes);
        }
        return historyList.size();
    }
}




