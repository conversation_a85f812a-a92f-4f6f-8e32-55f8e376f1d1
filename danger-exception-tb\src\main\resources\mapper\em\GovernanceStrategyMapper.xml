<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.newland.detb.exceptionmanage.mapper.GovernanceStrategyMapper">

    <resultMap id="BaseResultMap" type="com.newland.detb.exceptionmanage.entity.GovernanceStrategy">
            <id property="id" column="ID" jdbcType="DECIMAL"/>
            <result property="sceneCode" column="SCENE_CODE" jdbcType="VARCHAR"/>
            <result property="strategyCode" column="STRATEGY_CODE" jdbcType="VARCHAR"/>
            <result property="remark" column="REMARK" jdbcType="VARCHAR"/>
            <result property="deleted" column="DELETED" jdbcType="DECIMAL"/>
            <result property="createTime" column="CREATE_TIME" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID,SCENE_CODE,STRATEGY_CODE,
        REMARK,DELETED,CREATE_TIME
    </sql>
    <insert id="insert">
        insert into he_governance_strategy
        (
         scene_code,
         strategy_code,
         remark)
        values(
        #{sceneCode,jdbcType=VARCHAR},
        #{strategyCode,jdbcType=VARCHAR},
        #{remark,jdbcType=VARCHAR}
        )
    </insert>
    <update id="updateGovernanceStrategy">
        update he_governance_strategy
        <set>
            <if test="remark!=null">remark = #{remark,jdbcType=VARCHAR}</if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <delete id="delBySceneCode">
        delete from he_governance_strategy where scene_code = #{sceneCode}
    </delete>
    <select id="queryBySceneCode" resultType="com.newland.detb.exceptionmanage.entity.GovernanceStrategy">
        select id,scene_code,strategy_code,remark,create_time from he_governance_strategy where scene_code = #{sceneCode}
    </select>
    <select id="getAllGovernanceStrategies" resultMap="allGovernanceStrategy">
        SELECT gs.id,
               gs.scene_code,
               gs.strategy_code,
               gs.remark,
               gs.create_time,
               hs.id as sid,
               hs.module,
               hs.attribution,
               hs.type,
               hs.env,
               hs.parent_class,
               hs.sub_class,
               hs.exception_desc,
               hs.source,
               hs.is_override,
               hs.exception_attribution,
               hs.is_auth,
               hs.is_inspection,
               hs.create_time as screate_time
        FROM he_governance_strategy gs
                 left join he_exception_scenario hs
                           on gs.scene_code = hs.scene_code
        <where>
            <if test="remark != null and remark != ''">
                gs.remark like concat('%',#{remark},'%')
            </if>
        </where>
        order by gs.create_time desc
    </select>

    <resultMap id="allGovernanceStrategy" type="com.newland.detb.exceptionmanage.entity.GovernanceStrategy">
        <id column="id" property="id"/>
        <result column="scene_code" property="sceneCode"/>
        <result column="strategy_code" property="strategyCode"/>
        <result column="remark" property="remark"/>
        <result column="create_time" property="createTime"/>
        <association property="exceptionScenario" javaType="com.newland.detb.exceptionmanage.entity.ExceptionScenario">
            <id column="sid" property="id"/>
            <result column="module" property="module"/>
            <result column="attribution" property="attribution"/>
            <result column="type" property="type"/>
            <result column="env" property="env"/>
            <result column="parent_class" property="parentClass"/>
            <result column="sub_class" property="subClass"/>
            <result column="exception_desc" property="exceptionDesc"/>
            <result column="source" property="source"/>
            <result column="is_override" property="isOverride"/>
            <result column="exception_attribution" property="exceptionAttribution"/>
            <result column="is_auth" property="isAuth"/>
            <result column="is_inspection" property="isInspection"/>
            <result column="screate_time" property="createTime"/>
        </association>
    </resultMap>

</mapper>
