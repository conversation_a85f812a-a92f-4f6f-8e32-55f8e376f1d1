package com.newland.detb.exceptionmanage.controller;

import cn.com.nlsoft.cas.client.annotation.CasPermissionRequired;
import cn.hutool.core.lang.Validator;
import cn.hutool.core.util.DesensitizedUtil;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.newland.detb.common.Result;
import com.newland.detb.exceptionmanage.entity.MessageConfig;
import com.newland.detb.exceptionmanage.service.MessageConfigService;
import com.newland.detb.log.annotation.SysLog;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.validator.constraints.Length;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023-05-17 017 11:25:34
 * @description 短信配置
 */
@Slf4j
@RestController
@RequestMapping(value = "/messageConfig")
@Validated
public class ExceptionMessageConfigController {

    @Resource
    private MessageConfigService messageConfigService;

    @SysLog("保存/更新短信配置信息")
    @PostMapping("save")
    @CasPermissionRequired("aimt.message_config.change")
    public Result saveMessageConfig(@RequestBody @Validated MessageConfig messageConfig) {
        log.info("messageConfig-save:{}", messageConfig);
        if (messageConfig.getId() == null) {
            //校验手机号和邮箱
            boolean mobile = Validator.isMobile(messageConfig.getPhone());
            boolean email = Validator.isEmail(messageConfig.getEmail());
            if (mobile && email) {
                int res = messageConfigService.insert(messageConfig);
                if(res == 1) {
                    return Result.success("短信配置保存成功");
                }else {
                    return Result.error(550,"短信配置保存失败");
                }
            }else {
                return Result.error(550,"手机号或邮箱格式不正确，请重新输入");
            }
        }else {
            //校验手机号和邮箱
            boolean mobile = Validator.isMobile(messageConfig.getPhone());
            boolean email = Validator.isEmail(messageConfig.getEmail());
            if (mobile && email) {
                int res = messageConfigService.update(messageConfig);
                if(res == 1) {
                    return Result.success("短信配置更新成功");
                }else {
                    return Result.error(550,"短信配置更新失败");
                }
            }else {
                return Result.error(550,"手机号或邮箱格式不正确，请重新输入");
            }
        }
    }

    @PostMapping("update")
    @CasPermissionRequired("aimt.message_config.change")
    public Result updateMessageConfig(@RequestBody MessageConfig messageConfig) {
        log.info("messageConfig-update:{}", messageConfig);
        int res = messageConfigService.update(messageConfig);
        if(res == 1) {
            return Result.success("短信配置更新成功");
        }else {
            return Result.error(550,"短信配置更新失败");
        }
    }

    @GetMapping("getAll")
    @CasPermissionRequired("aimt.message_config.query")
    public Result getMessageConfig(@RequestParam(value = "currentPage",defaultValue = "1") int currentPage,
                                   @RequestParam(value = "pageSize",defaultValue = "10") int pageSize,
                                   @RequestParam(value = "name",required = false,defaultValue = "") @Length(min = 0,max = 6,message = "请求的姓名超长") String name,
                                   @RequestParam(value = "phone",required = false,defaultValue = "") @Length(min = 0,max = 11,message = "请求的手机号超长") String phone,
                                   @RequestParam(value = "email",required = false,defaultValue = "") @Length(min = 0,max = 30,message = "请求的邮箱地址超长") String email,
                                   @RequestParam(value = "position",required = false,defaultValue = "") @Length(min = 0,max = 10,message = "请求的职位名称超长") String position) {
        PageHelper.startPage(currentPage,pageSize);
        List<MessageConfig> messageConfigs = messageConfigService.selectAll(name,phone,email,position);

        PageInfo<MessageConfig> pageInfo = new PageInfo<>(messageConfigs);
        Map<String, Object> map = new ConcurrentHashMap<>();
        map.put("total",pageInfo.getTotal());
        map.put("list",pageInfo.getList());
        return Result.success(map);
    }

    @SysLog("删除短信配置信息")
    @GetMapping("del/{id}")
    @CasPermissionRequired("aimt.message_config.change")
    public Result delMessageConfig(@PathVariable("id") @Min(value = 0,message = "配置信息ID超出下限") @Max(value = 9223372036854774807L,message = "配置信息ID超出最大值") Long id) {
        int res = messageConfigService.deleteById(id);
        if (res < 0) {
            return Result.error(550,"短信配置信息删除失败");
        }else {
            return Result.success("短信配置信息删除成功");
        }
    }

    @SysLog("查询单条短信配置信息")
    @GetMapping("get/{id}")
    @CasPermissionRequired("aimt.message_config.query")
    public Result getMessageConfigById(@PathVariable("id") @Min(value = 0,message = "配置信息ID超出下限") @Max(value = 9223372036854774807L,message = "配置信息ID超出最大值") Long id) {
        MessageConfig messageConfig = messageConfigService.selectById(id);
        return Result.success(messageConfig);
    }
}
