package com.newland.detb.exceptionmanage.service.impl;

import com.newland.detb.exceptionmanage.entity.GovernanceStrategy;
import com.newland.detb.exceptionmanage.service.GovernanceStrategyService;
import com.newland.detb.exceptionmanage.mapper.GovernanceStrategyMapper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【HE_GOVERNANCE_STRATEGY(异常治理策略信息表)】的数据库操作Service实现
* @createDate 2023-04-26 16:37:44
*/
@Service
public class GovernanceStrategyServiceImpl implements GovernanceStrategyService{

    @Resource
    private GovernanceStrategyMapper governanceStrategyMapper;

    /**
     * 插入异常治理策略
     *
     * @param strategy
     * @return
     */
    @Override
    public int insert(GovernanceStrategy strategy) {
        return governanceStrategyMapper.insert(strategy);
    }

    /**
     * 根据异常场景编码查询异常治理策略
     *
     * @param sceneCode
     * @return
     */
    @Override
    public List<GovernanceStrategy> queryBySceneCode(String sceneCode) {
        return governanceStrategyMapper.queryBySceneCode(sceneCode);
    }

    /**
     * 根据异常场景编码删除治理策略
     *
     * @param sceneCode
     * @return
     */
    @Override
    public int delBySceneCode(String sceneCode) {
        return governanceStrategyMapper.delBySceneCode(sceneCode);
    }

    /**
     * 查询全部治理策略信息
     *
     * @return
     */
    @Override
    public List<GovernanceStrategy> getAllGovernanceStrategies(String remark) {
        return governanceStrategyMapper.getAllGovernanceStrategies(remark);
    }

    /**
     * 更新治理策略
     *
     * @param id
     * @param remark
     * @return
     */
    @Override
    public int updateGovernanceStrategy(Long id, String remark) {
        return governanceStrategyMapper.updateGovernanceStrategy(id,remark);
    }
}




