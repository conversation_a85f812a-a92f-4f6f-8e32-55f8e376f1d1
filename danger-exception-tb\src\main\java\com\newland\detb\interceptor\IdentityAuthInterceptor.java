package com.newland.detb.interceptor;

import cn.hutool.core.util.StrUtil;
import com.auth0.jwt.JWT;
import com.auth0.jwt.JWTVerifier;
import com.auth0.jwt.algorithms.Algorithm;
import com.auth0.jwt.exceptions.JWTDecodeException;
import com.auth0.jwt.exceptions.JWTVerificationException;
import com.newland.detb.common.annotation.AuthAccess;
import com.newland.detb.exception.ServiceException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * @date 2023-02-22 022 17:19:01
 * @description 身份认证拦截器
 */
@Slf4j
public class IdentityAuthInterceptor implements HandlerInterceptor {

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {

        // String token = request.getHeader("token");
        log.info("开始验证身份信息：{}",request.getServerName());
        //去验证身份信息
        return true;

        // String token = request.getHeader("token");
        // //如果不是映射到方法直接通过
        // if (!(handler instanceof HandlerMethod)) {
        //     return true;
        // }else {
        //     HandlerMethod h = (HandlerMethod) handler;
        //     AuthAccess authAccess = h.getMethodAnnotation(AuthAccess.class);
        //     if (authAccess != null) {
        //         return true;
        //     }
        // }
        // //执行认证
        // if (StrUtil.isBlank(token)) {
        //     throw new ServiceException(401,"无token，请重新登录");
        // }
        // //获取token中的userId
        // String userId;
        // try {
        //     userId = JWT.decode(token).getAudience().get(0);
        // }catch (JWTDecodeException e) {
        //     throw new ServiceException(401,"token验证失败，请重新登录");
        // }
        // //根据userId获取用户信息
        // User user = userService.getById(userId);
        // if (user == null) {
        //     throw new ServiceException(Constants.CODE_401,"用户不存在，请重新登录");
        // }
        // //用户密码加签 验证token
        // JWTVerifier jwtVerifier = JWT.require(Algorithm.HMAC256(user.getPassword())).build();
        // try {
        //     jwtVerifier.verify(token);
        // }catch (JWTVerificationException e) {
        //     throw new ServiceException(Constants.CODE_401,"token验证失败，请重新登录");
        // }
        //
        // return true;
    }
}
