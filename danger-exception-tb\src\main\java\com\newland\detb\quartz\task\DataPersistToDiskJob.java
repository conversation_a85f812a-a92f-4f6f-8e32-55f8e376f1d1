package com.newland.detb.quartz.task;

import com.alibaba.excel.EasyExcel;
import com.newland.detb.exceptionmanage.entity.ExceptionDataHistory;
import com.newland.detb.exceptionmanage.entity.WorkOrderHistory;
import com.newland.detb.exceptionmanage.mapper.DataPersistToDiskMapper;
import com.newland.detb.util.TimeFormatUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.quartz.JobDetail;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.quartz.PersistJobDataAfterExecution;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.quartz.QuartzJobBean;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-09-13 013 16:21:15
 * @description 持久化数据到磁盘
 */
@Slf4j
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@Component
@PersistJobDataAfterExecution
public class DataPersistToDiskJob extends QuartzJobBean {

    @Value("${backup.path}")
    private String backupPath;

    @Resource
    private DataPersistToDiskMapper dataPersistToDiskMapper;

    @Override
    protected void executeInternal(JobExecutionContext jobExecutionContext) throws JobExecutionException {
        log.info("----------------------[定时持久化数据到磁盘开始执行]----------------------");
        JobDetail jobDetail = jobExecutionContext.getJobDetail();
        log.info("定时任务名称：{}，定时任务组名：{}，要执行固定的任务描述：{}，执行的当前时间：{}",jobDetail.getKey().getName(),jobDetail.getKey().getGroup(),jobDetail.getDescription(), TimeFormatUtil.getStringTime(new Date()));
        try {
            //查询异常告警待持久化数据（除去最近3个月）
            List<ExceptionDataHistory> exceptionHistoryDataList = dataPersistToDiskMapper.selectPersistExceptionHistory();
            //查询异常治理工单待持久化数据（出去最近3个月）
            List<WorkOrderHistory> workOrderHistoryDataList = dataPersistToDiskMapper.selectPersistWorkOrderHistory();

            //持久化数据格式："异常告警历史数据_" + TimeFormatUtil.getDate(new Date())
            String exceptionHistoryDataPath = backupPath + "/异常告警历史数据_" + TimeFormatUtil.getDate(new Date());
            String workOrderHistoryDataPath = backupPath + "/异常治理工单历史数据_" + TimeFormatUtil.getDate(new Date());
            log.info("持久化异常告警历史数据path：{}",exceptionHistoryDataPath);
            log.info("持久化异常治理工单历史数据path：{}",workOrderHistoryDataPath);
            //导出
            EasyExcel.write(exceptionHistoryDataPath,ExceptionDataHistory.class).sheet("异常告警历史数据").doWrite(exceptionHistoryDataList);
            EasyExcel.write(workOrderHistoryDataPath,WorkOrderHistory.class).sheet("异常治理工单历史数据").doWrite(workOrderHistoryDataList);
            log.info("----------------------[持久化历史数据到磁盘【成功】]----------------------");
        } catch (Exception e) {
            e.printStackTrace();
            log.error("[定时持久化数据到磁盘开始执行出现异常]：{}",e.getMessage());
        }

    }
}
