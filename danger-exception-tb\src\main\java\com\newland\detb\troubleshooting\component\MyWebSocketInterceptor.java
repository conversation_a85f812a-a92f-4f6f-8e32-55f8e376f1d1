package com.newland.detb.troubleshooting.component;

import cn.com.nlsoft.cas.client.bean.CasUser;
import cn.com.nlsoft.cas.client.util.CasClient;
import cn.hutool.extra.servlet.ServletUtil;
import cn.hutool.json.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.http.server.ServletServerHttpRequest;
import org.springframework.http.server.ServletServerHttpResponse;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.WebSocketHandler;
import org.springframework.web.socket.WebSocketSession;
import org.springframework.web.socket.server.HandshakeInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024-03-15 015 17:50:34
 * @description
 */
@Slf4j
@Component
public class MyWebSocketInterceptor implements HandshakeInterceptor {

    @Override
    public boolean beforeHandshake(ServerHttpRequest request, ServerHttpResponse response, WebSocketHandler webSocketHandler, Map<String, Object> map) throws Exception {
        HttpServletRequest rs = ((ServletServerHttpRequest) request).getServletRequest();
        HttpServletResponse hp = ((ServletServerHttpResponse)response).getServletResponse();
        String token = rs.getParameter("token");
        log.info("拦截器中获取到的token：{}",token);

//        if (StringUtils.isBlank(token)){
//            log.info("WebSocket握手时，token为空");
//            throw new Exception("WebSocket握手失败，token为空，请先登录！");
//        }
//        CasUser casUser = CasUser.fromJwt(token);
//        if (casUser == null) {
//            throw new Exception("WebSocket握手失败，请重新登录！");
//        }
        //权限无需验证，只需要登录即可
        //获取request中的fingerId参数信息，作为session的唯一标记
        String fingerId = rs.getParameter("fingerId");
        map.put("fingerId",fingerId);
        return true;
    }

    /**
     * 如果在beforeHandshake方法中返回false，那么就不会触发此方法
     * @param serverHttpRequest
     * @param serverHttpResponse
     * @param webSocketHandler
     * @param e
     */
    @Override
    public void afterHandshake(ServerHttpRequest serverHttpRequest, ServerHttpResponse serverHttpResponse, WebSocketHandler webSocketHandler, Exception e) {

    }
}
