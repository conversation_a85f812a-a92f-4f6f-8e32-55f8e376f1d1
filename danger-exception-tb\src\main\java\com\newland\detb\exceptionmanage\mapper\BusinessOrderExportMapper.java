package com.newland.detb.exceptionmanage.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.newland.detb.exceptionmanage.entity.BusinessOrder;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 进行业务异常单周报数据查询
 * 1、调用云库存储过程进行数据准备
 * 2、调用查询语句进行数据查询
 */
@Mapper
@DS("slave")
public interface BusinessOrderExportMapper {


    /**
     * 查询满足时间范围的异常单信息
     * @param beginDate
     * @param endDate
     * @return
     */
    List<BusinessOrder> queryBusinessOrderList(@Param("beginDate") String beginDate, @Param("endDate") String endDate);
}
