package com.newland.detb.quartz.task;

import com.newland.detb.common.entity.HeExceptionDataBean;
import com.newland.detb.common.util.FunctionUtils;
import com.newland.detb.quartz.service.HeExceptionDataService;
import com.newland.detb.util.JschUtil;
import lombok.*;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.quartz.JobDetail;
import org.quartz.JobExecutionContext;
import org.quartz.PersistJobDataAfterExecution;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.quartz.QuartzJobBean;
import org.springframework.stereotype.Component;
import javax.annotation.Resource;
import java.sql.SQLException;
import java.util.Date;
import java.util.List;


/**
 * @description:内部数据隐患排查信息入库
 * @author: xumengfan
 * @date: 2023/4/14 9:35
 **/
@Slf4j
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@Component
@PersistJobDataAfterExecution
@Configuration
public class HeExceptionDataTaskJob extends QuartzJobBean {

    @Resource
    private HeExceptionDataBean heExceptionDataBean;

    @Resource
    private HeExceptionDataService heExceptionDataService;

    //异常治理标志位判断
    @Resource
    private JschUtil jschUtil;

    /* 环境 */
    private static final String CLOUD_ENVIROMENT="cloud";
    private static final String NOT_CLOUD_ENVIROMENT="notcloud";

    /*告警级别*/
    private static final String IMPORMENT_ALARM_LEVEL="重要告警";
    private static final String SEVERITY_ALARM_LEVEL="严重告警";
    @SneakyThrows
    @Override
    protected void executeInternal(JobExecutionContext context) {
        log.info("-------------------隐患排查任务执行中-----------");
        JobDetail jobDetail = context.getJobDetail();
        log.info("任务名称：{}，组名：{}，要执行固定的任务：{}",jobDetail.getKey().getName(),jobDetail.getKey().getGroup(),jobDetail.getDescription());
        String nowTime = FunctionUtils.formatDate("yyyyMMddHHmmss");
        //需要判断异常治理标志位状态，如果为真再进行入库存储
        Boolean flag = jschUtil.getFileStatus();
        // flag = flagBit && flag;
        log.info("[隐患排查工具隐患分析模块检查标志位状态]，标志位状态为：{}",flag);
        if (flag) {
            try{
                List<HeExceptionDataBean> heExceptionDataBeans = heExceptionDataService.selectExceptionAlarmArk();
                if (heExceptionDataBeans.size() == 0) {
                    log.debug(nowTime+"本时间不存在异常数据");
                }else {
                    log.debug(nowTime+"本时间存在异常数据，开始获取");
                    //获取到隐患排查的数据
                    heExceptionDataBeans.forEach(item -> {

                        if(CLOUD_ENVIROMENT.equals(item.getEnvironment())){
                            item.setEnvironment("0");
                        }
                        if(NOT_CLOUD_ENVIROMENT.equals(item.getEnvironment())){
                            item.setEnvironment("1");
                        }
                        if(SEVERITY_ALARM_LEVEL.equals(item.getAlarmLevel())){
                            item.setAlarmLevel("1");
                        }
                        if(IMPORMENT_ALARM_LEVEL.equals(item.getAlarmLevel())){
                            item.setAlarmLevel("2");
                        }
                        item.setStatus("0");
                        item.setCreateTime(new Date());
                        item.setRemark("隐患排查数据");
                        //对隐患数据做记录
                        int i = heExceptionDataService.insertExceptionData(item);
                        if(i<1){
                            log.debug("插入he_excepion_data表失败：{}",i);
                            try {
                                throw new SQLException("插入he_bgoc_perf_detail表失败");
                            } catch (SQLException throwables) {
                                throwables.printStackTrace();
                            }
                        }
                    });
                }
            }catch(Exception e){
                log.debug("隐患排查失败，异常为：{}"+e.getMessage());
                throw new Exception("隐患排查失败，异常为："+e.getMessage());
            }
        }else{
            log.info("[隐患排查工具隐患分析模块暂停异常数据入库]，原因是治理标志位为[假]");
        }
    }
}
