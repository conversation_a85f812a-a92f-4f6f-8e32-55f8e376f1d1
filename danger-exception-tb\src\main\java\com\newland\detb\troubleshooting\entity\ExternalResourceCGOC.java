package com.newland.detb.troubleshooting.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023-03-10 010 17:54:22
 * @description C-GOC侧业务资源类
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class ExternalResourceCGOC implements Serializable {

    //id
    private Long id;

    //采集资源id
    private String collectionResourceId;

    //设备名称
    private String equipmentName;

    //资源池名称
    private String resourcePoolName;

    //系统版本
    private String systemVersion;

    //pod名称
    private String podName;

    //业务网ipv4
    private String networkIpv4;

    //标准业务系统名称
    private String businessSystemName;

    //外平台来源名称（C-GOC/B-GOC）
    private String platformName;

    //创建时间
    private Date createTime;
}
