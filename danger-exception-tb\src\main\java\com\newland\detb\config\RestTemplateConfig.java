package com.newland.detb.config;

import lombok.Data;
import org.apache.http.Header;
import org.apache.http.client.HttpClient;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.config.Registry;
import org.apache.http.config.RegistryBuilder;
import org.apache.http.conn.socket.ConnectionSocketFactory;
import org.apache.http.conn.socket.PlainConnectionSocketFactory;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.impl.client.DefaultConnectionKeepAliveStrategy;
import org.apache.http.impl.client.DefaultHttpRequestRetryHandler;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.apache.http.message.BasicHeader;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.ClientHttpRequestFactory;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.web.client.RestTemplate;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-03-13 013 15:20:18
 * @description restTemplate配置(请求远程http)
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "http")
public class RestTemplateConfig {

    private Integer maxTotal;

    private Integer defaultMaxPerRoute;

    private Integer connectionTimeout;

    private Integer connectionRequestTimeout;

    private Integer socketTimeout;

    private Boolean staleConnectionCheckEnabled;

    private Integer validateAfterInactivity;

    @Bean
    public RestTemplate restTemplate() {
        return new RestTemplate(httpRequestFactory());
    }

    @Bean
    public  ClientHttpRequestFactory httpRequestFactory() {
        return new HttpComponentsClientHttpRequestFactory(httpClient());
    }

    @Bean
    public HttpClient httpClient() {
        Registry<ConnectionSocketFactory> registry = RegistryBuilder.<ConnectionSocketFactory>create()
                .register("http", PlainConnectionSocketFactory.getSocketFactory())
                .register("https", SSLConnectionSocketFactory.getSocketFactory())
                .build();
        PoolingHttpClientConnectionManager connectionManager = new PoolingHttpClientConnectionManager(registry);
        //最大连接数
        connectionManager.setMaxTotal(maxTotal);
        //单个路由最大连接数
        connectionManager.setDefaultMaxPerRoute(defaultMaxPerRoute);
        //最大空闲时间
        connectionManager.setValidateAfterInactivity(validateAfterInactivity);

        RequestConfig requestConfig = RequestConfig.custom()
                //服务器返回数据(response)的时间，超过抛出read timeout
                .setSocketTimeout(socketTimeout)
                //连接上服务器(握手成功)的时间，超出抛出connect timeout
                .setConnectTimeout(connectionTimeout)
                //提交前检测是否可用
                .setStaleConnectionCheckEnabled(staleConnectionCheckEnabled)
                //从连接池中获取连接的超时时间，超时间未拿到可用连接，会抛出org.apache.http.conn.ConnectionPoolTimeoutException: Timeout waiting for connection from pool
                .setConnectionRequestTimeout(connectionRequestTimeout)
                //为了防止请求时间过长而引起资源的过度浪费。如果在超过设置的timeout还没有数据返回，就直接断开连接
                .build();

        List<Header> headers = new ArrayList<>();
        // headers.add(new BasicHeader("User-Agent", "Mozilla/5.0 (Windows NT 6.1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/31.0.1650.16 Safari/537.36"));
        // headers.add(new BasicHeader("Accept-Encoding", "gzip,deflate"));
        // headers.add(new BasicHeader("Accept-Language", "zh-CN"));
        headers.add(new BasicHeader("Connection", "Keep-Alive"));
        headers.add(new BasicHeader("Content-type", "application/json;charset=UTF-8"));

        return HttpClientBuilder.create()
                .setDefaultRequestConfig(requestConfig)
                .setConnectionManager(connectionManager)
                .setDefaultHeaders(headers)
                //保持长连接配置，需要在headers中添加keep-alive
                .setKeepAliveStrategy(new DefaultConnectionKeepAliveStrategy())
                //重试次数，默认是3次，没有开启
                .setRetryHandler(new DefaultHttpRequestRetryHandler(3,true))
                .build();
    }

    // @Bean
    // public RestTemplate restTemplate(ClientHttpRequestFactory factory) {
    //     return new RestTemplate(factory);
    // }
    //
    // @Bean
    // public ClientHttpRequestFactory simpleClientHttpRequestFactory() {
    //     SimpleClientHttpRequestFactory factory = new SimpleClientHttpRequestFactory();
    //     factory.setReadTimeout(5000);
    //     factory.setConnectTimeout(15000);
    //
    //     return factory;
    // }


}
