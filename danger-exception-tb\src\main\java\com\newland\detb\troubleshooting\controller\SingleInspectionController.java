package com.newland.detb.troubleshooting.controller;


import cn.com.nlsoft.cas.client.annotation.CasPermissionRequired;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.newland.detb.common.Result;
import com.newland.detb.troubleshooting.entity.SingleInspectionBean;
import com.newland.detb.troubleshooting.service.SingleInspectionService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.stream.Collectors;

/**
 * @Author: xumengfan
 * @CreateTime: 2023-04-28  14:50
 * @Description: TODO
 */
@Slf4j
@RestController
@RequestMapping("/single")
public class SingleInspectionController {
    @Resource
    private SingleInspectionService singleInspectionService;

    /**
     * 查询所有巡检项
     * @param
     * @return
     */
    @GetMapping(value="/query")
    @CasPermissionRequired("aimt.single.query")
    public Result querySingleInspection() {
        log.info("进入====》querySingleInspection");
        List<SingleInspectionBean> singleInspectionBeans = singleInspectionService.querySingleInspection();
        log.info("单项巡检结果：{}",singleInspectionBeans.toString());
        return Result.success(singleInspectionBeans);
    }

    @GetMapping(value="/findPage")
    @CasPermissionRequired("aimt.single.query")
    public Object findPage(@RequestParam(value = "pageNow",defaultValue = "1") int pageNum,
                           @RequestParam(value = "pageSize",defaultValue = "100") int pageSize,
                           @RequestParam(value = "id",defaultValue = "") String kpi_template_id) {
        log.info("接收到的单项巡检参数信息：{},{},{}",pageNum,pageSize,kpi_template_id);
        PageHelper.startPage(pageNum,pageSize);
        List<SingleInspectionBean> page = singleInspectionService.findPage(pageNum, pageSize, kpi_template_id);
        List<Map<String,Object>> list = new CopyOnWriteArrayList<>();
        List<String> resourceidList = new ArrayList<>();

        //先将资源编码分组放在数组里
        page.forEach(item -> {
            if(resourceidList.indexOf(item.getResourceid()) == -1){
                resourceidList.add(item.getResourceid());
            }
        });
        for (int i = 0; i < resourceidList.size(); i++) {
            Map<String, Object> map = new ConcurrentHashMap<>();
            for (int j = 0; j < page.size(); j++) {
                if(page.get(j).getResourceid().equals(resourceidList.get(i))){
                    map.put("主机IP", page.get(j).getIpAddress());
                    map.put("巡检项", page.get(j).getTemplateName());
                    //map.put("发生时间", page.get(j).getCreateTime());
                    map.put(page.get(j).getKpiName(),page.get(j).getKpiValue());
                }
            }
            list.add(map);
        }
        PageInfo<Map<String,Object>> pageInfo=new PageInfo(list);

        log.info("分页查询结果检结果：{}",pageInfo);
        return Result.success(pageInfo);
    }

}
