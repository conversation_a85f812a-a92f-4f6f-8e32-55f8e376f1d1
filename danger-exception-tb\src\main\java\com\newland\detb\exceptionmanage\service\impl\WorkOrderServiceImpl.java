package com.newland.detb.exceptionmanage.service.impl;

import com.newland.detb.exceptionmanage.entity.SceneInspection;
import com.newland.detb.exceptionmanage.entity.WorkOrder;
import com.newland.detb.exceptionmanage.mapper.SceneInspectionMapper;
import com.newland.detb.exceptionmanage.service.WorkOrderService;
import com.newland.detb.exceptionmanage.mapper.WorkOrderMapper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【HE_WORK_ORDER(治理工单信息表)】的数据库操作Service实现
* @createDate 2023-05-10 15:28:03
*/
@Service
public class WorkOrderServiceImpl implements WorkOrderService{

    @Resource
    private WorkOrderMapper workOrderMapper;

    @Resource
    private SceneInspectionMapper sceneInspectionMapper;

    /**
     * 插入治理工单表
     *
     * @param workOrder
     * @return
     */
    @Override
    public int insert(WorkOrder workOrder) {
        return workOrderMapper.insert(workOrder);
    }

    /**
     * 根据id查询
     *
     * @param id
     * @return
     */
    @Override
    public WorkOrder selectById(Long id) {
        return workOrderMapper.selectById(id);
    }

    /**
     * 查询全部治理工单表数据
     *
     * @return
     */
    @Override
    public List<WorkOrder> selectAll() {
        return workOrderMapper.selectAll();
    }

    /**
     * 根据id删除
     *
     * @param id
     * @return
     */
    @Override
    public int deleteById(Long id) {
        return workOrderMapper.deleteById(id);
    }

    /**
     * 更新工单表数据
     *
     * @param workOrder
     * @return
     */
    @Override
    public int update(WorkOrder workOrder) {
        return workOrderMapper.update(workOrder);
    }

    /**
     * 更新是否触发单项巡检
     *
     * @param isInspection
     * @param id
     * @return
     */
    @Override
    public int updateIsInspection(int isInspection, Long id) {
        return workOrderMapper.updateIsInspection(isInspection, id);
    }

    /**
     * 更新最终的治理结果
     *
     * @param governanceRes
     * @param id
     * @return
     */
    @Override
    public int updateGovernanceResInt(String governanceRes, Long id) {
        return workOrderMapper.updateGovernanceResInt(governanceRes, id);
    }

    /**
     * 根据data_id查询WorkOrder对象
     *
     * @param dataId
     * @return
     */
    @Override
    public WorkOrder getWorkOrderByDataId(Long dataId) {
        return workOrderMapper.getWorkOrderByDataId(dataId);
    }

    @Override
    public List<WorkOrder> findPage(Long dataId, String appName, String env, Integer isAuth,Integer isInspection) {
        return workOrderMapper.findPage(dataId, appName, env, isAuth,isInspection);
    }

    /**
     * 查询出四个月之前的全部数据，因为数据迁移到历史表中的规则是：3+1（前三个月+当前月的数据需要留在原表中，其他的数据迁移到历史表中）
     *
     * @return
     */
    @Override
    public List<WorkOrder> selectWorkOrderDataForMoveToHistory() {
        return workOrderMapper.selectWorkOrderDataForMoveToHistory();
    }

    /**
     * 获取单项巡检的地址
     *
     * @param sceneCode
     * @return
     */
    @Override
    public String getInspectionPath(String sceneCode, HttpServletRequest request) {
        //根据场景编码，查询关联表，获取对应巡检指标名称
        SceneInspection sceneInspection = sceneInspectionMapper.getInspectionNameBySceneCode(Integer.parseInt(sceneCode));
        if (sceneInspection == null) {
            return null;
        }
        String inspectionName = sceneInspection.getInspectionName();
        return "http://" + request.getServerName() +":9087/hidden/single?id=" + inspectionName;
    }

    /**
     * 根据dataId给workOrder表设置traceId
     *
     * @param traceId
     * @param dataId
     * @return
     */
    @Override
    public int updateTraceIdByDataId(String traceId, Long dataId) {
        return workOrderMapper.updateTraceIdByDataId(traceId, dataId);
    }

    /**
     * 更新是否授权和是否触发单项巡检
     *
     * @param isAuth
     * @param id
     * @return
     */
    @Override
    public int updateIsAuth(int isAuth, Long id) {
        return workOrderMapper.updateIsAuth(isAuth, id);
    }
}




