package com.newland.detb;

import com.newland.detb.common.Result;
import com.newland.detb.common.entity.HeExceptionDataBean;
import com.newland.detb.exceptionmanage.entity.*;
import com.newland.detb.exceptionmanage.mapper.BusinessExceptionOrderMapper;
import com.newland.detb.exceptionmanage.mapper.ExceptionDataHistoryMapper;
import com.newland.detb.exceptionmanage.mapper.NoncloudInfoMapper;
import com.newland.detb.exceptionmanage.service.*;
import com.newland.detb.exceptionmanage.util.ProcessUtil;
import com.newland.detb.exceptionmanage.util.SnowFlakeGeneratorId;
import net.sf.json.JSONObject;
import org.jasypt.util.text.BasicTextEncryptor;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@ActiveProfiles("dev")
class DangerExceptionTbApplicationTests {

    @Resource
    private ExceptionScenarioService exceptionScenarioService;

    @Resource
    private ExceptionProgressService exceptionProgressService;

    @Resource
    private GovernanceStrategyService governanceStrategyService;

    @Resource
    private NoncloudInfoService noncloudInfoService;

    @Resource
    private NoncloudInfoMapper noncloudInfoMapper;

    @Resource
    private ExceptionDataHistoryService exceptionDataHistoryService;

    @Resource
    private WorkOrderHistoryService workOrderHistoryService;

    @Resource
    private ExceptionDataService exceptionDataService;

    @Resource
    private ProcessUtil processUtil;

    @Resource
    private SnowFlakeGeneratorId snowFlakeGeneratorId;

    @Resource
    private BusinessExceptionOrderMapper businessExceptionOrderMapper;

    @Test
    void testNoncloudInfoBatchInsert() {
        List<NoncloudInfo> list = new CopyOnWriteArrayList<>();
        for (int i = 0; i < 10; i++) {
            NoncloudInfo noncloudInfo = new NoncloudInfo();
            noncloudInfo.setAppName("appName");
            noncloudInfo.setProcessName("processName");
            noncloudInfo.setHostIp("127.0.0.1");
            noncloudInfo.setHostName("localhost");
            noncloudInfo.setProcessNum("1");
            noncloudInfo.setScriptPath("/ebosshome/eboss/sprinigboot");
            noncloudInfo.setDeployPath("/ebosshome/eboss/deploy");
            noncloudInfo.setTypeName("applicationSharding");
            list.add(noncloudInfo);
        }
        int res = noncloudInfoMapper.insertBatch(list);
        System.out.println("res:" + res);
    }

    @Test
    void testProcedure() {
        //@Param("id") Long id,@Param("name") String name, @Param("age")int age,@Param("address") String address, @Param("sex")String sex
        Map<String, Object> paramsMap = new ConcurrentHashMap<>();
        paramsMap.put("p_order_table","bb_operation");
        paramsMap.put("p_warning_id","144446");

        businessExceptionOrderMapper.invokeInspection(paramsMap);
        String msg = (String) paramsMap.get("p_exec_msg");
        System.out.println("resultPO:" + msg);
    }

    @Test
    void contextLoads() {
        BasicTextEncryptor encryptor = new BasicTextEncryptor();
        encryptor.setPassword("RnR1y5oBsxXX0kWf3k6dJc3LQ1isuDrw");

        String username = encryptor.encrypt("customer");
        String pwd = encryptor.encrypt("Emc20090");
        // String pwd = encryptor.encrypt("Q9#4ykWqFi");
        // String kafka = encryptor.encrypt("org.apache.kafka.common.security.plain.PlainLoginModule required username=\"consumer9\" password=\"consumer91234\";");
        System.out.println("username:" + username);
        System.out.println("pwd:" + pwd);
        // System.out.println("kafka:" + kafka);

        // String decrypt = encryptor.decrypt("YeciEmjbzoJEJj+0SAH+IvGRy/32xoArvilwaqpUc2Ao5Fz8Dwa/3Iwe+ep76TzMPJXHsoPRkAJ7QzU9jY5AMd+S3qGiDfmUPkOfARooLClRru/qeKNHKcqEr9w8YVwEsrbjgGjVJIvrcLy2bYF4n6551hSlm2Fh");
        // System.out.println("decrypt:" + decrypt);
    }

    @Test
    void testExceptionScenario() {
        ExceptionScenario scenario = new ExceptionScenario();
        scenario.setModule("应用");
        scenario.setAttribution("目录监控1");
        scenario.setType("目录积压1");
        //0：非云
        scenario.setEnv(0);
        scenario.setParentClass("输入目录111");
        scenario.setSubClass("CDR111");
        scenario.setSource("全网监控");
        scenario.setIsOverride(0);
        scenario.setExceptionAttribution(0);
        scenario.setSceneCode(20230430);
        scenario.setExceptionDesc("当前目录10分钟前文件积压超过标准值");
        scenario.setRecoveryStep("1、查询最早文件是否更新，则跟踪10个批次：\n" +
                "登录主机，de到积压目录，使用ls |wc -l 查询积压值，每隔2分钟执行一次，执行10次检查是否检查\n" +
                "2、备份最早文件\n" +
                "find ./ -type f   |xargs  ls -lrt  |head -10 查询最早文件，将文件使用mv到备份目录\n" +
                "3、重启");

        int res = exceptionScenarioService.insert(scenario);
        System.out.println("result:" + res);
        Long id = scenario.getId();
        System.out.println("id:" + id);
    }

    @Test
    void initProgress() {
        List<ProgressDTO> list = new CopyOnWriteArrayList<>();
        for (int i = 0; i < 6; i++) {
            ProgressDTO progress = new ProgressDTO();
            progress.setParentProgress(i);
            progress.setChildProgress(0);
            list.add(progress);
        }
        list.forEach(System.out::println);
        // System.out.println(list);
        int res = exceptionProgressService.insertInitData(1L);
    }

    @Test
    void insertGovernanceData() {
        GovernanceStrategy strategy = new GovernanceStrategy();
        strategy.setSceneCode("1002");
        strategy.setStrategyCode("1002");
        strategy.setRemark("重启进程2");

        GovernanceStrategy strategy1 = new GovernanceStrategy();
        strategy1.setSceneCode("1003");
        strategy1.setStrategyCode("1003");
        strategy1.setRemark("重启进程3");
        governanceStrategyService.insert(strategy);
        governanceStrategyService.insert(strategy1);
        System.out.println("插入完成2：" + strategy);
        System.out.println("插入完成3：" + strategy1);
    }

    @Test
    void insertNonCloudInfoData() {
        NoncloudInfo info = new NoncloudInfo();
        info.setAppName("文件FTP111");
        info.setProcessName("autoFtp111");
        info.setHostIp("10.209.15.14511");
        info.setHostName("ebosscjapp0211");
        info.setProcessNum("3");
        info.setBackupIp("10.209.15.14411");
        info.setScriptPath("/ebosshome/eboss/billing/app_local/application/Independentprocess/autoFtp111");
        info.setDeployPath("bin/start.sh clean111");

        noncloudInfoService.insert(info);
        System.out.println("插入完成noncloudinfo111：" + info);
    }

    @Test
    void testExceptionDataHistory() {
        ExceptionDataHistory history = new ExceptionDataHistory();
        history.setDataId(111111L);
        history.setEnvironment("0");
        history.setAlarmType("yanzhong");
        history.setHomeModule("application");
        history.setProcessName("parentNode");
        history.setExceptionDesc("dsdsdsdsdsdsdsds");
        history.setStatus(1);
        history.setRemark("111");
        history.setAlarmLevel("2");
        history.setChildProcessName("childNode");
        history.setHostIp("127.0.0.1");
        history.setHostName("localhost");
        history.setCreateTime(new Date());
        int res = exceptionDataHistoryService.insert(history);
        System.out.println(res);
    }

    @Test
    void testWorkOrderHistory() {
        WorkOrderHistory history = new WorkOrderHistory();
        history.setId(3333333L);
        history.setDataId(144446L);
        history.setSceneCode("1111");
        history.setStrategyRemark("tesing");
        history.setEnv("1");
        history.setHostName("localhost");
        history.setAppName("application");
        history.setIsAuth(1);
        history.setIsInspection(1);
        history.setCreateTime(new Date());
        int res = workOrderHistoryService.insert(history);
        System.out.println(res);
    }

    @Test
    void test001() {
        List<HeExceptionDataBean> heExceptionDataBeans = exceptionDataService.selectExceptionDataForMoveToHistory();
        heExceptionDataBeans.forEach(System.out::println);
    }

    @Test
    void test002() {
        Integer maxSceneCode = exceptionScenarioService.getMaxSceneCode();
        System.out.println("maxSceneCode:" + maxSceneCode);
        if (maxSceneCode == null) {
            System.out.println("maxSceneCode-Boolean1:" + maxSceneCode);
        }
    }

    @Test
    void test003() {
        // boolean res = ProcessUtil.executeScript("/home/<USER>/test.sh");
        // boolean res = processUtil.executeConfirmScript("/home/<USER>/test.sh");
        // System.out.println(res);
    }

    @Test
    void test004() {
        String jsonStr = "{\"resourceId\":\"cbf7bd37706c4be4b064f7356e8c96c0\",\"subTaskName\":\"BGOCyc1\",\"resourceName\":\n" +
                "\"kvm-hkg-qqw2-18:/apps\",\"dn\":\"b1cd897f9fe14ac185cf7ae896266636.host_fs./apps\",\"type\":\"perf\",\"subTaskId\":\"491660459183140864\",\"perf\":[{\"resourceId\":\"cbf7bd37706c4be4b064f7356e8c96c0\",\"kpiValue\":\"可读写\",\"modelId\":\"host_fs\",\"module\":\"SYSM_PM\",\"kpiId\":\"host_fs_ro\",\"kpiName\":\"文件系统是否只读\",\n" +
                "\"mainModelId\":\"pcserver\",\"resourceName\":\"kvm-hkg-qqw2-18:/apps\",\"dn\":\"b1cd897f9fe14ac185cf7ae896266636.host_fs./apps\",\"belongToSystem\":\"network_wide_customer_center\",\"ip_address\":\"************\",\"modelName\":\"主机文件系统\",\"mainModelName\":\"PC服务器\",\"tenantId\":\"ityj\",\"startTime\":\"2023-07-07 1\n" +
                "5:27:00\",\"endTime\":\"2023-07-07 15:27:00\",\"dimension\":{},\"key\":\"\"},{\"resourceId\":\"cbf7bd37706c4be4b064f7356e8c96c0\",\"kpiValue\":\"104617527\",\"modelId\":\"host_fs\",\"module\":\"SYSM_PM\",\"kpiId\":\"host_fs_inode_free\",\"kpiName\":\"主机文件系统I-Node空闲数\",\"mainModelId\":\"pcserver\",\"resourceName\":\"kvm-hkg\n" +
                "-qqw2-18:/apps\",\"dn\":\"b1cd897f9fe14ac185cf7ae896266636.host_fs./apps\",\"belongToSystem\":\"network_wide_customer_center\",\"ip_address\":\"************\",\"modelName\":\"主机文件系统\",\"mainModelName\":\"PC服务器\",\"tenantId\":\"ityj\",\"startTime\":\"2023-07-07 15:27:00\",\"endTime\":\"2023-07-07 15:27:00\",\"dimens\n" +
                "ion\":{},\"key\":\"\"},{\"resourceId\":\"cbf7bd37706c4be4b064f7356e8c96c0\",\"kpiValue\":\"0.0100\",\"modelId\":\"host_fs\",\"module\":\"SYSM_PM\",\"kpiId\":\"host_fs_inode_used\",\"kpiName\":\"主机文件系统I-Node使用率\",\"mainModelId\":\"pcserver\",\"resourceName\":\"kvm-hkg-qqw2-18:/apps\",\"dn\":\"b1cd897f9fe14ac185cf7ae896266\n" +
                "636.host_fs./apps\",\"belongToSystem\":\"network_wide_customer_center\",\"ip_address\":\"************\",\"modelName\":\"主机文件系统\",\"mainModelName\":\"PC服务器\",\"tenantId\":\"ityj\",\"startTime\":\"2023-07-07 15:27:00\",\"endTime\":\"2023-07-07 15:27:00\",\"dimension\":{},\"key\":\"\"},{\"resourceId\":\"cbf7bd37706c4be4b0\n" +
                "64f7356e8c96c0\",\"kpiValue\":\"238025\",\"modelId\":\"host_fs\",\"module\":\"SYSM_PM\",\"kpiId\":\"host_fs_inode_use\",\"kpiName\":\"主机文件系统I-Node使用数\",\"mainModelId\":\"pcserver\",\"resourceName\":\"kvm-hkg-qqw2-18:/apps\",\"dn\":\"b1cd897f9fe14ac185cf7ae896266636.host_fs./apps\",\"belongToSystem\":\"network_wide_cu\n" +
                "stomer_center\",\"ip_address\":\"************\",\"modelName\":\"主机文件系统\",\"mainModelName\":\"PC服务器\",\"tenantId\":\"ityj\",\"startTime\":\"2023-07-07 15:27:00\",\"endTime\":\"2023-07-07 15:27:00\",\"dimension\":{},\"key\":\"\"},{\"resourceId\":\"cbf7bd37706c4be4b064f7356e8c96c0\",\"kpiValue\":\"0.0800\",\"modelId\":\"host_\n" +
                "fs\",\"module\":\"SYSM_PM\",\"kpiId\":\"host_fs_used\",\"kpiName\":\"主机文件系统使用率\",\"mainModelId\":\"pcserver\",\"resourceName\":\"kvm-hkg-qqw2-18:/apps\",\"dn\":\"b1cd897f9fe14ac185cf7ae896266636.host_fs./apps\",\"belongToSystem\":\"network_wide_customer_center\",\"ip_address\":\"************\",\"modelName\":\"主机文\n" +
                "系统\",\"mainModelName\":\"PC服务器\",\"tenantId\":\"ityj\",\"startTime\":\"2023-07-07 15:27:00\",\"endTime\":\"2023-07-07 15:27:00\",\"dimension\":{},\"key\":\"\"},{\"resourceId\":\"cbf7bd37706c4be4b064f7356e8c96c0\",\"kpiValue\":\"14886.43\",\"modelId\":\"host_fs\",\"module\":\"SYSM_PM\",\"kpiId\":\"host_fs_use\",\"kpiName\":\"主机文\n" +
                "件系统使用大小\",\"mainModelId\":\"pcserver\",\"resourceName\":\"kvm-hkg-qqw2-18:/apps\",\"dn\":\"b1cd897f9fe14ac185cf7ae896266636.host_fs./apps\",\"belongToSystem\":\"network_wide_customer_center\",\"ip_address\":\"************\",\"modelName\":\"主机文件系统\",\"mainModelName\":\"PC服务器\",\"tenantId\":\"ityj\",\"startTim\n" +
                "e\":\"2023-07-07 15:27:00\",\"endTime\":\"2023-07-07 15:27:00\",\"dimension\":{},\"key\":\"\"},{\"resourceId\":\"cbf7bd37706c4be4b064f7356e8c96c0\",\"kpiValue\":\"104855552\",\"modelId\":\"host_fs\",\"module\":\"SYSM_PM\",\"kpiId\":\"host_fs_inode_num\",\"kpiName\":\"文件系统inode数\",\"mainModelId\":\"pcserver\",\"resourceName\":\"k\n" +
                "vm-hkg-qqw2-18:/apps\",\"dn\":\"b1cd897f9fe14ac185cf7ae896266636.host_fs./apps\",\"belongToSystem\":\"network_wide_customer_center\",\"ip_address\":\"************\",\"modelName\":\"主机文件系统\",\"mainModelName\":\"PC服务器\",\"tenantId\":\"ityj\",\"startTime\":\"2023-07-07 15:27:00\",\"endTime\":\"2023-07-07 15:27:00\",\"\n" +
                "dimension\":{},\"key\":\"\"},{\"resourceId\":\"cbf7bd37706c4be4b064f7356e8c96c0\",\"kpiValue\":\"189809.57\",\"modelId\":\"host_fs\",\"module\":\"SYSM_PM\",\"kpiId\":\"host_fs_free\",\"kpiName\":\"主机文件系统剩余大小\",\"mainModelId\":\"pcserver\",\"resourceName\":\"kvm-hkg-qqw2-18:/apps\",\"dn\":\"b1cd897f9fe14ac185cf7ae8962666\n" +
                "36.host_fs./apps\",\"belongToSystem\":\"network_wide_customer_center\",\"ip_address\":\"************\",\"modelName\":\"主机文件系统\",\"mainModelName\":\"PC服务器\",\"tenantId\":\"ityj\",\"startTime\":\"2023-07-07 15:27:00\",\"endTime\":\"2023-07-07 15:27:00\",\"dimension\":{},\"key\":\"\"},{\"resourceId\":\"f6c412e09d4d408da04\n" +
                "07185f21fc57e\",\"kpiValue\":\"可读写\",\"modelId\":\"host_fs\",\"module\":\"SYSM_PM\",\"kpiId\":\"host_fs_ro\",\"kpiName\":\"文件系统是否只读\",\"mainModelId\":\"pcserver\",\"resourceName\":\"kvm-hkg-qqw2-18:/\",\"dn\":\"b1cd897f9fe14ac185cf7ae896266636.host_fs./\",\"belongToSystem\":\"network_wide_customer_center\",\"ip_addre\n" +
                "ss\":\"************\",\"modelName\":\"主机文件系统\",\"mainModelName\":\"PC服务器\",\"tenantId\":\"ityj\",\"startTime\":\"2023-07-07 15:27:00\",\"endTime\":\"2023-07-07 15:27:00\",\"dimension\":{},\"key\":\"\"},{\"resourceId\":\"f6c412e09d4d408da0407185f21fc57e\",\"kpiValue\":\"12532963\",\"modelId\":\"host_fs\",\"module\":\"SYSM_PM\"\n" +
                ",\"kpiId\":\"host_fs_inode_free\",\"kpiName\":\"主机文件系统I-Node空闲数\",\"mainModelId\":\"pcserver\",\"resourceName\":\"kvm-hkg-qqw2-18:/\",\"dn\":\"b1cd897f9fe14ac185cf7ae896266636.host_fs./\",\"belongToSystem\":\"network_wide_customer_center\",\"ip_address\":\"************\",\"modelName\":\"主机文件系统\",\"mainModelN\n" +
                "ame\":\"PC服务器\",\"tenantId\":\"ityj\",\"startTime\":\"2023-07-07 15:27:00\",\"endTime\":\"2023-07-07 15:27:00\",\"dimension\":{},\"key\":\"\"},{\"resourceId\":\"f6c412e09d4d408da0407185f21fc57e\",\"kpiValue\":\"0.0500\",\"modelId\":\"host_fs\",\"module\":\"SYSM_PM\",\"kpiId\":\"host_fs_inode_used\",\"kpiName\":\"主机文件系统I-Node\n" +
                "使用率\",\"mainModelId\":\"pcserver\",\"resourceName\":\"kvm-hkg-qqw2-18:/\",\"dn\":\"b1cd897f9fe14ac185cf7ae896266636.host_fs./\",\"belongToSystem\":\"network_wide_customer_center\",\"ip_address\":\"************\",\"modelName\":\"主机文件系统\",\"mainModelName\":\"PC服务器\",\"tenantId\":\"ityj\",\"startTime\":\"2023-07-07 1\n" +
                "5:27:00\",\"endTime\":\"2023-07-07 15:27:00\",\"dimension\":{},\"key\":\"\"},{\"resourceId\":\"f6c412e09d4d408da0407185f21fc57e\",\"kpiValue\":\"574237\",\"modelId\":\"host_fs\",\"module\":\"SYSM_PM\",\"kpiId\":\"host_fs_inode_use\",\"kpiName\":\"主机文件系统I-Node使用数\",\"mainModelId\":\"pcserver\",\"resourceName\":\"kvm-hkg-qqw\n" +
                "2-18:/\",\"dn\":\"b1cd897f9fe14ac185cf7ae896266636.host_fs./\",\"belongToSystem\":\"network_wide_customer_center\",\"ip_address\":\"************\",\"modelName\":\"主机文件系统\",\"mainModelName\":\"PC服务器\",\"tenantId\":\"ityj\",\"startTime\":\"2023-07-07 15:27:00\",\"endTime\":\"2023-07-07 15:27:00\",\"dimension\":{},\"key\n" +
                "\":\"\"},{\"resourceId\":\"f6c412e09d4d408da0407185f21fc57e\",\"kpiValue\":\"0.1200\",\"modelId\":\"host_fs\",\"module\":\"SYSM_PM\",\"kpiId\":\"host_fs_used\",\"kpiName\":\"主机文件系统使用率\",\"mainModelId\":\"pcserver\",\"resourceName\":\"kvm-hkg-qqw2-18:/\",\"dn\":\"b1cd897f9fe14ac185cf7ae896266636.host_fs./\",\"belongToSyst\n" +
                "em\":\"network_wide_customer_center\",\"ip_address\":\"************\",\"modelName\":\"主机文件系统\",\"mainModelName\":\"PC服务器\",\"tenantId\":\"ityj\",\"startTime\":\"2023-07-07 15:27:00\",\"endTime\":\"2023-07-07 15:27:00\",\"dimension\":{},\"key\":\"\"},{\"resourceId\":\"f6c412e09d4d408da0407185f21fc57e\",\"kpiValue\":\"1310\n" +
                "7200\",\"modelId\":\"host_fs\",\"module\":\"SYSM_PM\",\"kpiId\":\"host_fs_inode_num\",\"kpiName\":\"文件系统inode数\",\"mainModelId\":\"pcserver\",\"resourceName\":\"kvm-hkg-qqw2-18:/\",\"dn\":\"b1cd897f9fe14ac185cf7ae896266636.host_fs./\",\"belongToSystem\":\"network_wide_customer_center\",\"ip_address\":\"************\",\"mod\n" +
                "elName\":\"主机文件系统\",\"mainModelName\":\"PC服务器\",\"tenantId\":\"ityj\",\"startTime\":\"2023-07-07 15:27:00\",\"endTime\":\"2023-07-07 15:27:00\",\"dimension\":{},\"key\":\"\"},{\"resourceId\":\"f6c412e09d4d408da0407185f21fc57e\",\"kpiValue\":\"22380.64\",\"modelId\":\"host_fs\",\"module\":\"SYSM_PM\",\"kpiId\":\"host_fs_use\",\n" +
                "\"kpiName\":\"主机文件系统使用大小\",\"mainModelId\":\"pcserver\",\"resourceName\":\"kvm-hkg-qqw2-18:/\",\"dn\":\"b1cd897f9fe14ac185cf7ae896266636.host_fs./\",\"belongToSystem\":\"network_wide_customer_center\",\"ip_address\":\"************\",\"modelName\":\"主机文件系统\",\"mainModelName\":\"PC服务器\",\"tenantId\":\"ityj\",\n" +
                "\"startTime\":\"2023-07-07 15:27:00\",\"endTime\":\"2023-07-07 15:27:00\",\"dimension\":{},\"key\":\"\"},{\"resourceId\":\"f6c412e09d4d408da0407185f21fc57e\",\"kpiValue\":\"170763.69\",\"modelId\":\"host_fs\",\"module\":\"SYSM_PM\",\"kpiId\":\"host_fs_free\",\"kpiName\":\"主机文件系统剩余大小\",\"mainModelId\":\"pcserver\",\"resourc\n" +
                "eName\":\"kvm-hkg-qqw2-18:/\",\"dn\":\"b1cd897f9fe14ac185cf7ae896266636.host_fs./\",\"belongToSystem\":\"network_wide_customer_center\",\"ip_address\":\"************\",\"modelName\":\"主机文件系统\",\"mainModelName\":\"PC服务器\",\"tenantId\":\"ityj\",\"startTime\":\"2023-07-07 15:27:00\",\"endTime\":\"2023-07-07 15:27:00\",\n" +
                "\"dimension\":{},\"key\":\"\"}]}";
        JSONObject jsonRequest = JSONObject.fromObject(jsonStr);
        System.out.println(jsonRequest);
        System.out.println(jsonRequest.getString("resourceId"));
    }


    @Test
    void test005() {
        // long snowflakeId = snowFlakeGeneratorId.snowflakeId();
        // System.out.println("snowflakeId:" + snowflakeId);

        ExecutorService threadPool = Executors.newFixedThreadPool(5);
        for (int i = 0; i < 20; i++) {
            threadPool.submit(() -> {
                long snowflakeId = snowFlakeGeneratorId.snowflakeId();
                System.out.println("snowflakeId:" + snowflakeId);
            });
        }
        threadPool.shutdown();
    }

}
