package com.newland.detb.exceptionmanage.async;

import com.newland.detb.common.entity.HeExceptionDataBean;
import com.newland.detb.exceptionmanage.entity.*;
import com.newland.detb.exceptionmanage.mapper.ExceptionProgressMapper;
import com.newland.detb.exceptionmanage.mapper.NoncloudInfoMapper;
import com.newland.detb.exceptionmanage.service.*;
import com.newland.detb.lock.DatabaseLock;
import com.newland.detb.quartz.service.HeExceptionDataService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023-04-26 026 17:07:02
 * @description 生成治理步骤
 */
// @Lazy
@Slf4j
@Component
public class AsyncProduceGovernanceStepTask extends BaseAsyncTask{

    @Resource
    private NoncloudInfoService noncloudInfoService;

    //生成治理工单异步任务
    @Lazy
    @Resource
    private AsyncGenerateWorkOrderTask asyncGenerateWorkOrderTask;

    @Resource
    private ScriptInfoService scriptInfoService;

    @Resource
    private DictInfoService dictInfoService;

    @Resource
    private DatabaseLock databaseLock;

    @Resource
    private NoncloudInfoMapper noncloudInfoMapper;

    @Resource
    private WorkOrderService workOrderService;


    /**
     * 生成治理步骤
     * @param heExceptionDataBean 异常数据工单
     * @param exceptionScenario 异常场景库
     * @param governanceStrategy 治理策略
     */
    @Async("taskExecutor")
    @Override
    public void executeAsync(String traceId,HeExceptionDataBean heExceptionDataBean, ExceptionScenario exceptionScenario, GovernanceStrategy governanceStrategy) {
        asyncPrintLogInfo(traceId,"拼接生成治理恢复步骤");
        updateHeExceptionDataWithRemark(heExceptionDataBean,2,"正在拼接生成治理恢复步骤");
        updateProgress(heExceptionDataBean,2,2,"doing","正在拼接生成治理恢复步骤");
        try {
            //先查询脚本配置信息
            ScriptInfo scriptInfo = scriptInfoService.selectByChildException(heExceptionDataBean.getAlarmType());
            if (scriptInfo != null) {
                //根据告警类型名称查询字典表
                DictInfo dictInfo = dictInfoService.selectDictInfo(heExceptionDataBean.getAlarmType());
                if (dictInfo != null) {
                    //拼接命令
                    //xx.sh 应用名 type 环境 机器
                    //对于日志关键字的场景来说，其中如果是198机器（agent01）对应的日志关键字异常，按照云环境处理，那么脚本参数：xx.sh 应用名 type 环境
                    String command = "";
                    //"日志关键字".equals(heExceptionDataBean.getAlarmType())
                    if (isIndependentScene(heExceptionDataBean.getAlarmType())) {
                        //如果是日志关键字、pulsar业务、目录告警（目录积压）、activemq消息队列 场景，需要查询生产资料信息，获取脚本参数：type，同时生成脚本命令
                        command = productLogKeyCommand(heExceptionDataBean, scriptInfo, traceId);
                    }else {
                        command = scriptInfo.getChildCode() + " " + heExceptionDataBean.getHomeModule() + " " + dictInfo.getValue() + " " + heExceptionDataBean.getEnvironment() + " " + (heExceptionDataBean.getHostName() == null ? "" : heExceptionDataBean.getHostName());
                    }
                    //执行脚本
                    asyncPrintLogInfo(traceId,2,2,"生成治理步骤的命令：" + command);
                    //判断command命令是否为空，不为空再执行后续操作
                    if (Objects.equals(command,"")) {
                        asyncPrintErrorLogInfo(traceId,2,2,"治理步骤拼接结果为空，结束后续流程");
                        updateHeExceptionDataWithRemark(heExceptionDataBean,5,"治理步骤为空，治理结束");
                        updateProgress(heExceptionDataBean,2,2,"fail","治理步骤为空，治理结束");
                        updateProgress(heExceptionDataBean,5,1,"fail","治理步骤拼接操作失败，治理步骤为空");
                    }else {
                        //治理命令拼接成功
                        asyncPrintLogInfo(traceId,2,2,"治理步骤拼接成功：" + command);
                        updateHeExceptionDataWithRemark(heExceptionDataBean,2,"治理步骤拼接成功");
                        updateProgress(heExceptionDataBean,2,2,"success","治理步骤拼接成功");
                    }
                    //去生成治理工单
                    asyncGenerateWorkOrderTask.executeAsync(traceId,heExceptionDataBean, exceptionScenario, governanceStrategy,!Objects.equals(command,""),command);
                }else {
                    updateProgress(heExceptionDataBean,2,2,"fail","治理步骤拼接操作失败，字典信息不存在");
                    updateHeExceptionDataWithRemark(heExceptionDataBean,5,"治理步骤拼接操作失败");
                    updateProgress(heExceptionDataBean,5,1,"fail","治理步骤拼接操作失败，字典信息不存在");
                }
            }else {
                updateProgress(heExceptionDataBean,2,2,"fail","治理步骤拼接操作失败，脚本配置信息不存在");
                updateHeExceptionDataWithRemark(heExceptionDataBean,5,"治理步骤拼接操作失败");
                updateProgress(heExceptionDataBean,5,1,"fail","治理步骤拼接操作失败，脚本配置信息不存在");
            }
        } catch (Exception e) {
            e.printStackTrace();
            asyncPrintErrorLogInfo(traceId,2,2,"治理步骤拼接时出现异常：" + e.getMessage());
            updateProgress(heExceptionDataBean,2,2,"fail","治理步骤拼接操作失败");
            updateHeExceptionDataWithRemark(heExceptionDataBean,5,"治理步骤拼接操作失败");
            updateProgress(heExceptionDataBean,5,1,"fail","治理步骤拼接操作失败");
        }
    }

    /**
     * 业务异常单-生成治理步骤
     * @param heExceptionDataBean 异常数据工单
     */
    @Async("taskExecutor")
    public void executeBusinessAsync(String traceId,HeExceptionDataBean heExceptionDataBean,BusinessType businessType) {
        asyncPrintLogInfo(traceId,"业务异常单-拼接生成治理恢复步骤");
        updateHeExceptionDataWithRemark(heExceptionDataBean,2,"业务异常单-拼接治理恢复步骤完成");
        updateProgress(heExceptionDataBean,2,2,"success","业务异常单-拼接治理恢复步骤完成");
        //去执行生成治理工单任务（2-3）
        asyncGenerateWorkOrderTask.executeBusinessAsync(traceId,heExceptionDataBean,businessType);
    }

    /**
     * 拼接日志关键字场景恢复脚本命令参数
     * @param bean
     * @param traceId
     * @return
     */
    private String productLogKeyCommand(HeExceptionDataBean bean,ScriptInfo scriptInfo,String traceId) {
        String command = "";
        try {
            //加锁
            databaseLock.lock("拼接治理步骤",traceId);
            //根据进程名查询关键字type
            //后缀带-
            String envSuffix = Objects.equals(bean.getEnvironment(), "0") ? "云-" : "非云-";
            //前缀带-
            String envPrefix = Objects.equals(bean.getEnvironment(), "0") ? "-云" : "-非云";
            List<String> typeNameList = noncloudInfoMapper.getTypeNameByProcessName(bean.getProcessName(), envSuffix, envPrefix);
            asyncPrintLogInfo(traceId,3,3,"拼接[" + bean.getAlarmType() + "]恢复脚本参数[关键字]为：" + typeNameList);
            if (typeNameList.isEmpty()) {
                //查询不到关键字type，直接返回
                return "";
            }
            if (Objects.equals(bean.getHostName(),"agent01") && Objects.equals(bean.getAlarmType(),"日志关键字")) {
                //说明是198主机日志关键字，脚本参数为：xx.sh 应用名 type 环境
                //其他主机：xx.sh 应用名 type 环境 机器
                command = scriptInfo.getChildCode() + " " + bean.getProcessName() + " " + typeNameList.get(0) + " " + bean.getEnvironment();
            }else {
                command = scriptInfo.getChildCode() + " " + bean.getProcessName() + " " + typeNameList.get(0) + " " + bean.getEnvironment() + " " + (bean.getHostName() == null ? "" : bean.getHostName());
            }
            asyncPrintLogInfo(traceId,3,3,"拼接[" + bean.getAlarmType() + "]恢复脚本参数[命令]为：" + command);
            return command;
        } catch (Exception e) {
            e.printStackTrace();
            asyncPrintErrorLogInfo(traceId,3,3,"拼接[" + bean.getAlarmType() + "]恢复脚本参数异常：" + e.getMessage());
            return "";
        }finally {
            databaseLock.unlock("拼接治理步骤",traceId);
        }
    }

    /**
     * 通过告警关键字判断是否是日志关键字、PULSAR业务、目录告警（目录积压）、activemq消息队列 场景
     * @param alarmTypeName
     * @return
     */
    private Boolean isIndependentScene(String alarmTypeName) {
        return Objects.equals(alarmTypeName, "日志关键字") || Objects.equals(alarmTypeName, "PULSAR业务") || Objects.equals(alarmTypeName, "目录告警") || Objects.equals(alarmTypeName, "activemq消息队列");
    }

}
