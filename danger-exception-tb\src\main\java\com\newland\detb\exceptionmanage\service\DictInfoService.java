package com.newland.detb.exceptionmanage.service;

import com.newland.detb.exceptionmanage.entity.DictInfo;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【HE_DICT_INFO(字典表)】的数据库操作Service
* @createDate 2023-05-18 15:49:31
*/
public interface DictInfoService {

    /**
     * 保存字典数据
     * @param dictInfo
     * @return
     */
    int insert(DictInfo dictInfo);

    /**
     * 根据name查询字典数据
     * @param name
     * @return
     */
    DictInfo selectDictInfo(String name);

    /**
     * 查询全部字段数据
     * @return
     */
    List<DictInfo> selectAllDictInfo(String name,String value);

    /**
     * 根据name删除字典表
     * @param name
     * @return
     */
    int deleteDictInfoByName(String name);

    /**
     * 更新字典数据
     * @param dictInfo
     * @return
     */
    int update(DictInfo dictInfo);
}
