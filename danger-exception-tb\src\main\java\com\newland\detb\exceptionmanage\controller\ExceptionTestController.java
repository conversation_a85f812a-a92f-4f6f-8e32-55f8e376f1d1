package com.newland.detb.exceptionmanage.controller;

import com.newland.detb.common.Result;
import com.newland.detb.lock.DatabaseLock;
import com.newland.detb.util.JschScriptUtil;
import com.newland.detb.util.TestLock;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2023-05-18 018 11:30:04
 * @description
 */
@Slf4j
@RestController
@RequestMapping(value = "/testControl")
public class ExceptionTestController {
    
    @Resource
    private JschScriptUtil jschScriptUtil;

    @Resource
    private DatabaseLock databaseLock;

    @Resource
    private TestLock testLock;
    
    // @GetMapping("test01")
    public Result testSCScript() {
        Boolean flag = null;
        try {
            String command = "6001-001.sh hello world";
            flag = jschScriptUtil.executeSCScript(command);
        } catch (Exception e) {
            log.error("执行脚本失败", e);
        }
        return Result.success(flag);
    }

    private void printInfo(String name) {
        log.info("Test printInfo Method:{}",name);
    }

    // @GetMapping("test02")
    public String everySeconds() {
        try {
            databaseLock.lock("测试","test02");
            printInfo("main method");
            TimeUnit.SECONDS.sleep(10);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            databaseLock.unlock("测试","test02");
        }
        return "完成";
    }

    // @Scheduled(cron = "0/10 * * * * ?")
    public void task() {
        try {
            databaseLock.lock("测试","test");
            printInfo("timing method");
            TimeUnit.SECONDS.sleep(5);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            databaseLock.unlock("测试","test0");
        }
    }

    // @GetMapping("test03")
    public String test03() {
        try {
            databaseLock.lock("测试","test03");
            testLock.printInfo("自定义请求-customer request");
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            databaseLock.unlock("测试","test03");
        }
        return "完成";
    }
}
