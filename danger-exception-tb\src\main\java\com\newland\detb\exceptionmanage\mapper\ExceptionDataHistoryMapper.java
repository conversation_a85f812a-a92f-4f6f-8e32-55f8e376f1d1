package com.newland.detb.exceptionmanage.mapper;

import com.newland.detb.exceptionmanage.entity.ExceptionDataHistory;
import org.apache.ibatis.annotations.Mapper;

/**
* <AUTHOR>
* @description 针对表【HE_EXCEPTION_DATA_HISTORY(告警历史工单信息表)】的数据库操作Mapper
* @createDate 2023-06-06 10:29:03
* @Entity com.newland.detb.exceptionmanage.entity.ExceptionDataHistory
*/
@Mapper
public interface ExceptionDataHistoryMapper {

    /**
     * 保存告警历史工单数据
     * @param exceptionDataHistory
     * @return
     */
    int insert(ExceptionDataHistory exceptionDataHistory);

}




