package com.newland.detb.troubleshooting.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.util.Date;

/**
 * @Author: xumengfan
 * @CreateTime: 2023-04-18  11:58
 * @Description: TODO
 */
@Data
public class BgocPerfDetailBean {
    /**
     *
     */
    private String resourceId;
    /**
     *
     */
    private String kpiValue;

    /**
     *
     */
    private String modelId;

    /**
     *
     */
    private String module;

    /**
     *
     */
    private String kpiId;

    /**
     *
     */
    private String kpiName;

    /**
     *
     */
    private String mainModelId;

    /**
     *
     */
    private String resourceName;

    /**
     *
     */
    private String dn;

    /**
     *
     */
    private String belongToSystem;

    /**
     *
     */
    private String ip_address;

    /**
     *
     */
    private String modelName;
    /**
     *
     */
    private String mainModelName;
    /**
     *
     */
    private String tenantId;

    /**
     *
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    /**
     *
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;



    private Object dimension;
    /**
     *
     */
    private String key;
}
