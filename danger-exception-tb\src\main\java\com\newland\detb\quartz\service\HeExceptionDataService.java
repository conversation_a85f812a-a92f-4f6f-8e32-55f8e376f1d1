package com.newland.detb.quartz.service;

import com.newland.detb.common.entity.HeExceptionDataBean;
import com.newland.detb.exceptionmanage.entity.ExceptionScenario;
import com.newland.detb.quartz.entity.QuartzTask;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface HeExceptionDataService {
    //隐患排查异常数据
    List<HeExceptionDataBean> selectExceptionAlarmArk();

    //插入异常工单表
    int insertExceptionData(HeExceptionDataBean heExceptionData);

    /**
     * 根据id修改工单表的status
     * @param status
     * @return
     */
    int updateExceptionDataStatusById(List<HeExceptionDataBean> heExceptionDataBeans,int status,boolean flag);

    /**
     * 根据id修改工单表的status(单条数据)
     * @param status
     * @return
     */
    int updateExceptionDataStatusById(HeExceptionDataBean heExceptionDataBean,int status);

    /**
     * 根据id修改工单表的status(单条数据，出现异常时，在remark中写入异常信息)
     * @param status
     * @return
     */
    int updateExceptionDataStatusById(HeExceptionDataBean heExceptionDataBean,int status,String remark);

    /**
     * 查询所有工单表数据status为0的记录
     * @return
     */
    List<HeExceptionDataBean> selectExceptionDataStatus0();

    /**
     * 进行异常场景匹配操作
     * @param heExceptionDataBeans
     * @return
     */
    String sceneMatching(List<HeExceptionDataBean> heExceptionDataBeans);

    /**
     * 根据id查询heExceptionData
     * @param dataId
     * @return
     */
    HeExceptionDataBean selectById(Long dataId);
}
