package com.newland.detb.exceptionmanage.service.impl;

import com.newland.detb.exceptionmanage.entity.ExceptionDataHistory;
import com.newland.detb.exceptionmanage.mapper.ExceptionDataMapper;
import com.newland.detb.exceptionmanage.service.ExceptionDataHistoryService;
import com.newland.detb.exceptionmanage.mapper.ExceptionDataHistoryMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【HE_EXCEPTION_DATA_HISTORY(告警历史工单信息表)】的数据库操作Service实现
* @createDate 2023-06-06 10:29:03
*/
@Slf4j
@Service
public class ExceptionDataHistoryServiceImpl implements ExceptionDataHistoryService{

    @Resource
    private ExceptionDataHistoryMapper exceptionDataHistoryMapper;

    @Resource
    private ExceptionDataMapper exceptionDataMapper;

    /**
     * 保存告警历史工单数据
     *
     * @param exceptionDataHistory
     * @return
     */
    @Override
    public int insert(ExceptionDataHistory exceptionDataHistory) {
        return exceptionDataHistoryMapper.insert(exceptionDataHistory);
    }

    /**
     * 迁移数据到历史表中
     *
     * @param historyList
     * @return
     */
    @Transactional
    @Override
    public int migrateData(List<ExceptionDataHistory> historyList) {
        //先保存到历史表中，再移除原表数据
        for (ExceptionDataHistory dataHistory : historyList) {
            //先保存到历史表中
            int saveRes = insert(dataHistory);
            int delRes = exceptionDataMapper.delByDataId(dataHistory.getDataId());
            log.info("[正在进行历史数据迁移]---保存成功数：{}，清除原表数：{}",saveRes,delRes);
        }
        return historyList.size();
    }
}




