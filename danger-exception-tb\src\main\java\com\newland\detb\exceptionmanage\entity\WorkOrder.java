package com.newland.detb.exceptionmanage.entity;

import java.io.Serializable;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * 治理工单信息表
 * @TableName HE_WORK_ORDER
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class WorkOrder implements Serializable {

    /**
     * id
     */
    private Long id;

    /**
     * 异常工单表数据id
     */
    @Max(value = 9223372036854774807L,message = "告警工单ID超出最大值")
    @Min(value = 0,message = "告警工单ID值错误")
    private Long dataId;

    /**
     * 异常场景编码
     */
    private String sceneCode;

    /**
     * 治理策略描述
     */
    private String strategyRemark;

    /**
     * 环境
     */
    @Length(min = 0, max = 1,message = "环境只能选择云或者非云环境")
    private String env;

    /**
     * 主机名
     */
    private String hostName;

    /**
     * 应用名
     */
    @Length(min = 0, max = 25,message = "请求的应用名称超长")
    private String appName;

    /**
     * 是否完成授权确认操作，默认0：没有完成，1：已经完成
     */
    @Min(value = 0,message = "授权操作只能选择是或否")
    @Max(value = 1,message = "授权操作只能选择是或否")
    private Integer isAuth;

    /**
     * 是否触发单项巡检是否判断完成，默认0：未判断，1：判断完成
     */
    @Min(value = 0,message = "单项巡检操作只能选择是或否")
    @Max(value = 1,message = "单项巡检操作只能选择是或否")
    private Integer isInspection;

    /**
     * 是否删除，默认0：表示未删除
     */
    private Integer deleted;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date createTime;

    /**
     * 恢复命令
     */
    private String recoveryCommand;

    /**
     * 最终的治理结果，是否成功，可以用于汇总治理消息
     */
    private String governanceRes;

    /**
     * traceId用于流程跟踪
     */
    private String traceId;

    private static final long serialVersionUID = 1L;
}