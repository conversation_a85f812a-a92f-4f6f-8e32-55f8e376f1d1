package com.newland.detb.common.converter;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.Cell;
import com.alibaba.excel.metadata.CellData;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.property.ExcelContentProperty;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023-04-19 019 11:48:16
 * @description 异常场景库归属模块converter
 */
public class ExceptionScenarioAttributionConverter implements Converter<Integer> {

    //异常归属：0代表系统，1代表业务
    private static final String attrSys = "系统";
    private static final String attrBus = "业务";

    private static final Integer attrSysNum = 0;
    private static final Integer attrBusNum = 1;


    @Override
    public Class supportJavaTypeKey() {
        return Integer.class;
    }

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        return CellDataTypeEnum.STRING;
    }

    /**
     * 导入
     * @param cellData
     * @param excelContentProperty
     * @param globalConfiguration
     * @return
     * @throws Exception
     */
    @Override
    public Integer convertToJavaData(CellData cellData, ExcelContentProperty excelContentProperty, GlobalConfiguration globalConfiguration) throws Exception {
        String value = cellData.getStringValue();
        if (Objects.equals(value,attrSys)) {
            return attrSysNum;
        }else if (Objects.equals(value,attrBus)) {
            return attrBusNum;
        }else {
            return null;
        }
    }

    /**
     * 导出
     * @param s
     * @param excelContentProperty
     * @param globalConfiguration
     * @return
     * @throws Exception
     */
    @Override
    public CellData convertToExcelData(Integer s, ExcelContentProperty excelContentProperty, GlobalConfiguration globalConfiguration) throws Exception {
        if (Objects.equals(s,attrSysNum)) {
            return new CellData<>(attrSys);
        }else if (Objects.equals(s,attrBusNum)) {
            return new CellData<>(attrBusNum);
        }else {
            return new CellData<>("");
        }
    }
}
