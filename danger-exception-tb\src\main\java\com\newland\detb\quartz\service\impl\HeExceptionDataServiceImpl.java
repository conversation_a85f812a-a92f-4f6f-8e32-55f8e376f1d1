package com.newland.detb.quartz.service.impl;

import com.newland.detb.common.ResEnum;
import com.newland.detb.common.entity.HeExceptionDataBean;
import com.newland.detb.exception.ExceptionDataException;
import com.newland.detb.exceptionmanage.entity.WorkOrder;
import com.newland.detb.exceptionmanage.log.GovernanceLogger;
import com.newland.detb.exceptionmanage.mapper.ExceptionProgressMapper;
import com.newland.detb.exceptionmanage.mapper.ExceptionScenarioMapper;
import com.newland.detb.exceptionmanage.service.WorkOrderService;
import com.newland.detb.quartz.mapper.HeExceptionDataMapper;
import com.newland.detb.quartz.service.HeExceptionDataService;
import com.newland.detb.exceptionmanage.async.AsyncSceneMatchingTask;
import com.newland.detb.util.TimeFormatUtil;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;

@Slf4j
@Service
public class HeExceptionDataServiceImpl implements HeExceptionDataService {

    //自定义治理日志配置
    private static final Logger LOG = GovernanceLogger.LOG;

    @Resource
    private HeExceptionDataMapper heExceptionDataMapper;

    @Resource
    private ExceptionScenarioMapper exceptionScenarioMapper;

    @Resource
    private HeExceptionDataService heExceptionDataService;

    @Resource
    private ExceptionProgressMapper exceptionProgressMapper;

    @Resource
    private WorkOrderService workOrderService;

    //异步任务：异常场景匹配流程
    @Lazy
    @Resource
    private AsyncSceneMatchingTask asyncSceneMatchingTask;


    @Override
    public List<HeExceptionDataBean> selectExceptionAlarmArk() {
        return heExceptionDataMapper.selectExceptionAlarmArk();
    }

    @Override
    @Transactional
    public int insertExceptionData(HeExceptionDataBean heExceptionData) {
        return heExceptionDataMapper.insertExceptionData(heExceptionData);
    }

    /**
     * 根据id修改工单表的status
     *
     * @param status
     * @param flag 当flag为true时，表示继续执行后续治理流程；为false时，表示不需要执行后续治理流程
     * @return
     */
    @Override
    @Transactional
    public int updateExceptionDataStatusById(List<HeExceptionDataBean> heExceptionDataBeans, int status,boolean flag) {
        int count = 0;
        //将beans中的全部status改成传递过来的值
        for (HeExceptionDataBean bean : heExceptionDataBeans) {
            //在创建workOrder对象时，需要考虑是否时业务上异常：对于业务上异常需要获取节点名称和子节点名称
            //往治理工单表中插入对应的数据
            WorkOrder workOrder = new WorkOrder();
            if (Objects.equals(bean.getAlarmType(),"business") || Objects.equals(bean.getAlarmType(),"BUSINESS")) {
                //业务异常
                workOrder.setDataId(bean.getDataId());
                workOrder.setEnv(bean.getEnvironment());
                workOrder.setAppName(bean.getProcessName() != null ? bean.getProcessName() : "业务异常");
                workOrder.setHostName(bean.getHomeModule() != null ? bean.getHomeModule() : "业务异常");
                workOrder.setStrategyRemark("执行异常单恢复处理");
                workOrder.setRecoveryCommand("调度业务异常单处理框架");
                workOrder.setSceneCode("待匹配");
            }else {
                //系统异常
                workOrder.setDataId(bean.getDataId());
                workOrder.setEnv(bean.getEnvironment());
                workOrder.setAppName(bean.getHomeModule() == null ? (Objects.equals(bean.getEnvironment(),"0") ? "云主机应用" : "非云主机应用") : bean.getHomeModule());
                workOrder.setHostName(bean.getHostName() == null ? (Objects.equals(bean.getEnvironment(),"0") ? "云主机" : "非云主机") : bean.getHostName());
                workOrder.setStrategyRemark("执行恢复调度任务");
                workOrder.setRecoveryCommand("调度恢复脚本处理");
                workOrder.setSceneCode("待匹配");
            }

            if (!flag) {
                workOrder.setGovernanceRes("治理结束：根据治理标志位判断结果，无需执行后续治理流程");
                heExceptionDataService.updateExceptionDataStatusById(bean,status,"治理结束：根据治理标志位判断结果，无需执行后续治理流程");
            }
            int initResult = workOrderService.insert(workOrder);
            if (initResult > 0) {
                LOG.info("WorkOrder初始化成功，data_id:{}",bean.getDataId());
            }
            int res = heExceptionDataMapper.updateExceptionDataById(bean.getDataId(), status);
            if (res != 1) {
                heExceptionDataMapper.updateExceptionDataRemarkById(bean.getDataId(), 11,"异常治理工单信息更新治理状态失败，请联系管理员查看");
                throw new ExceptionDataException(ResEnum.exception_data_error,"异常治理工单信息更新治理状态失败");
            }
            int progressResult;
            if (flag) {
                progressResult = exceptionProgressMapper.updateChildProgressByDataId(bean.getDataId(), 0, 3, "success", "治理标志位判断完成:继续执行", new Date());
            }else {
                progressResult = exceptionProgressMapper.updateChildProgressByDataId(bean.getDataId(), 0, 3, "fail", "治理标志位判断完成:结束流程", new Date());
            }
            // int progressResult = exceptionProgressMapper.updateChildProgressByDataId(bean.getDataId(), 0, 3, "success", "异常治理标志位判断完成:" + (flag ? "正常执行后续治理流程" : "结束执行后续治理流程"), new Date());
            if (progressResult != 1) {
                LOG.error("进度表更新异常治理标记位失败，data_id:{}",bean.getDataId());
            }
            count++;
            //告警解析
            String result = "告警解析完成,解析结果:" + (bean.getHostIp() == null ? "云环境主机" : bean.getHostIp()) + ",应用名称:" + bean.getHomeModule() + ",异常关键字:" + bean.getAlarmType();
            if (flag) {
                exceptionProgressMapper.updateChildProgressByDataId(bean.getDataId(), 0, 4, "success", result, new Date());
            }
        }
        return count;
    }

    /**
     * 根据id修改工单表的status(单条数据)
     *
     * @param heExceptionDataBean
     * @param status
     * @return
     */
    @Override
    public int updateExceptionDataStatusById(HeExceptionDataBean heExceptionDataBean, int status) {
        return heExceptionDataMapper.updateExceptionDataById(heExceptionDataBean.getDataId(), status);
    }

    /**
     * 根据id修改工单表的status(单条数据，出现异常时，在remark中写入异常信息)
     *
     * @param heExceptionDataBean
     * @param status
     * @param remark
     * @return
     */
    @Override
    public int updateExceptionDataStatusById(HeExceptionDataBean heExceptionDataBean, int status, String remark) {
        return heExceptionDataMapper.updateExceptionDataRemarkById(heExceptionDataBean.getDataId(), status, remark);
    }

    /**
     * 查询所有工单表数据status为0的记录
     *
     * @return
     */
    @Override
    public List<HeExceptionDataBean> selectExceptionDataStatus0() {
        return heExceptionDataMapper.selectExceptionDataOfStatusZero();
    }

    /**
     * 进行异常场景匹配操作
     * 去异常场景库中根据：环境、父节点、子节点
     * 可以去异步执行多个异常匹配操作
     * @param heExceptionDataBeans
     * @return
     */
    @Override
    public String sceneMatching(List<HeExceptionDataBean> heExceptionDataBeans) {
        //使用线程池处理异常场景匹配
        LOG.info("【======定时任务扫描完成，使用线程池进行异步的异常场景匹配操作======】");
        //修改进度表状态为正在处理
        for (HeExceptionDataBean bean : heExceptionDataBeans) {
            exceptionProgressMapper.updateChildProgressByDataId(bean.getDataId(), 1,2,"doing","正在处理",new Date());
        }

        // log.info("1-2------>去使用线程池进行异常场景匹配操作，当前时间:{}", TimeFormatUtil.getStringTime(new Date()));
        CompletableFuture<String> future = CompletableFuture.supplyAsync(() -> {
            //异步进行匹配
            // asyncSceneMatchingTask.exceptionSceneMatching(heExceptionDataBeans);
            asyncSceneMatchingTask.executeAsync(heExceptionDataBeans);
            return "异常场景异步匹配完成，共匹配了：" + heExceptionDataBeans.size() + " 条记录";
        });
        try {
            String result = future.get();
            LOG.info("异步异常场景匹配结束：{}",result);
        } catch (InterruptedException | ExecutionException e) {
            e.printStackTrace();
            LOG.error("获取异步任务：异常场景匹配操作的执行结果发发生异常");
        }
        return "异步任务->正在执行后续异常治理流程";
    }

    /**
     * 根据id查询heExceptionData
     *
     * @param dataId
     * @return
     */
    @Override
    public HeExceptionDataBean selectById(Long dataId) {
        return heExceptionDataMapper.selectById(dataId);
    }
}
