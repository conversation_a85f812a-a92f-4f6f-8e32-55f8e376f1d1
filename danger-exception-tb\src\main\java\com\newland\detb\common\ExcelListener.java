package com.newland.detb.common;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.fastjson.JSON;
import com.newland.detb.util.mapper.ExcelBaseService;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.concurrent.CopyOnWriteArrayList;

/**
 * <AUTHOR>
 * @date 2023-04-17 017 17:06:07
 * @description 导入数据监听器
 */
@Slf4j
public class ExcelListener<T> extends AnalysisEventListener<T> {

    //临时保存等待插入的数据
    List<Object> list = new CopyOnWriteArrayList<>();

    /**
     * 每隔5条存储数据库，实际使用中可以3000条，然后清理list ，方便内存回收
     */
    private static final int BATCH_COUNT = 5;

    //保存数据的父级mapper
    private ExcelBaseService excelBaseService;

    public ExcelListener(ExcelBaseService mapper) {
        this.excelBaseService = mapper;
    }

    /**
     * 每次解析一条数据
     * @param t
     * @param analysisContext
     */
    @Override
    public void invoke(T t, AnalysisContext analysisContext) {
        log.info("解析到一条数据:{}", JSON.toJSONString(t));
        list.add(t);
        if (list.size() >= BATCH_COUNT) {
            saveData();
            // 存储完成清理 list
            list.clear();
        }
    }

    /**
     *  所有数据解析完毕，都要进行
     * @param analysisContext
     */
    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {
        //这里也要保存数据，确保最后遗留的数据也存储到数据库
        saveData();
        log.info("所有数据解析完成");
    }

    /**
     * 加上存储数据库
     */
    private void saveData() {
        log.info("{}条数据，开始存储数据库！", list.size());
        excelBaseService.save(list);
        log.info("存储数据库成功！");
    }

    /**
     * 获取所有的list集合数据
     * @return
     */
    public List<Object> getData() {
        return list;
    }

    /**
     * 给list集合赋值
     * @param list
     */
    public void setData(List<Object> list) {
        this.list = list;
    }
}
