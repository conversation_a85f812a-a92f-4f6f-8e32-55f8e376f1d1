package com.newland.detb.exceptionmanage.service;

import com.newland.detb.exceptionmanage.entity.ExceptionDataHistory;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【HE_EXCEPTION_DATA_HISTORY(告警历史工单信息表)】的数据库操作Service
* @createDate 2023-06-06 10:29:03
*/
public interface ExceptionDataHistoryService {

    /**
     * 保存告警历史工单数据
     * @param exceptionDataHistory
     * @return
     */
    int insert(ExceptionDataHistory exceptionDataHistory);

    /**
     * 迁移数据到历史表中
     * @param historyList
     * @return
     */
    int migrateData(List<ExceptionDataHistory> historyList);

}
