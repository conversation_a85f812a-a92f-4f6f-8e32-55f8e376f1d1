package com.newland.detb.troubleshooting.mapper;

import com.newland.detb.troubleshooting.entity.BgocPerfDetailBean;
import com.newland.detb.troubleshooting.entity.BgocRecordBean;
import org.apache.ibatis.annotations.Mapper;

/**
* <AUTHOR>
* @description 针对表【HE_BGOC_PERF_DETAIL】的数据库操作Mapper
* @createDate 2023-04-17 16:54:20
* @Entity com.newland.detb.troubleshooting.entity.HeBgocPerfDetail
*/
@Mapper
public interface HeBgocDataMapper {

    //插入BGOC记录
    int insertHeBgocRecord(BgocRecordBean bgocRecordBean);
    //插入BGOC明细数据
    int insertHeBgocPerfDetail(BgocPerfDetailBean BgocPerfDetailBean);

    /**
     * 定时清理B-GOC数据
     */
    void truncateBGocRecordTable();

    /**
     * 定时清理B-GOC性能数据
     */
    void truncateBGocPerfTable();
}
