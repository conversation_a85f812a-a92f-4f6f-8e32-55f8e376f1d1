package com.newland.detb.exceptionmanage.service.impl;

import com.newland.detb.exceptionmanage.entity.SceneInspection;
import com.newland.detb.exceptionmanage.service.SceneInspectionService;
import com.newland.detb.exceptionmanage.mapper.SceneInspectionMapper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
* <AUTHOR>
* @description 针对表【HE_SCENE_INSPECTION(单项巡检和异常场景关联配置表)】的数据库操作Service实现
* @createDate 2023-06-13 14:21:37
*/
@Service
public class SceneInspectionServiceImpl implements SceneInspectionService{

    @Resource
    private SceneInspectionMapper sceneInspectionMapper;

    /**
     * 保存关联数据(巡检指标名称手动进行配置)
     *
     * @param sceneInspection
     * @return
     */
    @Override
    public int insert(SceneInspection sceneInspection) {
        return sceneInspectionMapper.insert(sceneInspection);
    }

    /**
     * 根据sceneCode删除
     *
     * @param sceneCode
     * @return
     */
    @Override
    public int deleteBySceneCode(int sceneCode) {
        return sceneInspectionMapper.deleteBySceneCode(sceneCode);
    }
}




