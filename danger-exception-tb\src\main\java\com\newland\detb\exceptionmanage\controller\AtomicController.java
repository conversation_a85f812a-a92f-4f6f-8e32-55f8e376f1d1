package com.newland.detb.exceptionmanage.controller;

import cn.com.nlsoft.cas.client.annotation.CasPermissionRequired;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.newland.detb.common.Result;
import com.newland.detb.exceptionmanage.entity.AtomicInfo;
import com.newland.detb.exceptionmanage.entity.MessageInfo;
import com.newland.detb.exceptionmanage.util.AtomicInfoUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @date 2023-08-09 009 18:20:49
 * @description 原子操作信息
 */
@Slf4j
@RestController
@RequestMapping("/atomic")
public class AtomicController {

    //原子操作信息工具类
    @Resource
    private AtomicInfoUtil atomicInfoUtil;

    /**
     * 获取基础的脚本信息
     * @param currentPage
     * @param pageSize
     * @return
     */
    @GetMapping("getFileInfo")
    @CasPermissionRequired("aimt.atomic.query")
    public Result getFileInfo(@RequestParam(value = "currentPage",defaultValue = "1") int currentPage,
                              @RequestParam(value = "pageSize",defaultValue = "10") int pageSize) {

        PageHelper.startPage(currentPage,pageSize);
        //需要构建children
        List<AtomicInfo> atomicInfoList = atomicInfoUtil.getFileBaseInfo();

        PageInfo<AtomicInfo> pageInfo = new PageInfo<>(atomicInfoList);
        Map<String, Object> map = new ConcurrentHashMap<>();
        map.put("total",getListSize(atomicInfoList));
        map.put("list",pageInfo.getList());
        return Result.success(map);
    }

    /**
     * 根据文件路径获取指定文件的内容
     * @param filePath
     * @return
     */
    @GetMapping("getFileContent")
    @CasPermissionRequired("aimt.atomic.query")
    public Result getFileContent(@RequestParam("filePath") String filePath) {
        String fileContent = atomicInfoUtil.getFileContent(filePath);
        return Result.success(fileContent);
    }

    /**
     * 获取脚本个数
     * @param atomicInfoList
     * @return
     */
    private int getListSize(List<AtomicInfo> atomicInfoList) {
        int count = 0;
        for (AtomicInfo item : atomicInfoList) {
            count += item.getChildren().size();
        }
        return count;
    }
}
