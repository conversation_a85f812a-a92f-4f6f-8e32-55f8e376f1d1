package com.newland.detb.exceptionmanage.mapper;

import com.newland.detb.common.entity.HeExceptionDataBean;
import com.newland.detb.exceptionmanage.entity.ExceptionDataHistory;
import com.newland.detb.exceptionmanage.entity.WorkOrderHistory;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 持久化历史数据到磁盘
 */
@Mapper
public interface DataPersistToDiskMapper {

    /**
     * 查询历史治理数据(除去最近3个月)
     * 导出全部数据
     * @return
     */
    List<ExceptionDataHistory> selectPersistExceptionHistory();

    /**
     * 查询历史工单数据(除去最近3个月)
     * @return
     */
    List<WorkOrderHistory> selectPersistWorkOrderHistory();
}
