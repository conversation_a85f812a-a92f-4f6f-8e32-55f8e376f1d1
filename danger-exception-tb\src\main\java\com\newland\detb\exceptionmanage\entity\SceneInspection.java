package com.newland.detb.exceptionmanage.entity;

import java.io.Serializable;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 单项巡检和异常场景关联配置表
 * @TableName HE_SCENE_INSPECTION
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SceneInspection implements Serializable {
    /**
     * 异常场景编码
     */
    private Integer sceneCode;

    /**
     * 异常场景名称
     */
    private String sceneName;

    /**
     * 单项巡检编码
     */
    private Integer inspectionCode;

    /**
     * 单项巡检内容
     */
    private String inspectionName;

    private static final long serialVersionUID = 1L;
}