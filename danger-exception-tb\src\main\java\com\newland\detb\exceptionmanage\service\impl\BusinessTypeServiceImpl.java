package com.newland.detb.exceptionmanage.service.impl;

import com.newland.detb.exceptionmanage.entity.BusinessType;
import com.newland.detb.exceptionmanage.service.BusinessTypeService;
import com.newland.detb.exceptionmanage.mapper.BusinessTypeMapper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【HE_BUSINESS_TYPE(业务异常场景表)】的数据库操作Service实现
* @createDate 2023-11-01 18:04:35
*/
@Service
public class BusinessTypeServiceImpl implements BusinessTypeService{

    @Resource
    private BusinessTypeMapper businessTypeMapper;

    /**
     * 根据业务场景名称查询是否存在
     *
     * @param type
     * @return
     */
    @Override
    public BusinessType queryBusinessType(String type,String parentName,String childName) {
        return businessTypeMapper.queryBusinessType(type,parentName,childName);
    }

    /**
     * 查询全部业务异常集合
     *
     * @param parentName
     * @param childName
     * @return
     */
    @Override
    public List<BusinessType> queryBusinessTypeList( String parentName, String childName) {
        return businessTypeMapper.queryBusinessTypeList(parentName,childName);
    }

    /**
     * 新增业务异常场景
     *
     * @param businessType
     * @return
     */
    @Override
    public int save(BusinessType businessType) {
        return businessTypeMapper.save(businessType);
    }

    /**
     * @param businessType
     * @return
     */
    @Override
    public int update(BusinessType businessType) {
        return businessTypeMapper.update(businessType);
    }

    /**
     * 根据id删除业务异常场景
     *
     * @param id
     * @return
     */
    @Override
    public int delete(Long id) {
        return businessTypeMapper.delete(id);
    }
}




