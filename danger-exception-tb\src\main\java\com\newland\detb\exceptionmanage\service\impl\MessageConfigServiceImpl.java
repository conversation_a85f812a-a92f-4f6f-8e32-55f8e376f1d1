package com.newland.detb.exceptionmanage.service.impl;

import cn.hutool.core.util.DesensitizedUtil;
import com.newland.detb.exceptionmanage.entity.MessageConfig;
import com.newland.detb.exceptionmanage.service.MessageConfigService;
import com.newland.detb.exceptionmanage.mapper.MessageConfigMapper;
import lombok.val;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
* <AUTHOR>
* @description 针对表【HE_MESSAGE_CONFIG(短信配置表)】的数据库操作Service实现
* @createDate 2023-05-17 11:18:14
*/
@Service
public class MessageConfigServiceImpl implements MessageConfigService{

    @Resource
    private MessageConfigMapper messageConfigMapper;

    /**
     * 插入messageConfig
     *
     * @param messageConfig
     * @return
     */
    @Override
    public int insert(MessageConfig messageConfig) {
        return messageConfigMapper.insert(messageConfig);
    }

    /**
     * 根据id删除
     *
     * @param id
     * @return
     */
    @Override
    public int deleteById(Long id) {
        return messageConfigMapper.deleteById(id);
    }

    /**
     * 更新messageConfig
     *
     * @param messageConfig
     * @return
     */
    @Override
    public int update(MessageConfig messageConfig) {
        return messageConfigMapper.update(messageConfig);
    }

    /**
     * 根据id查询
     *
     * @param id
     * @return
     */
    @Override
    public MessageConfig selectById(Long id) {
        return messageConfigMapper.selectById(id);
    }

    /**
     * 查询全部
     *
     * @return
     */
    @Override
    public List<MessageConfig> selectAll(String name, String phone, String email, String position) {
        //进行脱敏展示
        List<MessageConfig> messageConfigs = messageConfigMapper.selectAll(name, phone, email, position);
        if (messageConfigs != null && messageConfigs.size() > 0) {
            for (MessageConfig messageConfig : messageConfigs) {
                messageConfig.setPhone(DesensitizedUtil.mobilePhone(messageConfig.getPhone()));
                messageConfig.setEmail(DesensitizedUtil.email(messageConfig.getEmail()));
            }
        }
        return messageConfigs;
    }

    /**
     * 查询出is_send是0，即需要发送同步短信的人
     *
     * @return
     */
    @Override
    public List<MessageConfig> selectIsSend() {
        return messageConfigMapper.selectIsSend();
    }

    /**
     * 查询需要发送的内部负责人信息
     *
     * @return
     */
    @Override
    public List<MessageConfig> selectInnerList() {
        return messageConfigMapper.selectInnerList();
    }
}




