package com.newland.detb.common;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2023-02-22 022 17:46:54
 * @description
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class Result {

    private int code;

    private String msg;

    private Object data;

    public static Result success() {
        return new Result(ResEnum.success_code,"操作成功",null);
    }

    public static Result success(Object data) {
        return new Result(ResEnum.success_code,"",data);
    }

    public static Result success(Object... data) {
        return new Result(ResEnum.success_code,"",data);
    }

    public static Result error(int code,String msg) {
        return new Result(code,msg,null);
    }

    public static Result error() {
        return new Result(ResEnum.error_code,"系统错误",null);
    }
}
