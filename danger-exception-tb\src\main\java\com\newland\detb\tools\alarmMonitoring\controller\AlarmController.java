package com.newland.detb.tools.alarmMonitoring.controller;

import com.newland.detb.common.Result;
import com.newland.detb.tools.alarmMonitoring.entity.AlarmInfo;
import com.newland.detb.tools.alarmMonitoring.service.AlarmService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2022-12-01 001 16:02:14
 * @description 告警控制类
 */
@RestController
@RequestMapping("/alarm")
@Slf4j
public class AlarmController {

    @Resource
    private AlarmService alarmService;

    /**
     * 保存告警信息
     * @param alarmInfo
     * @return
     */
    @RequestMapping(value = "/save",method = RequestMethod.POST)
    public Result saveAlarm(@RequestBody AlarmInfo alarmInfo) {
        log.info("告警信息：{}",alarmInfo);
        Integer res = alarmService.saveAlarm(alarmInfo);
        if (res > 0) {
            return Result.success();
        }else {
            return Result.error(500,"告警信息保存失败");
        }
    }

}
