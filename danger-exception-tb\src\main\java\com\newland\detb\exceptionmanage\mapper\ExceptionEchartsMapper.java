package com.newland.detb.exceptionmanage.mapper;

import com.newland.detb.exceptionmanage.entity.EchartsData;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface ExceptionEchartsMapper {

    /**
     * 查询异常治理概述
     * 例如：当月共计治理：4 次异常告警；其中，治理完成：1 个；等待授权：10个；拒绝授权：1 个；异常场景匹配失败，需手动处理：1 个
     * @return
     */
    String summaryOverview();

    /**
     * 30天内每天的治理次数
     * @return
     */
    List<EchartsData> getDataByDay();

    /**
     * 近七天每天的业务异常单治理次数
     * 调整
     * @return
     */
    List<EchartsData> getBusinessExceptionCount();

    /**
     * 近七天对业务异常单的治理分组情况
     * 调整为展示业务异常单治理情况
     * @return
     */
    List<EchartsData> getBusinessExceptionGroup();

    /**
     * 获取异常场景库根据场景分类占比
     * 饼状图
     * @return
     */
    List<EchartsData> getSceneGroup();

    /**
     * 当月治理场景次数前10的天是哪些天
     * @return
     */
    List<EchartsData> getCountTopTen();

    /**
     * 获取治理结果分类-当天
     * 饼状图
     * @return
     */
    List<EchartsData> getGovernanceResGroupInDay();

    /**
     * 获取治理结果分类-当月
     * 饼状图
     * @return
     */
    List<EchartsData> getGovernanceResGroupInMonth();

    /**
     * 获取治理结果分类-全部
     * 包含历史表
     * 饼状图
     * @return
     */
    List<EchartsData> getGovernanceResGroupInAll();

    /**
     * 获取历史告警数据在不同时间段内的治理数据
     * @param type 用于判断是获取系统数据还是业务数据
     * @param time 开始时间
     */
    List<EchartsData> getExceptionDataCountInTimeRange(@Param("type") String type,@Param("time") Integer time);

    /**
     * 获取工单表中数据在不同时间段内的治理数据
     * @param type 判断是系统异常还是业务异常
     * @param time 开始时间
     * @return
     */
    List<EchartsData> getWorkOrderCountInTimeRange(@Param("type") String type,@Param("time") Integer time);

}
