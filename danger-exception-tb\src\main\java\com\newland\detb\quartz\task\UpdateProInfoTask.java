package com.newland.detb.quartz.task;

import com.newland.detb.exceptionmanage.mapper.NoncloudInfoMapper;
import com.newland.detb.exceptionmanage.service.NoncloudInfoService;
import com.newland.detb.util.TimeFormatUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.quartz.JobDetail;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.quartz.PersistJobDataAfterExecution;
import org.springframework.scheduling.quartz.QuartzJobBean;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023-12-13 013 18:26:17
 * @description 执行定期从EBOSS上线工具中更新应用部署信息
 */
@Slf4j
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@Component
@PersistJobDataAfterExecution
public class UpdateProInfoTask extends QuartzJobBean {

    @Resource
    private NoncloudInfoMapper noncloudInfoMapper;

    @Resource
    private NoncloudInfoService noncloudInfoService;

    @Override
    protected void executeInternal(JobExecutionContext jobExecutionContext) throws JobExecutionException {
        log.info("----------------------[定期同步应用部署信息任务执行中]----------------------");
        JobDetail jobDetail = jobExecutionContext.getJobDetail();
        log.info("定时任务名称：{}，定时任务组名：{}，要执行固定的任务描述：{}，执行的当前时间：{}",jobDetail.getKey().getName(),jobDetail.getKey().getGroup(),jobDetail.getDescription(), TimeFormatUtil.getStringTime(new Date()));
        //调用更新方法
        noncloudInfoService.updateProInfo();
    }
}
