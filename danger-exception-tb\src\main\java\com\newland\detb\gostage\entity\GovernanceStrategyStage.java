package com.newland.detb.gostage.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.newland.detb.exceptionmanage.entity.ExceptionScenario;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 异常治理策略信息表
 * @TableName HE_GOVERNANCE_STRATEGY_STAGE
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class GovernanceStrategyStage implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * id
     */
    private Long id;

    /**
     * 异常场景编码
     */
    private String sceneCode;

    /**
     * 治理策略编码
     */
    private String strategyCode;

    /**
     * 备注：描述恢复的策略
     */
    private String remark;

    /**
     * 是否删除，默认0不删除
     */
    private Integer deleted;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date createTime;

    /**
     * 关联的异常场景库信息
     */
    private ExceptionScenario exceptionScenario;
}
