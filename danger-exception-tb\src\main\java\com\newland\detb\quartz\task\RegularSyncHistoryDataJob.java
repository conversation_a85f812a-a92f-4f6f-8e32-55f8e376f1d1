package com.newland.detb.quartz.task;

import com.newland.detb.common.entity.HeExceptionDataBean;
import com.newland.detb.exceptionmanage.entity.ExceptionDataHistory;
import com.newland.detb.exceptionmanage.entity.WorkOrder;
import com.newland.detb.exceptionmanage.entity.WorkOrderHistory;
import com.newland.detb.exceptionmanage.service.ExceptionDataHistoryService;
import com.newland.detb.exceptionmanage.service.ExceptionDataService;
import com.newland.detb.exceptionmanage.service.WorkOrderHistoryService;
import com.newland.detb.exceptionmanage.service.WorkOrderService;
import com.newland.detb.util.TimeFormatUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.quartz.JobDetail;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.quartz.PersistJobDataAfterExecution;
import org.springframework.beans.BeanUtils;
import org.springframework.scheduling.quartz.QuartzJobBean;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.concurrent.CopyOnWriteArrayList;

/**
 * <AUTHOR>
 * @date 2023-06-06 006 11:37:29
 * @description 定期同步历史数据
 */
@Slf4j
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@Component
@PersistJobDataAfterExecution
public class RegularSyncHistoryDataJob extends QuartzJobBean {

    @Resource
    private ExceptionDataHistoryService exceptionDataHistoryService;

    @Resource
    private WorkOrderHistoryService workOrderHistoryService;

    @Resource
    private WorkOrderService workOrderService;

    @Resource
    private ExceptionDataService exceptionDataService;

    @Override
    protected void executeInternal(JobExecutionContext jobExecutionContext) throws JobExecutionException {
        log.info("----------------------[定期同步历史数据任务开始执行]----------------------");
        JobDetail jobDetail = jobExecutionContext.getJobDetail();
        log.info("[定时同步历史数据任务开始执行]任务名称：{}，组名：{}，要执行固定的任务描述：{}，执行的当前时间：{}",jobDetail.getKey().getName(),jobDetail.getKey().getGroup(),jobDetail.getDescription(), TimeFormatUtil.getStringTime(new Date()));
        List<HeExceptionDataBean> heExceptionDataBeans = exceptionDataService.selectExceptionDataForMoveToHistory();
        if (heExceptionDataBeans != null && heExceptionDataBeans.size() > 0) {
            //保存赋值后的对象
            List<ExceptionDataHistory> historyList = new CopyOnWriteArrayList<>();
            for (HeExceptionDataBean dataBean : heExceptionDataBeans) {
                ExceptionDataHistory dataHistory = new ExceptionDataHistory();
                //对象复制
                BeanUtils.copyProperties(dataBean,dataHistory);
                historyList.add(dataHistory);
            }
            //进行数据迁移操作
            int res = exceptionDataHistoryService.migrateData(historyList);
            log.info("[告警工单历史数据迁移完成]---共迁移数据：{}",res);
        }
        //治理工单历史数据进行迁移
        List<WorkOrder> workOrderList = workOrderService.selectWorkOrderDataForMoveToHistory();
        if (workOrderList != null && workOrderList.size() > 0) {
            List<WorkOrderHistory> historyData  = new CopyOnWriteArrayList<>();
            for (WorkOrder workOrder : workOrderList) {
                WorkOrderHistory workOrderHistory = new WorkOrderHistory();
                BeanUtils.copyProperties(workOrder, workOrderHistory);
                historyData.add(workOrderHistory);
            }
            int res = workOrderHistoryService.migrateData(historyData);
            log.info("[治理工单历史数据迁移完成]---共迁移数据：{}",res);
        }
        log.info("[全部历史数据迁移完成]");
    }
}
