package com.newland.detb.lock;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.locks.Condition;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;

/**
 * <AUTHOR>
 * @date 2023-12-12 012 16:41:36
 * @description 自定义锁，将用于在更新生产资料表时，控制其他线程进行等待
 */
@Slf4j
@Configuration
public class DatabaseLock {

    //定义锁
    private final Lock lock = new ReentrantLock();

    //用于控制线程之间协作
    private final Condition condition = lock.newCondition();

    //定义变量判断当前是否有线程正在执行
    private boolean isExecuting = false;

    /**
     * 加锁
     */
    public void lock(String desc,String traceId) {
        //加锁
        lock.lock();
        try {
            while (isExecuting) {
                log.info("{}-[{}]-当前有其他线程正在执行",traceId,desc);
                //执行线程等待
                condition.await();
            }
            isExecuting = true;
        }catch (InterruptedException e) {
            //出现异常及时中断
            Thread.currentThread().interrupt();
        }finally {
            //释放锁
            lock.unlock();
        }
    }

    /**
     * 释放锁
     */
    public void unlock(String desc,String traceId) {
        //加锁
        lock.lock();
        try {
            isExecuting = false;
            log.info("{}-[{}]-释放锁，唤醒其他线程",traceId,desc);
            //唤醒其他等待的线程
            condition.signalAll();
        }finally {
            //释放锁
            lock.unlock();
        }
    }
}
