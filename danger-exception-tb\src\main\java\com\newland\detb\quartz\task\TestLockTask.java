package com.newland.detb.quartz.task;

import com.newland.detb.util.TestLock;
import com.newland.detb.util.TimeFormatUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.quartz.JobDetail;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.quartz.PersistJobDataAfterExecution;
import org.springframework.scheduling.quartz.QuartzJobBean;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023-12-14 014 0:58:51
 * @description 测试在定时任务中使用锁
 */
@Slf4j
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@Component
@PersistJobDataAfterExecution
public class TestLockTask extends QuartzJobBean {

    @Resource
    private TestLock testLock;

    @Override
    protected void executeInternal(JobExecutionContext jobExecutionContext) throws JobExecutionException {
        log.info("----------------------[定时扫描异常治理工单任务执行中]----------------------");
        JobDetail jobDetail = jobExecutionContext.getJobDetail();
        log.info("定时任务名称：{}，定时任务组名：{}，要执行固定的任务描述：{}，执行的当前时间：{}",jobDetail.getKey().getName(),jobDetail.getKey().getGroup(),jobDetail.getDescription(), TimeFormatUtil.getStringTime(new Date()));
        //调用打印方法
        testLock.printInfo("定时任务-timing task");
    }
}
