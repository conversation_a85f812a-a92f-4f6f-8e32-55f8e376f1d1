package com.newland.detb.exceptionmanage.service;

import com.newland.detb.exceptionmanage.entity.MessageConfig;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【HE_MESSAGE_CONFIG(短信配置表)】的数据库操作Service
* @createDate 2023-05-17 11:18:14
*/
public interface MessageConfigService {

    /**
     * 插入messageConfig
     * @param messageConfig
     * @return
     */
    int insert(MessageConfig messageConfig);

    /**
     * 根据id删除
     * @param id
     * @return
     */
    int deleteById(Long id);

    /**
     * 更新messageConfig
     * @param messageConfig
     * @return
     */
    int update(MessageConfig messageConfig);

    /**
     * 根据id查询
     * @param id
     * @return
     */
    MessageConfig selectById(Long id);

    /**
     * 查询全部
     * @return
     */
    List<MessageConfig> selectAll(String name, String phone, String email, String position);

    /**
     * 查询出is_send是0，即需要发送同步短信的人
     * @return
     */
    List<MessageConfig> selectIsSend();

    /**
     * 查询需要发送的内部负责人信息
     * @return
     */
    List<MessageConfig> selectInnerList();

}
