package com.newland.detb.common;

/**
 * 定义响应类型
 */
public interface ResEnum {

    int success_code = 200;

    int error_code = 500;

    int jsch_error_code = 510;

    //excel导入导出操作异常
    int excel_error_code = 511;

    //异常场景库操作异常
    int scenario_error_code = 512;

    //异常治理工单表操作异常
    int exception_data_error = 513;

    //异常治理工单表操作异常
    int quartz_error = 515;

    //数据校验失败
    int check_error_code = 555;

    //前端传递的参数类型不匹配失败
    int argument_error_code = 516;

    //文件格式校验失败
    int file_check_error_code = 517;

    //文件大小超出最大限制
    int file_size_error = 518;

    //原子信息错误
    int atomic_error = 519;

    //认证错误
    int service_error = 401;
}
