package com.newland.detb.exceptionmanage.async;

import com.newland.detb.common.entity.HeExceptionDataBean;
import com.newland.detb.exceptionmanage.entity.*;
import com.newland.detb.exceptionmanage.mapper.ExceptionProgressMapper;
import com.newland.detb.exceptionmanage.service.WorkOrderService;
import com.newland.detb.quartz.service.HeExceptionDataService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023-05-10 010 16:00:22
 * @description 生成治理工单异步任务
 */
// @Lazy
@Slf4j
@Component
public class AsyncGenerateWorkOrderTask extends BaseAsyncTask{

    @Resource
    private WorkOrderService workOrderService;

    @Lazy
    @Resource
    private AsyncAuthTask asyncAuthTask;

    /**
     * 治理工单生成
     * @param heExceptionDataBean 异常工单表数据
     * @param exceptionScenario 异常场景库数据
     * @param governanceStrategy 治理策略
     * @param stepFlag 为true代表治理步骤生成成功，false表示治理步骤生成失败
     * @param recoveryCommand 恢复命令
     */
    @Async("taskExecutor")
    @Override
    public void executeAsync(String traceId,HeExceptionDataBean heExceptionDataBean, ExceptionScenario exceptionScenario, GovernanceStrategy governanceStrategy, Boolean stepFlag, String recoveryCommand) {
        WorkOrder workOrder = null;
        try {
            asyncPrintLogInfo(traceId,"生成治理工单");
            updateHeExceptionDataWithRemark(heExceptionDataBean,2,"正在生成治理工单");
            updateProgress(heExceptionDataBean,2,3,"doing","正在生成治理工单");

            workOrder = getWorkOrder(heExceptionDataBean.getDataId());
            workOrder.setSceneCode(exceptionScenario.getSceneCode().toString());
            workOrder.setStrategyRemark(governanceStrategy.getRemark());
            workOrder.setHostName("0".equals(heExceptionDataBean.getEnvironment()) ? "云主机" : heExceptionDataBean.getHostIp());
            workOrder.setAppName(heExceptionDataBean.getHomeModule() != null ? heExceptionDataBean.getHomeModule() : heExceptionDataBean.getProcessName());
            workOrder.setRecoveryCommand(stepFlag ? recoveryCommand : "未生成");
            workOrder.setTraceId(traceId);
            workOrder.setGovernanceRes(stepFlag ? "治理步骤拼接成功" : "治理步骤拼接失败");
            workOrderService.update(workOrder);
            //更新成功，打印日志
            asyncPrintLogInfo(traceId,2,3,"治理工单表数据保存成功：" + workOrder);
            updateHeExceptionDataWithRemark(heExceptionDataBean,2,"生成治理工单成功,跟踪ID:" + traceId);
            updateProgress(heExceptionDataBean,2,3,"success","生成治理工单成功,跟踪ID:" + traceId);
            if (stepFlag) {
                //进行异常治理执行操作（扫描治理工单-授权确认-执行治理-是否触发单项巡检）
                updateHeExceptionDataWithRemark(heExceptionDataBean,3,"治理工单扫描成功");
                updateProgress(heExceptionDataBean,3,1,"success","治理工单扫描成功");
                asyncPrintLogInfo(traceId,"治理步骤拼接成功，进行授权判断");
                //判断是否需要进行授权
                asyncAuthTask.executeAsync(traceId,heExceptionDataBean, exceptionScenario, workOrder);
            }else {
                asyncPrintLogInfo(traceId,"治理步骤拼接失败，治理结束");
                //更新告警表状态
                updateHeExceptionDataWithRemark(heExceptionDataBean,5,"治理步骤拼接失败，治理结束");
            }
        } catch (Exception e) {
            e.printStackTrace();
            asyncPrintErrorLogInfo(traceId,2,3,"治理工单表数据保存失败：" + workOrder);
            updateHeExceptionDataWithRemark(heExceptionDataBean,2,"生成治理工单失败");
            updateProgress(heExceptionDataBean,2,3,"fail","生成治理工单失败");
            updateWorkOrderWhenFailCase(heExceptionDataBean.getDataId(),"治理工单信息补充失败");
        }
    }

    /**
     * 业务异常单-治理工单生成
     * @param heExceptionDataBean 异常工单表数据
     */
    @Async("taskExecutor")
    public void executeBusinessAsync(String traceId, HeExceptionDataBean heExceptionDataBean, BusinessType businessType) {
        asyncPrintLogInfo(traceId,"业务异常单-生成治理工单");
        updateHeExceptionDataWithRemark(heExceptionDataBean,2,"业务异常单-正在生成治理工单");
        updateProgress(heExceptionDataBean,2,3,"doing","业务异常单-正在生成治理工单");

        WorkOrder workOrder = getWorkOrder(heExceptionDataBean.getDataId());
        workOrder.setSceneCode("BUSINESS");
        workOrder.setStrategyRemark("执行异常单恢复处理");
        workOrder.setHostName(Objects.equals(heExceptionDataBean.getEnvironment(),"0") ? "EBOSS云业务" : "EBOSS非云业务");
        workOrder.setAppName(heExceptionDataBean.getProcessName());
        workOrder.setRecoveryCommand("调度业务异常单处理框架");
        workOrder.setTraceId(traceId);
        int res = workOrderService.update(workOrder);

        if (res == 1) {
            asyncPrintLogInfo(traceId,2,3,"业务异常单-治理工单表数据保存成功：" + workOrder);
            updateHeExceptionDataWithRemark(heExceptionDataBean,2,"业务异常单-生成治理工单成功,跟踪ID:" + traceId);
            updateProgress(heExceptionDataBean,2,3,"success","业务异常单-生成治理工单成功,跟踪ID:" + traceId);
        }else {
            asyncPrintErrorLogInfo(traceId,2,3,"业务异常单-治理工单表数据保存失败：" + workOrder);
            updateHeExceptionDataWithRemark(heExceptionDataBean,2,"业务异常单-生成治理工单失败");
            updateProgress(heExceptionDataBean,2,3,"fail","业务异常单-生成治理工单失败");
            updateWorkOrderWhenFailCase(heExceptionDataBean.getDataId(),"业务异常单-治理工单信息补充失败");
        }
        //进行异常治理执行操作（扫描治理工单-授权确认-执行治理-是否触发单项巡检）
        updateHeExceptionDataWithRemark(heExceptionDataBean,3,"业务异常单-治理工单扫描成功");
        updateProgress(heExceptionDataBean,3,1,"success","业务异常单-治理工单扫描成功");
        //判断是否需要进行授权
        asyncAuthTask.executeBusinessAsync(traceId,heExceptionDataBean, workOrder,businessType);
    }
}
