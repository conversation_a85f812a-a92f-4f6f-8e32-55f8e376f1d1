package com.newland.detb.gostage.entity;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.newland.detb.common.converter.ExceptionScenarioAttributionConverter;
import com.newland.detb.common.converter.ExceptionScenarioEnvConverter;
import com.newland.detb.common.converter.ExceptionScenarioIsAuthOrIsInspectionConverter;
import com.newland.detb.common.converter.ExceptionScenarioIsOverrideConverter;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 异常场景表(he_exception_scenario_stage)实体类
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ExceptionScenarioStage implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * id
     */
    @ExcelIgnore
    private Long id;

    /**
     * 模块
     */
    @NotNull(message = "模块不能为空")
    @Length(min = 0, max = 25,message = "模块属性的最大长度为25位")
    @ExcelProperty("模块")
    private String module;

    /**
     * 归属
     */
    @NotNull(message = "归属不能为空")
    @Length(min = 0, max = 25,message = "归属属性的最大长度为25位")
    @ExcelProperty("归属")
    private String attribution;

    /**
     * 类型标识
     */
    @NotNull(message = "类型不能为空")
    @Length(min = 0, max = 25,message = "类型属性的最大长度为25位")
    @ExcelProperty("类型")
    private String type;

    /**
     * 环境：0代表云环境，1代表非云环境
     */
    @NotNull(message = "类型不能为空")
    @Max(value = 1,message = "环境只能是云和非云")
    @Min(value = 0,message = "环境只能是云和非云")
    @ExcelProperty(value = "环境",converter = ExceptionScenarioEnvConverter.class)
    private Integer env;

    /**
     * 异常大类
     */
    // @NotNull(message = "异常大类不能为空")
    @Length(min = 0, max = 100,message = "异常大类属性的最大长度为100位")
    @ExcelProperty("异常大类")
    private String parentClass;

    /**
     * 异常子类
     */
    @NotNull(message = "异常子类不能为空")
    @Length(min = 0, max = 100,message = "异常子类属性的最大长度为100位")
    @ExcelProperty("异常子类")
    private String subClass;

    /**
     * 异常来源
     */
    @NotNull(message = "异常来源不能为空")
    @Length(min = 0, max = 25,message = "异常来源属性的最大长度为25位")
    @ExcelProperty("异常来源")
    private String source;

    /**
     * 是否覆盖：0代表覆盖，1代表未覆盖
     */
    @NotNull(message = "是否覆盖选项不能为空")
    @Max(value = 1,message = "只能选择是或者否")
    @Min(value = 0,message = "只能选择是或者否")
    @ExcelProperty(value = "是否覆盖",converter = ExceptionScenarioIsOverrideConverter.class)
    private Integer isOverride;

    /**
     * 异常归属：0代表系统，1代表业务
     */
    @NotNull(message = "异常归属分类不能为空")
    @Max(value = 1,message = "异常归属分类只能是系统或者业务")
    @Min(value = 0,message = "异常归属分类只能是系统或者业务")
    @ExcelProperty(value = "异常归属分类",converter = ExceptionScenarioAttributionConverter.class)
    private Integer exceptionAttribution;

    /**
     * 异常场景编码
     */
    @NotNull(message = "场景编码不能为空")
    @Max(value = 32767,message = "场景编码最大只能是32767")
    @Min(value = 0,message = "场景编码最小只能为0")
    @ExcelProperty("场景编码")
    private Integer sceneCode;

    /**
     * 是否删除，默认0，未删除，1代表删除
     */
    @ExcelIgnore
    private Integer deleted;

    /**
     * 创建时间
     */
    @ExcelProperty("创建时间")
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date createTime;

    /**
     * 异常描述
     */
    // @NotNull(message = "异常描述不能为空")
    @ExcelProperty("异常描述")
    private String exceptionDesc;

    /**
     * 恢复方案：大概步骤
     */
    @NotNull(message = "恢复方案不能为空")
    @ExcelProperty("恢复方案")
    private String recoveryStep;

    @NotNull(message = "是否需要授权选项不能为空")
    @Max(value = 1,message = "只能选择是或否")
    @Min(value = 0,message = "只能选择是或否")
    @ExcelProperty(value = "是否需要授权",converter = ExceptionScenarioIsAuthOrIsInspectionConverter.class)
    private Integer isAuth;

//    @NotNull(message = "是否需要触发单项巡检选项不能为空")
//    @Max(value = 1,message = "只能选择是或否")
//    @Min(value = 0,message = "只能选择是或否")
//    @ExcelProperty(value = "是否需要触发单项巡检",converter = ExceptionScenarioIsAuthOrIsInspectionConverter.class)
    private Integer isInspection;

//    @NotNull(message = "治理策略不能为空")
//    @Length(min = 0, max = 25,message = "治理策略属性的最大长度为25位")
    @ExcelProperty(value = "治理策略")
    private String strategy;

    /**
     * 业务异常处置手段
     */
    @ExcelIgnore
    private List<BusinessHandle> businessHandles;
}
