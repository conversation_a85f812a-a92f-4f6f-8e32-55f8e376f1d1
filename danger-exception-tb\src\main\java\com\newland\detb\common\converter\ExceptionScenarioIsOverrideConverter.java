package com.newland.detb.common.converter;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.CellData;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.property.ExcelContentProperty;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023-04-19 019 11:41:00
 * @description 异常场景库是否覆盖converter
 */
public class ExceptionScenarioIsOverrideConverter implements Converter<Integer> {

    //0代表覆盖，1代表未覆盖
    private static final Integer overrideNum = 0;
    private static final Integer nonOverrideNum = 1;

    private static final String override = "是";
    private static final String nonOverride = "否";


    @Override
    public Class supportJavaTypeKey() {
        return Integer.class;
    }

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        return CellDataTypeEnum.STRING;
    }

    /**
     * 导入
     * @param cellData
     * @param excelContentProperty
     * @param globalConfiguration
     * @return
     * @throws Exception
     */
    @Override
    public Integer convertToJavaData(CellData cellData, ExcelContentProperty excelContentProperty, GlobalConfiguration globalConfiguration) throws Exception {
        String value = cellData.getStringValue();
        if (Objects.equals(value,override)) {
            return overrideNum;
        }else if (Objects.equals(value,nonOverride)) {
            return nonOverrideNum;
        }else {
            return null;
        }
    }

    /**
     * 导出
     * @param s
     * @param excelContentProperty
     * @param globalConfiguration
     * @return
     * @throws Exception
     */
    @Override
    public CellData convertToExcelData(Integer s, ExcelContentProperty excelContentProperty, GlobalConfiguration globalConfiguration) throws Exception {
        if (Objects.equals(s,overrideNum)) {
            return new CellData<>(override);
        }else if (Objects.equals(s,nonOverrideNum)) {
            return new CellData<>(nonOverride);
        }else {
            return new CellData<>("");
        }
    }
}
