package com.newland.detb.util;

import com.jcraft.jsch.ChannelExec;
import com.jcraft.jsch.JSch;
import com.jcraft.jsch.JSchException;
import com.jcraft.jsch.Session;
import com.newland.detb.common.ResEnum;
import com.newland.detb.exception.JschException;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.Optional;
import java.util.Properties;

/**
 * <AUTHOR>
 * @date 2023-05-17 017 17:22:37
 * @description 二次确认脚本远程调用工具类
 */
@Data
@Slf4j
@Component
@ConfigurationProperties(prefix = "script")
public class JschScriptUtil {

    private String host;

    private String username;

    private String password;

    private int port;

    private String scpath;

    private String governpath;

    /**
     * 创建jsch的session
     * @return
     */
    public Session createSession() {
        JSch jSch = new JSch();
        Session session = null;
        log.info("【二次确认和执行治理】-开始请求连接远程主机：{}",host);
        try {
            session = jSch.getSession(username,host,port);
            //设置主机密码
            session.setPassword(password);
            Properties config = new Properties();
            //去掉首次连接确认
            config.put("StrictHostKeyChecking","no");
            session.setConfig(config);
            //设置超时时间：3秒
            session.setTimeout(3000);
            //连接
            session.connect();
            //获取连接结果
            boolean result = session.isConnected();
            if (result) {
                log.info("【二次确认和执行治理】-与远程主机连接成功：{}",host);
                return session;
            }
        } catch (JSchException e) {
            e.printStackTrace();
            log.error("【二次确认和执行治理】-远程连接：{} ,时发生异常：{}",host,e.getMessage());
            // throw new JschException(ResEnum.jsch_error_code,"连接远程主机失败");
        }
        return null;
    }

    /**
     * 调用二次确认脚本
     * @return
     */
    public Boolean executeSCScript(String command) {
        ChannelExec channel = null;
        //获取session对象
        Session session = createSession();
        if (!Optional.ofNullable(session).isPresent()) {
            log.error("【二次确认】------>jsch的session对象获取失败");
            throw new JschException(ResEnum.jsch_error_code,"连接远程主机失败");
        }
        //创建sftp通道
        try {
            channel = (ChannelExec) session.openChannel("exec");
            //cd /home/<USER>/liuning/ebosshome/eboss/app_nas/mt_tools/eboss_monitor/smsend && 6001-001.sh xx xx
            command = "cd " + scpath + " && ./" + command;
            log.info("【二次确认】-二次确认操作执行的命令：{}",command);
            channel.setCommand(command);
            channel.connect();
            //错误输出
            channel.setErrStream(System.err);
            //获取命令执行结果
            InputStream inputStream = channel.getInputStream();
            BufferedReader br = new BufferedReader(new InputStreamReader(inputStream));
            String line = br.readLine();
            log.info("【二次确认】------>执行脚本后内容：{}",line);
            return true;
        } catch (JSchException | IOException e) {
            e.printStackTrace();
            log.error("【二次确认】------>远程调用脚本失败：{}",e.getMessage());
            throw new JschException(ResEnum.jsch_error_code,"执行二次确认操作失败");
        } finally {
            if (channel != null && channel.isConnected()) {
                channel.disconnect();
                log.info("【二次确认】------>channel通道关闭完成");
            }
            if (session.isConnected()) {
                session.disconnect();
            }
            log.info("【二次确认】------>jsch的session关闭完成");
        }
    }

    /**
     * 调用执行治理脚本
     * @return
     */
    public Boolean executeGovernScript(String command) {
        ChannelExec channel = null;
        //获取session对象
        Session session = createSession();
        if (!Optional.ofNullable(session).isPresent()) {
            log.error("【执行治理】------>jsch的session对象获取失败");
            throw new JschException(ResEnum.jsch_error_code,"连接远程主机失败");
        }
        //创建sftp通道
        try {
            channel = (ChannelExec) session.openChannel("exec");
            //cd /home/<USER>/liuning/ebosshome/eboss/app_nas/mt_tools/eboss_monitor/smsend && 6001-001.sh xx xx
            command = "cd " + scpath + " && ./" + command;
            log.info("【执行治理】-执行治理执行的命令：{}",command);
            channel.setCommand(command);
            channel.connect();
            //错误输出
            channel.setErrStream(System.err);
            //获取命令执行结果
            InputStream inputStream = channel.getInputStream();
            BufferedReader br = new BufferedReader(new InputStreamReader(inputStream));
            String line = br.readLine();
            log.info("【执行治理】------>执行脚本后内容：{}",line);
            return true;
        } catch (JSchException | IOException e) {
            e.printStackTrace();
            log.error("【执行治理】------>远程调用脚本失败：{}",e.getMessage());
            throw new JschException(ResEnum.jsch_error_code,"执行二次确认操作失败");
        } finally {
            if (channel != null && channel.isConnected()) {
                channel.disconnect();
                log.info("【执行治理】------>channel通道关闭完成");
            }
            if (session.isConnected()) {
                session.disconnect();
            }
            log.info("【执行治理】------>jsch的session关闭完成");
        }
    }
}
