<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.newland.detb.tools.alarmMonitoring.mapper.AlarmMapper">

    <!-- 保存告警信息 -->
    <insert id="saveAlarm">
        insert into eboss_selfsms_except
        (
            system_name,
            module_type,
            node_name,
            zinode_name,
            warm_info,
            start_time,
            end_time,
            remark
        )
        values
            (
                #{systemName,jdbcType=VARCHAR},
                #{moduleType,jdbcType=VARCHAR},
                #{nodeName,jdbcType=VARCHAR},
                #{zinodeName,jdbcType=VARCHAR},
                #{warmInfo,jdbcType=VARCHAR},
                #{startTime,jdbcType=TIMESTAMP},
                #{endTime,jdbcType=TIMESTAMP},
                #{remark,jdbcType=VARCHAR}
            )
    </insert>
</mapper>