package com.newland.detb.exceptionmanage.entity;

import java.io.Serializable;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 异常治理进度表
 * @TableName HE_EXCEPTION_PROGRESS
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ExceptionProgress implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * id
     */
    private Long id;

    /**
     * 工单id
     */
    private Long dataId;

    /**
     * 父进度
     */
    private Integer parentProgress;

    /**
     * 子进度
     */
    private Integer childProgress;

    /**
     * 状态：success、fail、waiting、doing
     */
    private String execStatus;

    /**
     * 结果
     */
    private String execResult;

    /**
     * 是否删除，默认0表示未删除
     */
    private Integer deleted;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date createTime;

    /**
     * 结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date finishTime;

}