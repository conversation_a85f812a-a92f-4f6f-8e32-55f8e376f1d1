package com.newland.detb.exceptionmanage.async;

import com.newland.detb.common.entity.HeExceptionDataBean;
import com.newland.detb.exceptionmanage.entity.MessageConfig;
import com.newland.detb.exceptionmanage.entity.MessageInfo;
import com.newland.detb.exceptionmanage.service.MessageConfigService;
import com.newland.detb.exceptionmanage.service.MessageInfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023-09-12 012 23:27:07
 * @description 异步将短信内容保存到he_message_info中
 */
@Slf4j
@Component
public class AsyncSendMessage extends BaseAsyncTask{

    @Resource
    private MessageInfoService messageInfoService;

    @Resource
    private MessageConfigService messageConfigService;

    /**
     * 拼接生成内部messageInfo对象
     * @param heExceptionDataBean
     * @param innerMessage
     * @param traceId 唯一id
     */
    @Async("taskExecutor")
    public void spliceInnerMessageInfo(HeExceptionDataBean heExceptionDataBean, String innerMessage,String traceId) {
        asyncPrintLogInfo(traceId,1,2,"由于未匹配到异常场景定义，拼接发送内部短信:" + innerMessage);
        //查询短信配置表中的需要发送的内部负责人信息
        List<MessageConfig> messageConfigs = messageConfigService.selectInnerList();
        if (messageConfigs == null) {
            return;
        }
        for (MessageConfig messageConfig : messageConfigs) {
            //拼接内部短信信息
            MessageInfo messageInfo = new MessageInfo();
            messageInfo.setDataId(heExceptionDataBean.getDataId());
            messageInfo.setName(messageConfig.getName());
            messageInfo.setPhone(messageConfig.getPhone());
            messageInfo.setEmail(messageConfig.getEmail());
            messageInfo.setGovernLevel("1".equals(heExceptionDataBean.getAlarmLevel()) ? "严重告警" : "重要告警");
            messageInfo.setMessage("异常告警id：" + heExceptionDataBean.getDataId() + ",环境：" + (Objects.equals(heExceptionDataBean.getEnvironment(), "0") ? "云环境" : "非云环境") + ",告警关键字：" + heExceptionDataBean.getAlarmType() + ",治理异常原因：" + innerMessage + "【异常治理工具消息同步】");
            messageInfo.setIsInner(1);
            //保存
            int res = messageInfoService.insertInner(messageInfo);
            //打印输出是否插入成功
            asyncPrintLogInfo(traceId,1,2,"由于未匹配到异常场景定义，拼接发送内部短信:" + innerMessage + (res == 1 ? "[短信保存成功]" : "[短信保存失败]"));
        }
    }

    /**
     * 拼接生成内部messageInfo对象
     * @param heExceptionDataBean
     * @param innerMessage
     * @param traceId 唯一id
     */
    @Async("taskExecutor")
    public void spliceInnerMessageInfoCaseProduct(HeExceptionDataBean heExceptionDataBean, String innerMessage,String traceId) {
        asyncPrintLogInfo(traceId,2,2,"由于未匹配到生产资料定义，拼接发送内部短信:" + innerMessage);
        //查询短信配置表中的需要发送的内部负责人信息
        List<MessageConfig> messageConfigs = messageConfigService.selectInnerList();
        if (messageConfigs == null) {
            return;
        }
        for (MessageConfig messageConfig : messageConfigs) {
            //拼接内部短信信息
            MessageInfo messageInfo = new MessageInfo();
            messageInfo.setDataId(heExceptionDataBean.getDataId());
            messageInfo.setName(messageConfig.getName());
            messageInfo.setPhone(messageConfig.getPhone());
            messageInfo.setEmail(messageConfig.getEmail());
            messageInfo.setGovernLevel("1".equals(heExceptionDataBean.getAlarmLevel()) ? "严重告警" : "重要告警");
            messageInfo.setMessage("异常告警id：" + heExceptionDataBean.getDataId() + ",环境：" + (Objects.equals(heExceptionDataBean.getEnvironment(), "0") ? "云环境" : "非云环境") + ",告警关键字：" + heExceptionDataBean.getAlarmType() + ",治理异常原因：" + innerMessage + "【异常治理工具消息同步】");
            messageInfo.setIsInner(1);
            //保存
            int res = messageInfoService.insertInner(messageInfo);
            //打印输出是否插入成功
            asyncPrintLogInfo(traceId,1,2,"由于未匹配到生产资料定义，拼接发送内部短信:" + innerMessage + (res == 1 ? "[短信保存成功]" : "[短信保存失败]"));
        }
    }
}
