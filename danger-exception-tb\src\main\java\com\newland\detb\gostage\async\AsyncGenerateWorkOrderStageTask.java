package com.newland.detb.gostage.async;

import com.newland.detb.common.entity.HeExceptionDataBean;
import com.newland.detb.exceptionmanage.async.AsyncAuthTask;
import com.newland.detb.exceptionmanage.async.BaseAsyncTask;
import com.newland.detb.exceptionmanage.entity.BusinessType;
import com.newland.detb.exceptionmanage.entity.ExceptionScenario;
import com.newland.detb.exceptionmanage.entity.GovernanceStrategy;
import com.newland.detb.exceptionmanage.entity.WorkOrder;
import com.newland.detb.exceptionmanage.service.WorkOrderService;
import com.newland.detb.gostage.entity.ExceptionScenarioStage;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023-05-10 010 16:00:22
 * @description 生成治理工单异步任务
 */
// @Lazy
@EqualsAndHashCode(callSuper = true)
@Slf4j
@Component
@Data
public class AsyncGenerateWorkOrderStageTask extends BaseAsyncTask {

    @Resource
    private WorkOrderService workOrderService;

    @Lazy
    @Resource
    private AsyncAuthStageTask asyncAuthStageTask;

    @Value("${business.name}")
    private String businessName;

    /**
     * 治理工单生成
     * @param heExceptionDataBean 异常工单表数据
     * @param exceptionScenario 异常场景库数据
     */
    @Async("taskExecutor")
    @Override
    public void executeAsync(String traceId, HeExceptionDataBean heExceptionDataBean, ExceptionScenarioStage exceptionScenario, int randomNumber) {
        WorkOrder workOrder = null;
        try {
            asyncPrintLogInfo(traceId,"生成治理工单");
            updateHeExceptionDataWithRemark(heExceptionDataBean,2,"正在生成治理工单");
            updateProgress(heExceptionDataBean,2,3,"doing","正在生成治理工单");

            workOrder = getWorkOrder(heExceptionDataBean.getDataId());
            workOrder.setSceneCode(businessName);
            workOrder.setStrategyRemark(exceptionScenario.getBusinessHandles().get(randomNumber).getHandle());
            workOrder.setHostName("0".equals(heExceptionDataBean.getEnvironment()) ? "云主机" : heExceptionDataBean.getHostIp());
            workOrder.setAppName(heExceptionDataBean.getHomeModule() != null ? heExceptionDataBean.getHomeModule() : heExceptionDataBean.getProcessName());
            workOrder.setRecoveryCommand("生成成功");
            workOrder.setTraceId(traceId);
            workOrder.setGovernanceRes("治理步骤拼接成功");
            workOrderService.update(workOrder);
            //更新成功，打印日志
            asyncPrintLogInfo(traceId,2,3,"治理工单表数据保存成功：" + workOrder);
            updateHeExceptionDataWithRemark(heExceptionDataBean,2,"生成治理工单成功,跟踪ID:" + traceId);
            updateProgress(heExceptionDataBean,2,3,"success","生成治理工单成功,跟踪ID:" + traceId);

            //进行异常治理执行操作（扫描治理工单-授权确认-执行治理-是否触发单项巡检）
            updateHeExceptionDataWithRemark(heExceptionDataBean,3,"治理工单扫描成功");
            updateProgress(heExceptionDataBean,3,1,"success","治理工单扫描成功");
            asyncPrintLogInfo(traceId,"治理步骤拼接成功，进行授权判断");
            //判断是否需要进行授权
            asyncAuthStageTask.executeAsync(traceId,heExceptionDataBean, exceptionScenario, workOrder,randomNumber);
        } catch (Exception e) {
            e.printStackTrace();
            asyncPrintErrorLogInfo(traceId,2,3,"治理工单表数据保存失败：" + workOrder);
            updateHeExceptionDataWithRemark(heExceptionDataBean,2,"生成治理工单失败");
            updateProgress(heExceptionDataBean,2,3,"fail","生成治理工单失败");
            updateWorkOrderWhenFailCase(heExceptionDataBean.getDataId(),"治理工单信息补充失败");
        }
    }
}
