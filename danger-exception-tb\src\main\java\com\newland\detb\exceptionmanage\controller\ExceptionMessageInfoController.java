package com.newland.detb.exceptionmanage.controller;

import cn.com.nlsoft.cas.client.annotation.CasPermissionRequired;
import cn.hutool.core.util.DesensitizedUtil;
import cn.hutool.crypto.SecureUtil;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.newland.detb.common.Result;
import com.newland.detb.exceptionmanage.entity.MessageConfig;
import com.newland.detb.exceptionmanage.entity.MessageInfo;
import com.newland.detb.exceptionmanage.service.MessageInfoService;
import com.newland.detb.log.annotation.SysLog;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.validator.constraints.Length;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023-05-23 023 18:31:35
 * @description 短信汇总信息
 */
@Slf4j
@RestController
@RequestMapping(value = "/messageInfo")
@Validated
public class ExceptionMessageInfoController {

    @Resource
    private MessageInfoService messageInfoService;

    @GetMapping("getAll")
    @CasPermissionRequired("aimt.message_info.query")
    public Result getMessageConfig(@RequestParam(value = "currentPage",defaultValue = "1") int currentPage,
                                   @RequestParam(value = "pageSize",defaultValue = "10") int pageSize,
                                   @RequestParam(value = "dataId",required = false) @Min(value = 0,message = "告警工单ID值错误") @Max(value = 9223372036854774807L,message = "告警工单ID超出最大值") Long dataId,
                                   @RequestParam(value = "name",required = false,defaultValue = "") @Length(min = 0,max = 6,message = "请求的姓名超长") String name,
                                   @RequestParam(value = "phone",required = false,defaultValue = "") @Length(min = 0,max = 11,message = "请求的手机号超长") String phone,
                                   @RequestParam(value = "email",required = false,defaultValue = "") @Length(min = 0,max = 30,message = "请求的邮箱地址超长") String email,
                                   @RequestParam(value = "message",required = false,defaultValue = "") @Length(min = 0,max = 50,message = "请求的消息内容超长")String message,
                                   @RequestParam(value = "governLevel",required = false,defaultValue = "") @Length(min = 0,max = 10,message = "请求的告警级别超长") String governLevel,
                                   @RequestParam(value = "isComplete",required = false) @Min(value = 0,message = "是否完成只能选择是或否") @Max(value = 1,message = "是否完成只能选择是或否") Integer isComplete) throws CloneNotSupportedException {
        PageHelper.startPage(currentPage,pageSize);
        List<MessageInfo> messageInfos = messageInfoService.selectAll(dataId, name, phone, email, message, governLevel, isComplete);

        PageInfo<MessageInfo> pageInfo = new PageInfo<>(messageInfos);
        Map<String, Object> map = new ConcurrentHashMap<>();
        map.put("total",pageInfo.getTotal());
        map.put("list",pageInfo.getList());
        return Result.success(map);
    }

}
