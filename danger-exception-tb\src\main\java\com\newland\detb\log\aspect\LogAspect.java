package com.newland.detb.log.aspect;

import com.alibaba.fastjson.JSONObject;
import com.google.gson.Gson;
import com.newland.detb.common.ResEnum;
import com.newland.detb.common.Result;
import com.newland.detb.exception.ExceptionScenarioException;
import com.newland.detb.log.annotation.SysLog;
import com.newland.detb.log.entity.ConstraintString;
import com.newland.detb.log.entity.LogInfo;
import com.newland.detb.log.service.LogInfoService;
import com.newland.detb.log.util.IPUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.common.utils.SecurityUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.HashSet;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2023-04-06 006 10:00:59
 * @description 系统日志切面类处理
 */
@Slf4j
@Aspect
@Component
public class LogAspect {

    @Resource
    private LogInfoService logInfoService;

    @Pointcut("@annotation(com.newland.detb.log.annotation.SysLog)")
    public void logPointCut() {

    }

    @Around("logPointCut()")
    public Object around(ProceedingJoinPoint point) throws Throwable {
        long beginTime = System.currentTimeMillis();
        Object result = null;
        try {
            //执行方法
            result = point.proceed();
        } catch (Throwable e) {
            e.printStackTrace();
            System.out.println("方法执行发生异常：" + e.getMessage());
            //保存日志
            saveSysLog(point, 0L, true);
            log.info("异常：{}",e.toString());
            //对数据约束ConstraintViolationException校验失败的单独抛出异常，由全局异常进行捕获处理
            if (e.toString().contains("ConstraintViolationException")) {
                Set<ConstraintString> constraintViolations = new HashSet<>();
                ConstraintString constraintString = new ConstraintString();
                //抛出的异常格式：delDictInfo.name: 属性名称长度为1到3位
                String[] split = e.getMessage().split(":");
                for (String s : split) {
                    System.out.println("===" + s);
                }
                constraintString.setMsg(split.length > 1 ? split[1] : split[0]);
                // constraintString.setMsg(split[1]);
                constraintViolations.add(constraintString);
                throw new ConstraintViolationException(constraintViolations);
            }
            return Result.error(500,e.getMessage());
        }
        //执行时长(毫秒)
        long time = System.currentTimeMillis() - beginTime;

        //保存日志
        saveSysLog(point, time,false);

        return result;
    }

    /**
     * 执行日志的保存方法
     * @param joinPoint 切入点信息
     * @param time 执行耗时，当方法出现异常时，执行耗时为0
     * @param flag 判断方法是否出现异常，当flag为true时，表示方法执行出现异常，需要给方法的描述上添加请求异常
     */
    private void saveSysLog(ProceedingJoinPoint joinPoint, long time, Boolean flag) {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();

        // SysLogEntity sysLog = new SysLogEntity();
        LogInfo logInfo = new LogInfo();
        SysLog syslog = method.getAnnotation(SysLog.class);
        if(syslog != null){
            String operation = syslog.value();
            //对flag进行判断，判断是否是出现异常的情况
            if (flag) {
                operation += " ->请求异常";
            }
            //注解上的描述
            logInfo.setOperation(operation);
        }

        //请求的方法名
        String className = joinPoint.getTarget().getClass().getName();
        String methodName = signature.getName();
        logInfo.setMethod(className + "." + methodName + "()");

        //请求的参数
        Object[] args = joinPoint.getArgs();
        for (Object arg : args) {
            if (arg instanceof MultipartFile) {
                MultipartFile file = (MultipartFile) arg;
                // 处理上传文件参数，例如获取文件名
                String fileName = file.getOriginalFilename();
                // 将文件名转换为JSON格式的字符串
                String json = JSONObject.toJSONString(fileName);
                // 在这里可以进行日志记录或其他操作
                log.info("Uploaded file: {}",json);
                logInfo.setParams(json);
                break;
            }else {
                try{
                    // String params = new Gson().toJson(args[0]);
                    String params = JSONObject.toJSONString(args[0]);
                    log.info("【请求的参数】: {}", params);
                    logInfo.setParams(params);
                }catch (Exception e){
                    log.error("请求方法时的参数获取失败：{}",e.getMessage());
                }
            }
        }

        //获取request
        // HttpServletRequest request = HttpContextUtils.getHttpServletRequest();
        HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
        //设置IP地址
        logInfo.setIp(IPUtils.getIpAddr(request));

        //用户名
        String username = "";
        // String username = ((SysUserEntity) SecurityUtils.getSubject().getPrincipal()).getUserName();
        // if ((SecurityUtils.getSubject().getPrincipal()) == null) {
        //     String json = new Gson().toJson(args[0]);
        //     JSONObject jsonObject = JSONObject.parseObject(json);
        //     // JSONArray jsonArray = jsonObject.getJSONArray("username");
        //     // String name = jsonArray.getJSONObject(0).get("username").toString();
        //     String name = jsonObject.get("username").toString();
        //     username = name;
        // }else {
        //     username = ((SysUserEntity) SecurityUtils.getSubject().getPrincipal()).getUserName();
        // }
        //判断是否为登录请求，登录请求在shiro中不存在用户信息
        // if (username == null) {
        // 	username = new Gson().toJson(args[0]);
        // }
        logInfo.setUsername(username);

        logInfo.setExecuteTime(time);
        // logInfo.setCreateTime(new Date());
        //保存系统日志
        int save = logInfoService.save(logInfo);
        if (save == 1) {
            log.info("日志入库成功：{}",logInfo);
        }else {
            log.error("日志入库失败");
        }
    }
}
