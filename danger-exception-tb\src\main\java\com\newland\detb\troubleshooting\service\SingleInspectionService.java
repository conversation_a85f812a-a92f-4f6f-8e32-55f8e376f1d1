package com.newland.detb.troubleshooting.service;

import com.newland.detb.troubleshooting.entity.SingleInspectionBean;


import java.util.List;
import java.util.Map;

public interface SingleInspectionService {
    //单项巡检
    List<SingleInspectionBean> querySingleInspection();
    /**
     * 分页查询接口
     *
     * @param pageSize 自定义，统一分页查询请求
     * @return PageResult 自定义，统一分页查询结果
     */
    List<SingleInspectionBean> findPage(Integer pageNum, Integer pageSize, String kpi_template_id);
}
