<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.newland.detb.exceptionmanage.mapper.WorkOrderMapper">

    <resultMap id="BaseResultMap" type="com.newland.detb.exceptionmanage.entity.WorkOrder">
            <id property="id" column="ID" jdbcType="DECIMAL"/>
            <result property="dataId" column="DATA_ID" jdbcType="DECIMAL"/>
            <result property="sceneCode" column="SCENE_CODE" jdbcType="VARCHAR"/>
            <result property="strategyRemark" column="STRATEGY_REMARK" jdbcType="VARCHAR"/>
            <result property="env" column="ENV" jdbcType="VARCHAR"/>
            <result property="hostName" column="HOST_NAME" jdbcType="VARCHAR"/>
            <result property="appName" column="APP_NAME" jdbcType="VARCHAR"/>
            <result property="isAuth" column="IS_AUTH" jdbcType="DECIMAL"/>
            <result property="isInspection" column="IS_INSPECTION" jdbcType="DECIMAL"/>
            <result property="deleted" column="DELETED" jdbcType="DECIMAL"/>
            <result property="createTime" column="CREATE_TIME" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID,DATA_ID,SCENE_CODE,
        STRATEGY_REMARK,ENV,HOST_NAME,
        APP_NAME,IS_AUTH,IS_INSPECTION,
        DELETED,CREATE_TIME,RECOVERY_COMMAND
    </sql>
    <insert id="insert">
        INSERT INTO he_work_order
        (
        data_id,
        scene_code,
        strategy_remark,
        env,
        host_name,
        app_name,
        recovery_command,
        governance_res)
        values
        (
        #{dataId,jdbcType=BIGINT},
        #{sceneCode,jdbcType=VARCHAR},
        #{strategyRemark,jdbcType=VARCHAR},
        #{env,jdbcType=VARCHAR},
        #{hostName,jdbcType=VARCHAR},
        #{appName,jdbcType=VARCHAR},
        #{recoveryCommand,jdbcType=VARCHAR},
        #{governanceRes,jdbcType=VARCHAR})
    </insert>
    <update id="update">
        update he_work_order
        <set>
            <if test="dataId!=null">data_id = #{dataId,jdbcType=BIGINT},</if>
            <if test="sceneCode!=null">scene_code = #{sceneCode,jdbcType=VARCHAR},</if>
            <if test="strategyRemark!=null">strategy_remark = #{strategyRemark,jdbcType=VARCHAR},</if>
            <if test="env!=null">env = #{env,jdbcType=VARCHAR},</if>
            <if test="hostName!=null">host_name = #{hostName,jdbcType=VARCHAR},</if>
            <if test="appName!=null">app_name = #{appName,jdbcType=VARCHAR},</if>
            <if test="recoveryCommand!=null">recovery_command = #{recoveryCommand,jdbcType=VARCHAR},</if>
            <if test="isAuth!=null">is_auth = #{isAuth,jdbcType=INTEGER},</if>
            <if test="isInspection!=null">is_inspection = #{isInspection,jdbcType=INTEGER},</if>
            <if test="governanceRes!=null">governance_res = #{governanceRes,jdbcType=VARCHAR},</if>
            <if test="traceId!=null and traceId != ''">trace_id = #{traceId,jdbcType=VARCHAR}</if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateIsAuth">
        update he_work_order
        <set>
            <if test="isAuth!=null">is_auth = #{isAuth,jdbcType=INTEGER}</if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateIsInspection">
        update he_work_order
        <set>
            <if test="isInspection!=null">is_inspection = #{isInspection,jdbcType=INTEGER}</if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateGovernanceResInt">
        update he_work_order
        <set>
            <if test="governanceRes!=null">governance_res = #{governanceRes,jdbcType=VARCHAR}</if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateTraceIdByDataId">
        update he_work_order
        <set>
            <if test="traceId != null">trace_id = #{traceId,jdbcType=VARCHAR}</if>
        </set>
        where data_id = #{dataId,jdbcType=BIGINT}
    </update>
    <delete id="deleteById">
        delete from he_work_order where id = #{id,jdbcType=BIGINT}
    </delete>
    <select id="selectById" resultType="com.newland.detb.exceptionmanage.entity.WorkOrder">
        select id,
               data_id,
               scene_code,
               strategy_remark,
               env,
               host_name,
               app_name,
               recovery_command,
               is_auth,
               is_inspection,
               governance_res,
               deleted,
               create_time,
               trace_id
        from he_work_order
        where id = #{id,jdbcType=BIGINT}
        order by id desc
    </select>
    <select id="selectAll" resultType="com.newland.detb.exceptionmanage.entity.WorkOrder">
        select id,
               data_id,
               scene_code,
               strategy_remark,
               env,
               host_name,
               app_name,
               recovery_command,
               is_auth,
               is_inspection,
               governance_res,
               deleted,
               create_time
        from he_work_order
        order by id desc
    </select>
    <select id="getWorkOrderByDataId" resultType="com.newland.detb.exceptionmanage.entity.WorkOrder">
        select id,
               data_id,
               scene_code,
               strategy_remark,
               env,
               host_name,
               app_name,
               recovery_command,
               is_auth,
               is_inspection,
               governance_res,
               deleted,
               create_time
        from he_work_order
        where data_id = #{dataId}
        order by id desc
    </select>
    <select id="findPage" resultType="com.newland.detb.exceptionmanage.entity.WorkOrder" >
        select id,
            data_id,
            scene_code,
            strategy_remark,
            env,
            host_name,
            app_name,
            is_auth,
            is_inspection,
            recovery_command,
            create_time,
            governance_res,
            trace_id
        from he_work_order
        <where>
            <if test="dataId != null">
                 data_id = #{dataId}
            </if>
            <if test="appName != null and appName != ''">
                and app_name like concat('%',#{appName},'%')
            </if>
            <if test="env != null and env != ''">
                and env =  #{env}
            </if>
            <if test="isAuth != null">
                and is_auth = #{isAuth}
            </if>
            <if test="isInspection != null">
                and is_inspection = #{isInspection}
            </if>
        </where>
        order by id desc
    </select>
    <select id="selectWorkOrderDataForMoveToHistory" resultType="com.newland.detb.exceptionmanage.entity.WorkOrder">
        SELECT
        a.id,
        a.data_id,
        a.scene_code,
        a.strategy_remark,
        a.env,
        a.host_name,
        a.app_name,
        a.recovery_command,
        a.is_auth,
        a.is_inspection,
        a.governance_res,
        a.deleted,
        a.create_time
        FROM
        he_work_order a
        WHERE
        a.create_time &lt; DATE_SUB(DATE_FORMAT(CURDATE(), '%Y-%m-01'), INTERVAL 3 MONTH)
    </select>
</mapper>
