package com.newland.detb.gostage.service.impl;

import com.newland.detb.common.ResEnum;
import com.newland.detb.exception.ExceptionScenarioException;
import com.newland.detb.exceptionmanage.entity.ExceptionScenario;
import com.newland.detb.exceptionmanage.entity.GovernanceStrategy;
import com.newland.detb.exceptionmanage.entity.SceneInspection;
import com.newland.detb.exceptionmanage.mapper.ExceptionScenarioMapper;
import com.newland.detb.exceptionmanage.mapper.SceneInspectionMapper;
import com.newland.detb.exceptionmanage.service.GovernanceStrategyService;
import com.newland.detb.gostage.entity.ExceptionScenarioStage;
import com.newland.detb.gostage.entity.GovernanceStrategyStage;
import com.newland.detb.gostage.mapper.ExceptionScenarioStageMapper;
import com.newland.detb.gostage.service.GovernanceStrategyStageService;
import com.newland.detb.util.mapper.ExcelBaseService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR>
 * @date 2023-04-19 019 14:22:16
 * @description 用于异常场景库的导入操作
 */
@Slf4j
@Service
public class ExceptionScenarioExcelStageServiceImpl implements ExcelBaseService {

    @Resource
    private ExceptionScenarioStageMapper exceptionScenarioStageMapper;

    /**
     * 治理策略表（保存异常场景库信息时，同步保存对应治理策略）
     */
    @Resource
    private GovernanceStrategyStageService governanceStrategyStageService;

    @Resource
    private SceneInspectionMapper sceneInspectionMapper;

    /**
     * 实现excel的保存方法
     * @param data
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer save(List<?> data) {
        AtomicInteger counter = new AtomicInteger(0);
        for (Object item : data) {
            log.info("保存前收到的异常场景库信息：{}",item);
            ExceptionScenarioStage scenario = (ExceptionScenarioStage) item;
            //查询数据库中最大的异常编码
            Integer maxSceneCode = exceptionScenarioStageMapper.getMaxSceneCode();
            if (maxSceneCode == null) {
                maxSceneCode = 1000;
            }
            scenario.setSceneCode(maxSceneCode);
            //保存异常场景库信息
            int res = exceptionScenarioStageMapper.insert(scenario);
            if (res < 0) {
                log.error("在保存异常场景库时发生异常，当前数据：{}",item);
                throw new ExceptionScenarioException(ResEnum.scenario_error_code,"导入异常场景库信息保存时异常");
            }
            //保存异常治理策略信息
            GovernanceStrategyStage strategy = new GovernanceStrategyStage();
            // strategy.setSceneCode(((ExceptionScenario) item).getSceneCode().toString());
            // strategy.setStrategyCode(((ExceptionScenario) item).getSceneCode().toString());
            strategy.setSceneCode(String.valueOf(maxSceneCode));
            strategy.setStrategyCode(String.valueOf(maxSceneCode));
            strategy.setRemark(((ExceptionScenario) item).getStrategy());
            int strategyRes = governanceStrategyStageService.insert(strategy);
            if (strategyRes < 0) {
                log.error("在保存异常治理策略时发生异常，当前数据：{}",strategy);
                throw new ExceptionScenarioException(ResEnum.scenario_error_code,"同步保存异常治理策略时异常");
            }
            //如果需要触发单项巡检，则需要在对应的关联表中插入对应数据
            try {
                if (scenario.getIsInspection() == 0) {
                    SceneInspection sceneInspection = new SceneInspection();
                    sceneInspection.setSceneCode(scenario.getSceneCode());
                    sceneInspection.setSceneName(scenario.getType());
                    sceneInspection.setInspectionCode(scenario.getSceneCode());
                    int saveResult = sceneInspectionMapper.insert(sceneInspection);
                    if (saveResult < 1) {
                        log.error("异常场景和单项巡检关联表保存失败");
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
                throw new ExceptionScenarioException(ResEnum.scenario_error_code,"导入异常场景库信息时保存单项巡检关联数据异常");
            }
            log.info("异常场景库信息保存成功：{}",scenario);
            log.info("异常治理策略保存成功：{}",strategy);
            counter.incrementAndGet();
        }
        log.info("所有异常场景库信息数据已经保存完成，需要保存的条数：{}，成功保存的条数：{}",data.size(),counter.get());
        return counter.get();
    }
}
