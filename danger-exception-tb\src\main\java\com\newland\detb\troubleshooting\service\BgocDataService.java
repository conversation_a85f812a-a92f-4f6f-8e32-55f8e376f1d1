package com.newland.detb.troubleshooting.service;


import com.newland.detb.troubleshooting.entity.BgocPerfDetailBean;
import com.newland.detb.troubleshooting.entity.BgocRecordBean;


/**
* <AUTHOR>
* @description 针对bgoc的数据操作Service
* @createDate 2023-04-17 16:54:20
*/
public interface BgocDataService {

    //插入BGOC记录
    int insertHeBgocRecord(BgocRecordBean bgocRecordBean);
    //插入BGOC明细数据
    int insertHeBgocPerfDetail(BgocPerfDetailBean bgocPerfDetailBean);
}
