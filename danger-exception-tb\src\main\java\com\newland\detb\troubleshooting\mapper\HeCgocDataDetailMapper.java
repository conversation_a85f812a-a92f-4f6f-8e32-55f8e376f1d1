package com.newland.detb.troubleshooting.mapper;

import com.newland.detb.troubleshooting.entity.CgocDataDetailBean;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface HeCgocDataDetailMapper {
    int insertHeCgocDataDetailMapper(CgocDataDetailBean cgocDataDetailBean);

    /**
     * 通过resourceId和当前日期判断数据是否存在
     * 判断resourceId,metric,subReName是否相同（因为存在resourceId是相同的不同组数据，因此不可以只靠resourceId识别）
     * 其中subResName：部件
     * metric：指标英文名
     * @param cgocDataDetailBean
     * @param time
     * @return
     */
    int ifExistsCgocData(@Param("cgocDataDetailBean") CgocDataDetailBean cgocDataDetailBean, @Param("time") String time);

    /**
     * 根据resourceId更新C-GOC数据
     * @param cgocDataDetailBean
     * @return
     */
    int updateCgocDataDetailMapper( @Param("cgocDataDetailBean") CgocDataDetailBean cgocDataDetailBean,@Param("time") String time);

    /**
     * 定时清理C-GOC数据
     */
    void truncateCGocTable();
}
