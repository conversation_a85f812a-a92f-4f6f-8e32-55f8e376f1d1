package com.newland.detb.util;

import com.newland.detb.common.ResEnum;
import com.newland.detb.exception.FileCheckException;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.poifs.filesystem.FileMagic;

import java.io.BufferedInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023-05-26 026 18:12:28
 * @description 检查文件后缀是否符合要求
 */
@Slf4j
public class CheckFileSuffixUtil {

    /**
     * 校验文件后缀格式是否正确
     * 注意可能会获取报错 getFileMagic() only operates on streams which support mark(int)
     * 原因InputStream中markSupported方法返回值为false造成的，BufferedInputStream中返回值是true，所以改为InputStream is = new BufferedInputStream(file.getInputStream())
     * @param inputStream
     * @return
     */
    public static boolean isExcelFile(InputStream inputStream) {
        boolean result = false;
        try {
            FileMagic fileMagic = FileMagic.valueOf(new BufferedInputStream(inputStream));
            log.info("FileMagic: {}" ,fileMagic.name());
            //EXCEL2003/EXCEL2007
            if (Objects.equals(fileMagic, FileMagic.OLE2) || Objects.equals(fileMagic, FileMagic.OOXML)) {
                result = true;
            }
        } catch (IOException e) {
            e.printStackTrace();
            throw new FileCheckException(ResEnum.file_check_error_code,"校验文件后缀格式失败");
        }
        return result;
    }
}
