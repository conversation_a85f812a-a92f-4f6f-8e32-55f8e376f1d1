<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.newland.detb.exceptionmanage.mapper.DataPersistToDiskMapper">

    <select id="selectPersistExceptionHistory"
            resultType="com.newland.detb.exceptionmanage.entity.ExceptionDataHistory">
        SELECT *
        FROM he_work_order_history
        WHERE STR_TO_DATE(DATE_FORMAT(create_time, '%Y%m'), '%Y%m') &lt;=
        STR_TO_DATE(DATE_FORMAT(DATE_SUB(DATE_FORMAT(CURDATE(), '%Y-%m-01'), INTERVAL 3 MONTH), '%Y%m'), '%Y%m');
    </select>
    <select id="selectPersistWorkOrderHistory"
            resultType="com.newland.detb.exceptionmanage.entity.WorkOrderHistory">
        SELECT *
        FROM he_exception_data_history
        WHERE STR_TO_DATE(DATE_FORMAT(create_time, '%Y%m'), '%Y%m') &lt;=
        STR_TO_DATE(DATE_FORMAT(DATE_SUB(DATE_FORMAT(CURDATE(), '%Y-%m-01'), INTERVAL 3 MONTH), '%Y%m'), '%Y%m')
    </select>
</mapper>