package com.newland.detb.exceptionmanage.util;

import com.newland.detb.exceptionmanage.log.GovernanceLogger;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.Arrays;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023-06-12 012 17:59:42
 * @description 用于在Linux环境中执行shell脚本
 */
@Slf4j
@Component
public class ProcessUtil {

    //自定义治理日志配置
    private static final Logger LOG = GovernanceLogger.LOG;

    /**
     * 异常二次确认脚本路径
     */
    @Value("${governance.confirm}")
    private String governanceConfirmPath;

    /**
     * 异常治理恢复脚本路径
     */
    @Value("${governance.recover}")
    private String governanceRecoverPath;

    /**
     * 执行异常二次确认脚本
     * 结果：execute_status:END
     *      execute_rezult:0(或者1)
     * @param tmpParams xx.sh 应用名 type 环境 机器
     * @return
     */
    public boolean executeConfirmScript(String tmpParams,String traceId) {
        //判断一下后缀是否为/
        // command = governanceConfirmPath + (governanceConfirmPath.endsWith("/") ? "" : "/") + command;
        String[] command = {"sh","-c",governanceConfirmPath + (governanceConfirmPath.endsWith("/") ? "" : "/") + tmpParams};
        LOG.info("[{}]-[二次确认]---[需要执行的命令]：[{}]，开始判断系统环境", traceId, Arrays.toString(command));
        String osName = System.getProperty("os.name").toLowerCase();
        LOG.info("[{}]-[二次确认]---当前系统环境：{}",traceId,osName);
        //记录最终的状态和退出码，表名脚本是否执行结束
        String executeStatus = "";
        String executeResult = "";
        //linux
        if (osName.contains("nix") || osName.contains("nux")) {
            // 当前环境是Linux
            try {
                Process process = Runtime.getRuntime().exec(command);

                // 读取命令输出
                BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
                String line;
                while ((line = reader.readLine()) != null) {
                    LOG.info("[{}]-[二次确认]---命令输出:{}",traceId, line);
                    //识别到退出状态
                    if (line.contains("execute_status")) {
                        LOG.info("[{}]-[二次确认]---执行命令状态:{}",traceId, line);
                        if (line.trim().substring(line.trim().indexOf(":") + 1).contains("END")) {
                            executeStatus = "END";
                        }
                    }
                    //识别到执行结果
                    if (line.contains("execute_result")) {
                        LOG.info("[{}]-[二次确认]---执行命令结果:{}",traceId, line);
                        String[] split = line.trim().split(":");
                        if ("execute_result".equals(split[0]) && "0".equals(split[1])) {
                            executeResult = "0";
                        }else if ("execute_result".equals(split[0]) && "1".equals(split[1])) {
                            executeResult = "1";
                        }else if ("execute_result".equals(split[0]) && "2".equals(split[1])) {
                            executeResult = "2";
                        }else {
                            executeResult = "2";
                        }
                    }
                }
                // 等待命令执行完成
                int exitCode = process.waitFor();
                LOG.info("[{}]-[二次确认]---shell脚本调用完成，退出码：{}",traceId,exitCode);
                LOG.info("[{}]-[二次确认]---execute_result:{},execute_status:{}",traceId,executeResult,executeStatus);
                // 1代表执行成功，0和2代表执行失败
                return (("END".equals(executeStatus)) && ("1".equals(executeResult)));
            } catch (IOException e) {
                e.printStackTrace();
                return false;
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        } else {
            // 非Linux环境
            LOG.info("[{}]-[二次确认]---当前环境不是Linux，执行其他操作...",traceId);
            return true;
        }
        return false;
    }

    /**
     * 执行日志关键字二次确认
     * @param tmpParams
     * @param traceId
     * @return
     */
    public boolean executeLogKeyWordsConfirmScript(String tmpParams,String traceId,String alarmTypeName) {
        //判断一下后缀是否为/
        // 增加转义字符
        String[] command = {"sh","-c",governanceConfirmPath + (governanceConfirmPath.endsWith("/") ? "" : "/") + tmpParams};
        LOG.info("[{}]-[{}-二次确认]---[需要执行的命令]：[{}]，开始判断系统环境", alarmTypeName,traceId,Arrays.toString(command));
        String osName = System.getProperty("os.name").toLowerCase();
        LOG.info("[{}]-[{}-二次确认]---当前系统环境：{}",alarmTypeName,traceId,osName);
        //日志关键字最终返回结果为：0|1
        String executeResult = "";
        //linux
        if (osName.contains("nix") || osName.contains("nux")) {
            // 当前环境是Linux
            try {
                Process process = Runtime.getRuntime().exec(command);

                // 读取命令输出
                BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
                String line;
                while ((line = reader.readLine()) != null) {
                    LOG.info("[{}]-[{}-二次确认]---命令输出:{}",alarmTypeName, traceId,line);
                    //识别到退出状态
                    if ("1".equals(line)) {
                        executeResult = "1";
                    }else {
                        executeResult = "0";
                    }
                }
                // 等待命令执行完成
                int exitCode = process.waitFor();
                LOG.info("[{}]-[{}-二次确认]---shell脚本调用完成，退出码：{}",alarmTypeName,traceId,exitCode);
                LOG.info("[{}]-[{}-二次确认]---execute_result:{}",alarmTypeName,traceId,executeResult);
                // 1代表执行成功，0和2代表执行失败
                return "1".equals(executeResult);
            } catch (IOException e) {
                e.printStackTrace();
                return false;
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        } else {
            // 非Linux环境
            LOG.info("[{}]-[{}-二次确认]---当前环境不是Linux，执行其他操作...",alarmTypeName,traceId);
            return true;
        }
        return false;
    }

    /**
     * 执行异常治理恢复脚本
     * 结果：execute_status:END
     *      execute_rezult:0(或者1)
     * @param tmpParams xx.sh 应用名 type 环境 机器
     * @return
     */
    public boolean executeRecoverScript(String tmpParams,String traceId) {
        //判断一下后缀是否为/
        // command = governanceRecoverPath + (governanceRecoverPath.endsWith("/") ? "" : "/") + command;
        String[] command = {"sh","-c",governanceRecoverPath + (governanceRecoverPath.endsWith("/") ? "" : "/") + tmpParams};
        LOG.info("[治理恢复]---[需要执行的命令]：[{}]，开始判断系统环境",Arrays.toString(command));
        String osName = System.getProperty("os.name").toLowerCase();
        LOG.info("[治理恢复]---当前系统环境：{}",osName);
        //记录最终的状态和退出码，表名脚本是否执行结束
        String executeStatus = "";
        String executeResult = "";
        //linux
        if (osName.contains("nix") || osName.contains("nux")) {
            // 当前环境是Linux
            try {
                Process process = Runtime.getRuntime().exec(command);

                // 读取命令输出
                BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
                String line;
                while ((line = reader.readLine()) != null) {
                    //识别到退出状态
                    if (line.contains("execute_status")) {
                        LOG.info("[异常治理恢复]---执行命令状态:{}", line);
                        if (line.trim().substring(line.trim().indexOf(":") + 1).contains("END")) {
                            executeStatus = "END";
                        }
                    }
                    //识别到执行结果
                    if (line.contains("execute_result")) {
                        LOG.info("[异常治理恢复]---执行命令结果:{}", line);
                        String[] split = line.trim().split(":");
                        if ("execute_result".equals(split[0]) && "0".equals(split[1])) {
                            executeResult = "0";
                        }else if ("execute_result".equals(split[0]) && "1".equals(split[1])) {
                            executeResult = "1";
                        }else if ("execute_result".equals(split[0]) && "2".equals(split[1])) {
                            executeResult = "2";
                        }else {
                            executeResult = "2";
                        }
                    }
                }
                // 等待命令执行完成
                int exitCode = process.waitFor();
                LOG.info("[治理恢复]---shell脚本调用完成，退出码：{}",exitCode);
                // 1代表执行成功，0和2代表执行失败
                return (("END".equals(executeStatus)) && ("1".equals(executeResult)));
            } catch (IOException e) {
                e.printStackTrace();
                return false;
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        } else {
            // 非Linux环境
            LOG.info("[治理恢复]---当前环境不是Linux，执行其他操作...");
            return true;
        }
        return false;
    }

    /**
     * 执行修复业务异常场景脚本
     * @param tmpParams
     * @param traceId
     * @return
     */
    public boolean executeBusinessScript(String tmpParams,String traceId) {
        //入参直接为/ebosshome/eboss/app_nas/mt_tools/test.sh
        String[] command = {"sh","-c",tmpParams};
        LOG.info("[业务异常单-治理恢复]---[需要执行的命令]：[{}]，开始判断系统环境",Arrays.toString(command));
        String osName = System.getProperty("os.name").toLowerCase();
        LOG.info("[业务异常单-治理恢复]---当前系统环境：{}",osName);
        //记录最终的状态和退出码，表名脚本是否执行结束
        String executeStatus = "";
        String executeResult = "";
        //linux
        if (osName.contains("nix") || osName.contains("nux")) {
            // 当前环境是Linux
            try {
                Process process = Runtime.getRuntime().exec(command);

                // 读取命令输出
                BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
                String line;
                while ((line = reader.readLine()) != null) {
                    //识别到退出状态
                    if (line.contains("execute_status")) {
                        LOG.info("[业务异常单-异常治理恢复]---执行命令状态:{}", line);
                        if (line.trim().substring(line.trim().indexOf(":") + 1).contains("END")) {
                            executeStatus = "END";
                        }
                    }
                    //识别到执行结果
                    if (line.contains("execute_result")) {
                        LOG.info("[业务异常单-异常治理恢复]---执行命令结果:{}", line);
                        String[] split = line.trim().split(":");
                        if ("execute_result".equals(split[0]) && "0".equals(split[1])) {
                            executeResult = "0";
                        }else if ("execute_result".equals(split[0]) && "1".equals(split[1])) {
                            executeResult = "1";
                        }else if ("execute_result".equals(split[0]) && "2".equals(split[1])) {
                            executeResult = "2";
                        }else {
                            executeResult = "2";
                        }
                    }
                }
                // 等待命令执行完成
                int exitCode = process.waitFor();
                LOG.info("[业务异常单-治理恢复]---shell脚本调用完成，退出码：{}",exitCode);
                // 1代表执行成功，0和2代表执行失败
                return (("END".equals(executeStatus)) && ("1".equals(executeResult)));
            } catch (IOException e) {
                e.printStackTrace();
                return false;
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        } else {
            // 非Linux环境
            LOG.info("[业务异常单-治理恢复]---当前环境不是Linux，执行其他操作...");
            return true;
        }
        return false;
    }

    /**
     * 使用ProcessBuilder实现
     * @param tmpCommand
     * @return
     */
    public boolean processBuilderExec(String tmpCommand) {
        String[] command = {
                "sh",
                "-c",
                governanceConfirmPath + (governanceConfirmPath.endsWith("/") ? "" : "/") + tmpCommand
        };
        ProcessBuilder builder = new ProcessBuilder(command);
        try {
            Process process = builder.start();
            // 获取命令执行的输出和错误流
            InputStream inputStream = process.getInputStream();
            InputStream errorStream = process.getErrorStream();
            //记录最终的状态和退出码，表名脚本是否执行结束
            String executeStatus = "";
            String executeResult = "";
            // 处理输出和错误流（示例中只是简单地打印输出）
            BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(inputStream));
            // 读取命令输出
            String line;
            while ((line = bufferedReader.readLine()) != null) {
                log.info("[二次确认]---命令输出:{}", line);
                //识别到退出状态
                if (line.contains("execute_status")) {
                    log.info("[二次确认]---执行命令状态:{}", line);
                    if (line.trim().substring(line.trim().indexOf(":") + 1).contains("END")) {
                        executeStatus = "END";
                    }
                }
                //识别到执行结果
                if (line.contains("execute_result")) {
                    log.info("[二次确认]---执行命令结果:{}", line);
                    String[] split = line.trim().split(":");
                    if ("execute_result".equals(split[0]) && "0".equals(split[1])) {
                        executeResult = "0";
                    }else if ("execute_result".equals(split[0]) && "1".equals(split[1])) {
                        executeResult = "1";
                    }else if ("execute_result".equals(split[0]) && "2".equals(split[1])) {
                        executeResult = "2";
                    }else {
                        executeResult = "2";
                    }
                }
            }
            // 等待命令执行完成
            int exitCode = process.waitFor();
            log.info("[二次确认]---shell脚本调用完成，退出码：{}",exitCode);
            // 1代表执行成功，0和2代表执行失败
            return (("END".equals(executeStatus)) && ("1".equals(executeResult)));
        } catch (IOException | InterruptedException e) {
            e.printStackTrace();
        }
        return true;
    }
}
