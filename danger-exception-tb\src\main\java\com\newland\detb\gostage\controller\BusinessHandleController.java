package com.newland.detb.gostage.controller;

import com.newland.detb.common.Result;
import com.newland.detb.gostage.entity.BusinessHandle;
import com.newland.detb.gostage.service.BusinessHandleService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping("/stage/businessHandle")
public class BusinessHandleController {

    @Resource
    private BusinessHandleService businessHandleService;

    /**
     * 新增业务场景异常分析及处置方案
     * @param businessHandle
     * @return
     */
    @PostMapping("/save")
    public Result save(@RequestBody BusinessHandle businessHandle) {
        return Result.success(businessHandleService.save(businessHandle));
    }

    /**
     * 更新业务场景异常分析及处置方案
     * @param businessHandle
     * @return
     */
    @PutMapping("/update")
    public Result update(@RequestBody BusinessHandle businessHandle) {
        return Result.success(businessHandleService.update(businessHandle));
    }

    /**
     * 根据id删除业务场景异常分析及处置方案
     * @param id
     * @return
     */
    @DeleteMapping("/delete/{id}")
    public Result delete(@PathVariable("id") Long id) {
        return Result.success(businessHandleService.delete(id));
    }

    /**
     * 根据id查询业务场景异常分析及处置方案
     * @param id
     * @return
     */
    @GetMapping("/query/{id}")
    public Result queryById(@PathVariable("id") Long id) {
        return Result.success(businessHandleService.queryById(id));
    }

    /**
     * 根据父id查询业务场景异常分析及处置方案列表
     * @param pId
     * @return
     */
    @GetMapping("/list/{pId}")
    public Result queryListByPId(@PathVariable("pId") Long pId) {
        return Result.success(businessHandleService.queryListByPId(pId));
    }

    /**
     * 查询所有业务场景异常分析及处置方案
     * @return
     */
    @GetMapping("/list")
    public Result queryList() {
        return Result.success(businessHandleService.queryList());
    }
}
