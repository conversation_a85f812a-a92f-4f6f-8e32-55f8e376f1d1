package com.newland.detb.troubleshooting.entity;


import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import java.util.Date;
import java.util.Map;
/**
 * @Author: xumengfan
 * @CreateTime: 2023-04-20  13:25
 * @Description: TODO
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class CgocDataDetailBean {
        private String devType;
        private String resourceId;
        private String subResName;
        private String instance;
        private String metricName;
        private String ciCode;
        private String bmcIpv4;
        private String regionName;
        private String businessName;
        private String resourcePoolName;
        private String mgrIpv4;
        private String classId;
        private String tenantName;
        private String areaName;
        private String metric;
        private String vendor;
        private String osType;
        private String podName;
        private String busiIpv4;
        private String deviceModel;
        private Date timestamp;
        private Double val;
        private Map<String, Object> addInfo;

}
