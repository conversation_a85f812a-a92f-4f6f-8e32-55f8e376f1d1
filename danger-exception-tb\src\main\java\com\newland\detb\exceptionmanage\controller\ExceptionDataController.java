package com.newland.detb.exceptionmanage.controller;

import cn.com.nlsoft.cas.client.annotation.CasPermissionRequired;
import com.alibaba.excel.EasyExcel;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.newland.detb.common.ExcelListener;
import com.newland.detb.common.ResEnum;
import com.newland.detb.common.Result;
import com.newland.detb.common.entity.HeExceptionDataBean;
import com.newland.detb.exception.EasyExcelException;
import com.newland.detb.exceptionmanage.entity.ExceptionDataVO;
import com.newland.detb.exceptionmanage.service.ExceptionDataService;
import com.newland.detb.log.annotation.SysLog;
import com.newland.detb.util.ExcelUtil;
import com.newland.detb.util.mapper.ExcelBaseService;
import com.newland.detb.util.mapper.ExcelSaveServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.annotations.Param;
import org.hibernate.validator.constraints.Length;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.Max;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @date 2023-04-17 017 15:13:46
 * @description 用于对工单表的相关操作
 */
@Slf4j
@RestController
@RequestMapping(value = "/exceptionData")
@Validated
public class ExceptionDataController {

    @Resource
    private ExceptionDataService exceptionDataService;

    @Resource
    private ExcelSaveServiceImpl excelSaveService;

    /**
     * 未封装工具类的导入导出
     * @param response
     */
    @GetMapping("export")
    public void getExceptionData(HttpServletResponse response) {
        List<HeExceptionDataBean> beans = exceptionDataService.queryExceptionDataBeans();
        log.info("获取到了全部的异常治理工单信息条数：{}",beans.size());
        //设置文本内省
        // response.setContentType("application/vnd.ms-excel");
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        //设置字符编码
        response.setCharacterEncoding("utf-8");
        try {
        //设置响应头
        String fileName = URLEncoder.encode("异常治理工单信息", "UTF-8").replaceAll("\\+", "%20");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
        log.info("开始使用EasyExcel导出数据");
        EasyExcel.write(response.getOutputStream(),HeExceptionDataBean.class).sheet("异常治理工单信息").doWrite(beans);
        } catch (IOException e) {
            e.printStackTrace();
            throw new EasyExcelException(ResEnum.excel_error_code,"异常治理工单信息导出异常");
        }
    }

    /**
     * 封装了工具类后的导出
     * @param response
     */
    @GetMapping("export2")
    public void getExceptionData2(HttpServletResponse response) {
        List<HeExceptionDataBean> beans = exceptionDataService.queryExceptionDataBeans();
        log.info("获取到了全部的异常治理工单信息条数：{}",beans.size());
        ExcelUtil.exportDataToExcel(response,"异常治理工单表信息","异常治理工单表信息","工单信息",HeExceptionDataBean.class,beans);
    }

    /**
     * 封装了工具类后的导入
     * @param file
     */
    @GetMapping("import")
    public void importData(MultipartFile file) {
        ExcelUtil.importData(file,HeExceptionDataBean.class,"测试数据导入",new ExcelListener<>(excelSaveService));
        // ExcelUtil.importData(file,HeExceptionDataBean.class,"测试数据导入",new ExcelListener<>(excelBaseService));
    }

    @GetMapping("getAll")
    @CasPermissionRequired("aimt.exception.query")
    public Result getAllExceptionData(@RequestParam(value = "currentPage",defaultValue = "1") int currentPage,@RequestParam(value = "pageSize",defaultValue = "10") int pageSize) {
        PageHelper.startPage(currentPage,pageSize);
        List<HeExceptionDataBean> beans = exceptionDataService.queryExceptionDataBeans();
        PageInfo<HeExceptionDataBean> pageInfo = new PageInfo<>(beans);
        log.info("获取到了全部的异常治理工单信息条数：{}",pageInfo.getTotal());
        return Result.success(pageInfo);
    }

    /**
     * 分页查询全部告警工单表数据
     * @param currentPage
     * @param pageSize
     * @param dataId
     * @param env
     * @param homeModule
     * @param status
     * @param alarmLevel
     * @return
     */
    @GetMapping("getDataProgress")
    @CasPermissionRequired("aimt.exception.query")
    public Result getAllExceptionDataWithProgress(@RequestParam(value = "currentPage",defaultValue = "1") int currentPage,
                                                  @RequestParam(value = "pageSize",defaultValue = "10") int pageSize,
                                                  @RequestParam(value = "dataId",required = false) @Max(value = 9223372036854774807L,message = "告警ID的取值超过最大值") Long dataId,
                                                  @RequestParam(value = "env",required = false,defaultValue = "") @Length(min = 0,max = 1,message = "环境参数错误") String env,
                                                  @RequestParam(value = "keyWords",required = false,defaultValue = "") @Length(min = 0,max = 50,message = "告警关键字参数错误") String keyWords,
                                                  @RequestParam(value = "status",required = false,defaultValue = "") @Length(min = 0,max = 1,message = "治理状态参数错误") String status,
                                                  @RequestParam(value = "alarmLevel",required = false,defaultValue = "") @Length(min = 0,max = 1,message = "告警等级参数错误") String alarmLevel) {
        PageHelper.startPage(currentPage,pageSize);
        List<ExceptionDataVO> exceptionDataVOS = exceptionDataService.selectExceptionDataWithProgress(dataId,env,keyWords,status,alarmLevel);
        PageInfo<ExceptionDataVO> pageInfo = new PageInfo<>(exceptionDataVOS);
        log.info("获取到了全部的异常治理工单信息条数：{}",pageInfo.getTotal());
        Map<String ,Object> map = new ConcurrentHashMap<>();
        map.put("list",pageInfo.getList());
        map.put("total",pageInfo.getTotal());
        return Result.success(map);
    }

}
