package com.newland.detb.exceptionmanage.service;

import com.newland.detb.exceptionmanage.entity.NoncloudInfo;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【HE_NONCLOUD_INFO(非云生产资料信息表)】的数据库操作Service
* @createDate 2023-05-04 15:56:12
*/
public interface NoncloudInfoService {

    /**
     * 保存非云生产资料信息
     * @param noncloudInfo
     * @return
     */
    int insert(NoncloudInfo noncloudInfo);

    /**
     * 根据id查询非云生产资料信息
     * @param id
     * @return
     */
    NoncloudInfo selectNonCloudInfoById(Long id);

    /**
     * 查询全部的非云生产资料信息
     * @return
     */
    List<NoncloudInfo> selectAllNonCloudInfo();

    /**
     * 根据id删除非云生产资料信息
     * @param id
     * @return
     */
    int deleteNonCloudInfoById(Long id);

    /**
     * 更新非云生产资料信息
     * @param noncloudInfo
     * @return
     */
    int updateNonCloudInfoById(NoncloudInfo noncloudInfo);

    /**
     * 根据条件查询是否对应的异常告警的恢复策略
     * @param processName
     * @param hostIP
     * @param hostName
     * @return
     */
    List<NoncloudInfo> selectMatchByCondition(String processName,String hostIP, String hostName);

    List<NoncloudInfo> findPage(String hostIp, String processName);

    /**
     * 根据ids查询非云生产资料信息
     * @return
     */
    List<NoncloudInfo> selectNonCloudInfoByIds(List<Long> exportList);

    /**
     * 定期同步EBOSS上线工具应用部署信息到生产资料表中
     */
    void updateProInfo();

}
