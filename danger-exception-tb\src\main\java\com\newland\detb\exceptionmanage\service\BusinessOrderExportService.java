package com.newland.detb.exceptionmanage.service;

import com.newland.detb.common.Result;
import com.newland.detb.exceptionmanage.entity.BusinessOrder;
import org.apache.ibatis.annotations.Param;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

public interface BusinessOrderExportService {


    /**
     * 查询满足时间范围的异常单信息
     * @param beginDate
     * @param endDate
     * @return
     */
    List<BusinessOrder> queryBusinessOrderList(String beginDate, String endDate);

    /**
     * 导出业务异常单周报数据
     * @param beginDate
     * @param endDate
     */
    void exportBusinessOrder(HttpServletResponse response,String beginDate, String endDate);

    /**
     * 生成业务异常单的汇总信息
     * @param beginDate
     * @param endDate
     * @return
     */
    Result productWeeklyNewsPaperDesc(String beginDate, String endDate);
}
