package com.newland.detb.gostage.mapper;

import com.newland.detb.gostage.entity.BusinessHandle;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 业务异常场景处置方案映射关系
 */
@Mapper
public interface BusinessHandleMapper {

    /**
     * 新增业务场景异常分析及处置方案
     * @param businessHandle
     * @return
     */
    int save(BusinessHandle businessHandle);
    
    /**
     * 更新业务场景异常分析及处置方案
     * @param businessHandle
     * @return
     */
    int update(BusinessHandle businessHandle);
    
    /**
     * 根据id删除业务场景异常分析及处置方案
     * @param id
     * @return
     */
    int delete(@Param("id") Long id);
    
    /**
     * 根据id查询业务场景异常分析及处置方案
     * @param id
     * @return
     */
    BusinessHandle queryById(@Param("id") Long id);
    
    /**
     * 根据父id查询业务场景异常分析及处置方案列表
     * @param pId
     * @return
     */
    List<BusinessHandle> queryListByPId(@Param("pId") Long pId);
    
    /**
     * 查询所有业务场景异常分析及处置方案
     * @return
     */
    List<BusinessHandle> queryList();

    /**
     * 批量新增业务场景异常分析及处置方案
     * @param businessHandleList
     * @return
     */
    int batchSave(@Param("list") List<BusinessHandle> businessHandleList);

    /**
     * 根据PID删除业务场景异常分析及处置方案
     * @param pId
     * @return
     */
    int deleteByPId(Long pId);
}
