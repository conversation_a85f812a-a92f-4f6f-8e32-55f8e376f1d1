package com.newland.detb.exceptionmanage.service.impl;

import com.newland.detb.common.entity.HeExceptionDataBean;
import com.newland.detb.exceptionmanage.entity.ExceptionDataVO;
import com.newland.detb.exceptionmanage.mapper.ExceptionDataMapper;
import com.newland.detb.exceptionmanage.service.ExceptionDataService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-04-17 017 15:10:15
 * @description
 */
@Service
public class ExceptionDataServiceImpl implements ExceptionDataService {

    @Resource
    private ExceptionDataMapper exceptionDataMapper;

    /**
     * 查询全部工单表数据，用于导出数据
     * @return
     */
    @Override
    public List<HeExceptionDataBean> queryExceptionDataBeans() {
        return exceptionDataMapper.queryExceptionDataBeans();
    }

    @Override
    public List<ExceptionDataVO> selectExceptionDataWithProgress(Long dataId, String env,String keyWords, String status, String alarmLevel) {
        return exceptionDataMapper.selectExceptionDataWithProgress(dataId,env,keyWords,status,alarmLevel);
    }

    /**
     * 查询出四个月之前的全部数据，因为数据迁移到历史表中的规则是：3+1（前三个月+当前月的数据需要留在原表中，其他的数据迁移到历史表中）
     *
     * @return
     */
    @Override
    public List<HeExceptionDataBean> selectExceptionDataForMoveToHistory() {
        return exceptionDataMapper.selectExceptionDataForMoveToHistory();
    }
}
