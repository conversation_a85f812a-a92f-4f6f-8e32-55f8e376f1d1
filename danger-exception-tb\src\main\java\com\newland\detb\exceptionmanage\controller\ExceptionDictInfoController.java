package com.newland.detb.exceptionmanage.controller;

import cn.com.nlsoft.cas.client.annotation.CasPermissionRequired;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.newland.detb.common.Result;
import com.newland.detb.exceptionmanage.entity.DictInfo;
import com.newland.detb.exceptionmanage.service.DictInfoService;
import com.newland.detb.log.annotation.SysLog;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.Range;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @date 2023-05-18 018 15:58:02
 * @description 字典表操作
 */
@Slf4j
@RestController
@RequestMapping(value = "/dictInfo")
@Validated
public class ExceptionDictInfoController {

    @Resource
    private DictInfoService dictInfoService;

    @SysLog("保存字典映射信息")
    @PostMapping("save")
    @CasPermissionRequired("aimt.dict.change")
    public Result save(@RequestBody @Validated DictInfo dictInfo) {
        log.info("待保存的字典数据：{}",dictInfo);
        try {
            int res = dictInfoService.insert(dictInfo);
            if (res == 1){
                return Result.success("字典数据保存成功");
            }else {
                return Result.error(570,"字典数据保存失败");
            }
        } catch (Exception e) {
            //SQLIntegrityConstraintViolationException
            e.printStackTrace();
            return Result.error(570,"字典数据保存失败，可能已经存在该值");
        }
    }

    @SysLog("更新字典映射信息")
    @PostMapping("update")
    @CasPermissionRequired("aimt.dict.change")
    public Result update(@RequestBody @Validated DictInfo dictInfo) {
        log.info("待更新的字典数据：{}",dictInfo);
        int res = 0;
        try {
            res = dictInfoService.update(dictInfo);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.error(570,"字典数据更新失败，可能已经存在该属性值");
        }
        if (res == 1){
            return Result.success("字典数据更新成功");
        }else {
            return Result.error(570,"字典数据更新失败");
        }
    }

    @GetMapping("selectByName")
    @CasPermissionRequired("aimt.dict.query")
    public Result selectByName(@RequestParam("name") String name) {
        DictInfo dictInfo = dictInfoService.selectDictInfo(name);
        if (dictInfo!= null){
            return Result.success(dictInfo);
        }else {
            return Result.error(570,"字典数据不存在");
        }
    }

    @GetMapping("getAll")
    @CasPermissionRequired("aimt.dict.query")
    public Result selectAll(@RequestParam(value = "currentPage",defaultValue = "1") int currentPage,
                            @RequestParam(value = "pageSize",defaultValue = "10") int pageSize,
                            @RequestParam(value = "name",required = false,defaultValue = "") @Length(min = 0, max = 10,message = "属性名称的长度为1到10之间") String name,
                            @RequestParam(value = "value",required = false,defaultValue = "") @Length(min = 0, max = 20,message = "属性值的长度为1到20之间") String value) {
        PageHelper.startPage(currentPage,pageSize);
        List<DictInfo> dictInfos = dictInfoService.selectAllDictInfo(name, value);
        PageInfo<DictInfo> dictInfoPageInfo = new PageInfo<>(dictInfos);
        Map<String, Object> map = new ConcurrentHashMap<>();
        map.put("total", dictInfoPageInfo.getTotal());
        map.put("list", dictInfoPageInfo.getList());
        return Result.success(map);
    }

    @SysLog("删除字典映射信息")
    @GetMapping("del/{name}")
    @CasPermissionRequired("aimt.dict.change")
    public Result delDictInfo(@PathVariable("name") @Length(min = 1,max = 10,message = "属性名称长度为1到3位") String name) {
        int res = dictInfoService.deleteDictInfoByName(name);
        if (res == 1) {
            return Result.success("字典数据删除成功");
        }else {
            return Result.error(570,"字典数据删除失败");
        }
    }
}
