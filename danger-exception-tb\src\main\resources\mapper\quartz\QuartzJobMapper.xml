<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.newland.detb.quartz.mapper.QuartzJobMapper">

    <insert id="insertTask">
        insert into he_quartz_task
        (
         job_cron,
         job_name,
         job_group,
         class_name
        )
        values
            (
                #{jobCron,jdbcType=VARCHAR},
                #{jobName,jdbcType=VARCHAR},
                #{jobGroup,jdbcType=VARCHAR},
                #{className,jdbcType=VARCHAR}
            )
    </insert>


    <update id="updateTaskCron">
        update he_quartz_task set job_cron = #{cron} where id = #{id}
    </update>
    <update id="updateJobStatus">
        update he_quartz_task set job_status = #{jobStatus} where id = #{id}
    </update>

    <delete id="deleteTaskById">
        delete from he_quartz_task where id = #{id}
    </delete>

    <select id="selectAllTasks" resultType="com.newland.detb.quartz.entity.QuartzTask">
        select id,job_cron,job_name,job_group,job_status,class_name,create_time from he_quartz_task
        <where>
            <if test="jobName != null and jobName != ''">
                job_name like concat('%',#{jobName},'%')
            </if>
            <if test="jobGroup != null and jobGroup != ''">
                and job_group like concat('%',#{jobGroup},'%')
            </if>
            <if test="jobStatus != null and jobStatus != ''">
                and job_status concat('%',#{jobStatus},'%')
            </if>
        </where>
        order by id desc
    </select>

    <select id="selectById" resultType="com.newland.detb.quartz.entity.QuartzTask">
        select id,job_cron,job_name,job_group,job_status,class_name,create_time from he_quartz_task where id = #{id}
    </select>
</mapper>