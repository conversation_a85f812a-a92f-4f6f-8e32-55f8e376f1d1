package com.newland.detb.exceptionmanage.mapper;

import com.newland.detb.common.entity.HeExceptionDataBean;
import com.newland.detb.exceptionmanage.entity.ExceptionDataVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

//查询全部工单表数据，用于导出数据
@Mapper
public interface ExceptionDataMapper {

    //查询全部工单表数据，用于导出数据
    List<HeExceptionDataBean> queryExceptionDataBeans();

    /**
     * 查询告警工单表和进度表
     * 增加查询条件
     * @param dataId dataId
     * @param env 环境
     * @param homeModule 归属模块
     * @param status 状态
     * @param alarmLevel 告警等级
     * @return
     */
    List<ExceptionDataVO> selectExceptionDataWithProgress(@Param("dataId") Long dataId, @Param("env") String env, @Param("keyWords") String keyWords,@Param("status") String status, @Param("alarmLevel") String alarmLevel);

    /**
     * 查询出四个月之前的全部数据，因为数据迁移到历史表中的规则是：3+1（前三个月+当前月的数据需要留在原表中，其他的数据迁移到历史表中）
     * @return
     */
    List<HeExceptionDataBean> selectExceptionDataForMoveToHistory();

    /**
     * 根据dataId删除告警工单表数据（迁移数据用）
     * @param dataId
     * @return
     */
    int delByDataId(Long dataId);
}
