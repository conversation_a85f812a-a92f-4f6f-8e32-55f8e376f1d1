package com.newland.detb.exceptionmanage.controller;

import com.newland.detb.common.Result;
import com.newland.detb.exceptionmanage.entity.BusinessType;
import com.newland.detb.exceptionmanage.service.BusinessTypeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@Slf4j
@RestController
@RequestMapping("/businessType")
public class BusinessTypeController {

    @Resource
    private BusinessTypeService businessTypeService;

    /**
     * 查询业务异常信息
     * @param parentName
     * @param childName
     * @return
     */
    @GetMapping("query")
    public Result queryBusinessType( @RequestParam("parentName") String parentName, @RequestParam("childName") String childName){
        log.info("查询业务异常信息-parentName:{}, childName:{}",parentName, childName);
        return Result.success(businessTypeService.queryBusinessTypeList(parentName, childName));
    }

    @PostMapping("save")
    public Result saveBusinessType(@RequestBody BusinessType businessType){
        log.info("保存业务异常信息，businessType:{}", businessType);
        return Result.success(businessTypeService.save(businessType));
    }

    @PostMapping("update")
    public Result updateBusinessType(@RequestBody BusinessType businessType){
        log.info("更新业务异常信息，businessType:{}", businessType);
        return Result.success(businessTypeService.update(businessType));
    }

    @GetMapping("delete/{id}")
    public Result deleteBusinessType(@PathVariable("id") Long id){
        log.info("删除业务异常信息，id:{}", id);
        return Result.success(businessTypeService.delete(id));
    }


}
