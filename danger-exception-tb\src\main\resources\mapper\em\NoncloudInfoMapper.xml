<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.newland.detb.exceptionmanage.mapper.NoncloudInfoMapper">

    <resultMap id="BaseResultMap" type="com.newland.detb.exceptionmanage.entity.NoncloudInfo">
            <id property="id" column="ID" jdbcType="BIGINT"/>
            <result property="appName" column="APP_NAME" jdbcType="VARCHAR"/>
            <result property="processName" column="PROCESS_NAME" jdbcType="VARCHAR"/>
            <result property="hostIp" column="HOST_IP" jdbcType="VARCHAR"/>
            <result property="hostName" column="HOST_NAME" jdbcType="VARCHAR"/>
            <result property="processNum" column="PROCESS_NUM" jdbcType="DECIMAL"/>
            <result property="backupIp" column="BACKUP_IP" jdbcType="VARCHAR"/>
            <result property="scriptPath" column="SCRIPT_PATH" jdbcType="VARCHAR"/>
            <result property="deployPath" column="DEPLOY_PATH" jdbcType="VARCHAR"/>
            <result property="typeName" column="TYPE_NAME" jdbcType="VARCHAR"/>
            <result property="deleted" column="DELETED" jdbcType="DECIMAL"/>
            <result property="createTime" column="CREATE_TIME" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID,APP_NAME,PROCESS_NAME,
        HOST_IP,HOST_NAME,PROCESS_NUM,
        BACKUP_IP,SCRIPT_PATH,DEPLOY_PATH,
        TYPE_NAME,DELETED,CREATE_TIME
    </sql>
    <insert id="insert">
        INSERT INTO he_noncloud_info
        (
        app_name,
        process_name,
        host_ip,
        host_name,
        process_num,
        backup_ip,
        script_path,
        deploy_path,
        type_name)
        values
        (
        #{appName,jdbcType=VARCHAR},
        #{processName,jdbcType=VARCHAR},
        #{hostIp,jdbcType=VARCHAR},
        #{hostName,jdbcType=VARCHAR},
        #{processNum,jdbcType=INTEGER},
        #{backupIp,jdbcType=VARCHAR},
        #{scriptPath,jdbcType=VARCHAR},
        #{deployPath,jdbcType=VARCHAR},
        #{typeName,jdbcType=VARCHAR})
    </insert>
    <insert id="insertBatch" parameterType="java.util.List">
        INSERT INTO he_noncloud_info
        (
        app_name,
        process_name,
        host_ip,
        host_name,
        process_num,
        script_path,
        deploy_path,
        type_name
        )
        VALUES
        <foreach collection="noncloudInfoList" item="item" separator=",">
            (
            #{item.appName},
            #{item.processName},
            #{item.hostIp},
            #{item.hostName},
            #{item.processNum},
            #{item.scriptPath},
            #{item.deployPath},
            #{item.typeName}
            )
        </foreach>
    </insert>
    <update id="updateNonCloudInfoById">
        update he_noncloud_info
        <set>
            <if test="appName!=null">app_name = #{appName,jdbcType=VARCHAR},</if>
            <if test="processName!=null">process_name = #{processName,jdbcType=VARCHAR},</if>
            <if test="hostIp!=null">host_ip = #{hostIp,jdbcType=VARCHAR},</if>
            <if test="hostName!=null">host_name = #{hostName,jdbcType=INTEGER},</if>
            <if test="processNum!=null">process_num = #{processNum,jdbcType=INTEGER},</if>
            <if test="backupIp!=null">backup_ip = #{backupIp,jdbcType=VARCHAR},</if>
            <if test="scriptPath!=null">script_path = #{scriptPath,jdbcType=VARCHAR},</if>
            <if test="deployPath!=null">deploy_path = #{deployPath,jdbcType=VARCHAR},</if>
            <if test="typeName!=null">type_name = #{typeName,jdbcType=VARCHAR}</if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <delete id="deleteNonCloudInfoById">
        delete from he_noncloud_info where id = #{id,jdbcType=BIGINT}
    </delete>
    <delete id="clearNonCloudInfo">
        delete from he_noncloud_info
    </delete>
    <select id="selectNonCloudInfoById" resultType="com.newland.detb.exceptionmanage.entity.NoncloudInfo">
        select id,
               app_name,
               process_name,
               host_ip,
               host_name,
               process_num,
               backup_ip,
               script_path,
               deploy_path,
               type_name,
               deleted,
               create_time
        from he_noncloud_info
        where id = #{id, jdbcType = BIGINT}
        order by id desc
    </select>
    <select id="selectAllNonCloudInfo" resultType="com.newland.detb.exceptionmanage.entity.NoncloudInfo">
        select id,
               app_name,
               process_name,
               host_ip,
               host_name,
               process_num,
               backup_ip,
               script_path,
               deploy_path,
               type_name,
               deleted,
               create_time
        from he_noncloud_info
        order by id desc
    </select>
    <select id="selectMatchByCondition" resultType="com.newland.detb.exceptionmanage.entity.NoncloudInfo">
        select id,
               app_name,
               process_name,
               host_ip,
               host_name,
               process_num,
               backup_ip,
               script_path,
               deploy_path,
               type_name,
               deleted,
               create_time
        from he_noncloud_info
        <where>
            <if test="processName != null and processName != ''">
                process_name = #{processName,jdbcType=VARCHAR}
            </if>
            <if test="hostIp != null and hostIp != ''">
                and host_ip = #{hostIp,jdbcType=VARCHAR}
            </if>
            <if test="hostName != null and hostName != ''">
                and host_name = #{hostName,jdbcType=VARCHAR}
            </if>
        </where>
        order by id desc
    </select>
    <select id="findPage" resultType="com.newland.detb.exceptionmanage.entity.NoncloudInfo">
        select
            id,
            app_name,
            process_name,
            host_ip,
            host_name,
            process_num,
            backup_ip,
            script_path,
            deploy_path,
            type_name,
            deleted,
            create_time
        from he_noncloud_info
        <where>
            <if test="processName != null and processName != ''">
                process_name like concat('%',#{processName},'%')
            </if>
            <if test="hostIp != null and hostIp != ''">
                and host_ip like '%${hostIp}%'
            </if>
        </where>
        order by create_time desc
    </select>
    <select id="selectNonCloudInfoByIds" resultType="com.newland.detb.exceptionmanage.entity.NoncloudInfo">
        SELECT
        id,
        app_name,
        process_name,
        host_ip,
        host_name,
        process_num,
        backup_ip,
        script_path,
        deploy_path,
        type_name,
        deleted,
        create_time
        FROM
        he_noncloud_info
        WHERE
        id IN
        <foreach collection="exportList" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </select>
    <select id="qryNoncloudInfo" resultType="com.newland.detb.exceptionmanage.entity.NoncloudInfo">
        select id,
               app_name,
               process_name,
               host_ip,
               host_name,
               process_num,
               backup_ip,
               script_path,
               deploy_path,
               type_name,
               deleted,
               create_time
        from he_noncloud_info a
        where a.process_name=#{processName}
           and a.host_ip=#{hostIp}
           and a.host_name=#{hostName}
           and a.backup_ip=#{backupIp}
    </select>
    <select id="getProInfoFromView" resultType="com.newland.detb.exceptionmanage.entity.NoncloudInfo">
        select a.env_name     appName,
               a.process_name processName,
               a.host_name    hostName,
               a.host_ip      hostIp,
               a.channel_nun  processNum,
               a.script_path  scriptPath,
               a.deploy_path  deployPath,
               a.pro_type typeName
        from rmc.mt_eboss_bushuinfo a
    </select>
    <select id="getTypeNameByProcessName" resultType="java.lang.String">
        select type_name from he_noncloud_info where process_name = #{processName,jdbcType=VARCHAR} and (app_name like concat(#{envSuffix,jdbcType=VARCHAR},'%') or app_name like concat('%',#{envPrefix,jdbcType=VARCHAR}))
    </select>
</mapper>
