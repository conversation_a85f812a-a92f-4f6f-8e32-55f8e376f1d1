package com.newland.detb.exceptionmanage.async;

import com.newland.detb.common.entity.HeExceptionDataBean;
import com.newland.detb.exceptionmanage.entity.ExceptionScenario;
import com.newland.detb.exceptionmanage.entity.MessageConfig;
import com.newland.detb.exceptionmanage.entity.MessageInfo;
import com.newland.detb.exceptionmanage.entity.WorkOrder;
import com.newland.detb.exceptionmanage.service.MessageConfigService;
import com.newland.detb.exceptionmanage.service.MessageInfoService;
import com.newland.detb.util.TimeFormatUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023-05-11 011 15:37:58
 * @description 汇总同步消息
 */
// @Lazy
@Slf4j
@Component
public class AsyncSummaryMessageTask extends BaseAsyncTask{

    @Resource
    private MessageInfoService messageInfoService;

    @Resource
    private MessageConfigService messageConfigService;

    /**
     * 汇总治理流程信息
     * @param heExceptionDataBean 异常数据工单
     * @param exceptionScenario 异常场景库
     * @param workOrder 治理工单
     */
    @Async("taskExecutor")
    @Override
    public void executeAsync(String traceId,HeExceptionDataBean heExceptionDataBean, ExceptionScenario exceptionScenario, WorkOrder workOrder) {
        asyncPrintLogInfo(traceId,"汇总同步消息");
        updateHeExceptionDataWithRemark(heExceptionDataBean,4,"正在汇总同步消息");
        updateProgress(heExceptionDataBean,4,1,"doing","汇总治理流程信息");
        //汇总治理结果
        String message = "【异常治理工具消息同步】" + TimeFormatUtil.getStringTime(new Date()) + (heExceptionDataBean.getHostIp() == null ? "云环境" : (" 主机：" + heExceptionDataBean.getHostIp())) + " 应用：" + heExceptionDataBean.getHomeModule() + " 恢复方式：" + workOrder.getStrategyRemark() +"，异常["+ workOrder.getGovernanceRes() +"]，请知晓！";
        asyncPrintLogInfo(traceId,4,1,"汇总的治理结果信息：" + message);
        //告警级别,严重告警:1  重要告警：2

        //[生成同步短信内容]
        //1、查询需要发送短信的人
        List<MessageConfig> configList = messageConfigService.selectIsSend();
        if (configList != null && configList.size() > 0){
            //生成同步消息
            for (MessageConfig messageConfig : configList){
                MessageInfo messageInfo = new MessageInfo();
                messageInfo.setDataId(heExceptionDataBean.getDataId());
                messageInfo.setName(messageConfig.getName());
                messageInfo.setPhone(messageConfig.getPhone());
                messageInfo.setEmail(messageConfig.getEmail());
                messageInfo.setGovernLevel("1".equals(heExceptionDataBean.getAlarmLevel()) ? "严重告警" : "重要告警");
                messageInfo.setMessage(message);
                messageInfoService.insert(messageInfo);
                log.info("同步信息保存完成：{}",messageInfo);
            }
            asyncPrintLogInfo(traceId,4,1,"所有同步信息保存完成，保存条数：" + configList.size());
            updateHeExceptionDataWithRemark(heExceptionDataBean,4,"汇总同步消息完成");
            updateProgress(heExceptionDataBean,4,1,"success","汇总治理流程信息完成");
            //触发发送操作

            updateHeExceptionDataWithRemark(heExceptionDataBean,4,"同步消息发送成功");
            updateProgress(heExceptionDataBean,4,2,"success","同步消息发送成功");
            asyncPrintLogInfo(traceId,4,2,"同步消息发送成功");
        }else {
            updateProgress(heExceptionDataBean,4,1,"success","汇总治理流程信息完成，不需要发送同步短信信息");
            updateHeExceptionDataWithRemark(heExceptionDataBean,4,"不需要发送同步短信信息");
            updateProgress(heExceptionDataBean,4,2,"success","不需要发送同步短信信息");
            asyncPrintLogInfo(traceId,4,2,"不需要发送同步短信信息");
        }

        updateHeExceptionDataWithRemark(heExceptionDataBean,4,"同步消息处理结束");
        updateProgress(heExceptionDataBean,4,3,"success","同步消息处理结束");
        asyncPrintLogInfo(traceId,4,3,"同步消息处理结束");

        updateHeExceptionDataWithRemark(heExceptionDataBean,5,"异常治理流程结束");
        updateProgress(heExceptionDataBean,5,1,"success","异常治理流程结束");
        asyncPrintLogInfo(traceId,5,1,"异常治理流程结束");
        asyncPrintLogInfo(traceId,"================================[总体异常流程执行完成]================================");
    }

    /**
     * 业务异常单-汇总治理流程信息
     * @param heExceptionDataBean 异常数据工单
     * @param workOrder 治理工单
     * @param msg 恢复的异常单条数
     */
    @Async("taskExecutor")
    public void executeBusinessAsync(String traceId,HeExceptionDataBean heExceptionDataBean,  WorkOrder workOrder,String msg) {
        asyncPrintLogInfo(traceId,"业务异常单-汇总同步消息");
        updateHeExceptionDataWithRemark(heExceptionDataBean,4,"业务异常单-正在汇总同步消息");
        updateProgress(heExceptionDataBean,4,1,"doing","业务异常单-汇总治理流程信息");
        //汇总治理结果
        String message = "【异常治理工具消息同步】" + TimeFormatUtil.getStringTime(new Date()) + (Objects.equals(heExceptionDataBean.getEnvironment(),"0") ? "云环境业务" : "非云环境业务") + " 异常类别：" + heExceptionDataBean.getProcessName() + " 恢复方式：" + workOrder.getStrategyRemark() +"，异常["+ workOrder.getGovernanceRes() + "]，共恢复：" + msg + " 条异常单，请知晓！";
        asyncPrintLogInfo(traceId,4,1,"业务异常单-汇总的治理结果信息：" + message);
        //告警级别,严重告警:1  重要告警：2

        //[生成同步短信内容]
        //1、查询需要发送短信的人
        List<MessageConfig> configList = messageConfigService.selectIsSend();
        if (configList != null && configList.size() > 0){
            //生成同步消息
            for (MessageConfig messageConfig : configList){
                MessageInfo messageInfo = new MessageInfo();
                messageInfo.setDataId(heExceptionDataBean.getDataId());
                messageInfo.setName(messageConfig.getName());
                messageInfo.setPhone(messageConfig.getPhone());
                messageInfo.setEmail(messageConfig.getEmail());
                messageInfo.setGovernLevel("1".equals(heExceptionDataBean.getAlarmLevel()) ? "严重告警" : "重要告警");
                messageInfo.setMessage(message);
                messageInfoService.insert(messageInfo);
                log.info("同步信息保存完成：{}",messageInfo);
            }
            asyncPrintLogInfo(traceId,4,1,"所有同步信息保存完成，保存条数：" + configList.size());
            updateHeExceptionDataWithRemark(heExceptionDataBean,4,"汇总同步消息完成");
            updateProgress(heExceptionDataBean,4,1,"success","汇总治理流程信息完成");
            //触发发送操作

            updateHeExceptionDataWithRemark(heExceptionDataBean,4,"同步消息发送成功");
            updateProgress(heExceptionDataBean,4,2,"success","同步消息发送成功");
            asyncPrintLogInfo(traceId,4,2,"同步消息发送成功");
        }else {
            updateProgress(heExceptionDataBean,4,1,"success","汇总治理流程信息完成，不需要发送同步短信信息");
            updateHeExceptionDataWithRemark(heExceptionDataBean,4,"不需要发送同步短信信息");
            updateProgress(heExceptionDataBean,4,2,"success","不需要发送同步短信信息");
            asyncPrintLogInfo(traceId,4,2,"不需要发送同步短信信息");
        }

        updateHeExceptionDataWithRemark(heExceptionDataBean,4,"同步消息处理结束");
        updateProgress(heExceptionDataBean,4,3,"success","同步消息处理结束");
        asyncPrintLogInfo(traceId,4,3,"同步消息处理结束");

        updateHeExceptionDataWithRemark(heExceptionDataBean,5,"异常治理流程结束");
        updateProgress(heExceptionDataBean,5,1,"success","异常治理流程结束");
        asyncPrintLogInfo(traceId,5,1,"异常治理流程结束");
        asyncPrintLogInfo(traceId,"================================[总体异常流程执行完成]================================");
    }
}
