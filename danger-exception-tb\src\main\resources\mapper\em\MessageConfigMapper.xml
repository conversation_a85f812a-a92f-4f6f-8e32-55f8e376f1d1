<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.newland.detb.exceptionmanage.mapper.MessageConfigMapper">

    <resultMap id="BaseResultMap" type="com.newland.detb.exceptionmanage.entity.MessageConfig">
            <id property="id" column="ID" jdbcType="DECIMAL"/>
            <result property="name" column="NAME" jdbcType="VARCHAR"/>
            <result property="phone" column="PHONE" jdbcType="VARCHAR"/>
            <result property="email" column="EMAIL" jdbcType="VARCHAR"/>
            <result property="position" column="POSITION" jdbcType="VARCHAR"/>
            <result property="isSend" column="IS_SEND" jdbcType="DECIMAL"/>
            <result property="deleted" column="DELETED" jdbcType="DECIMAL"/>
            <result property="createTime" column="CREATE_TIME" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID,NAME,PHONE,
        EMAIL,POSITION,IS_SEND,
        DELETED,CREATE_TIME
    </sql>
    <insert id="insert">
        INSERT INTO he_message_config
        (
        name,
        phone,
        email,
        position,
        is_send)
        values
        (
        #{name,jdbcType=VARCHAR},
        #{phone,jdbcType=VARCHAR},
        #{email,jdbcType=VARCHAR},
        #{position,jdbcType=VARCHAR},
        #{isSend,jdbcType=INTEGER})
    </insert>
    <update id="update">
        update he_message_config
        <set>
            <if test="name!=null">name = #{name,jdbcType=VARCHAR},</if>
            <if test="phone!=null">phone = #{phone,jdbcType=VARCHAR},</if>
            <if test="email!=null">email = #{email,jdbcType=VARCHAR},</if>
            <if test="position!=null">position = #{position,jdbcType=VARCHAR},</if>
            <if test="isSend!=null">is_send = #{isSend,jdbcType=VARCHAR}</if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <delete id="deleteById">
        delete from he_message_config where id = #{id}
    </delete>
    <select id="selectById" resultType="com.newland.detb.exceptionmanage.entity.MessageConfig">
        select
            id,
            name,
            phone,
            email,
            position,
            is_send,
            deleted,
            create_time
        from he_message_config
        where id = #{id}
    </select>
    <select id="selectAll" resultType="com.newland.detb.exceptionmanage.entity.MessageConfig">
        select
            id,
            name,
            phone,
            email,
            position,
            is_send,
            deleted,
            create_time
        from he_message_config
        <where>
            <if test="name != null and name != ''">
                name like concat('%',#{name},'%')
            </if>
            <if test="phone != null and phone != ''">
                and phone like concat('%',#{phone},'%')
            </if>
            <if test="email != null and email != ''">
                and email like concat('%',#{email},'%')
            </if>
            <if test="position != null and position != ''">
                and position like concat('%',#{position},'%')
            </if>
        </where>
        order by id desc
    </select>
    <select id="selectIsSend" resultType="com.newland.detb.exceptionmanage.entity.MessageConfig">
        select
            id,
            name,
            phone,
            email,
            position,
            is_send,
            deleted,
            create_time
        from he_message_config
        where is_send = 0
        order by id desc
    </select>
    <select id="selectInnerList" resultType="com.newland.detb.exceptionmanage.entity.MessageConfig">
        select
            id,
            name,
            phone,
            email,
            position,
            is_send,
            is_inner
        from he_message_config
        where is_send = 0 and is_inner = 1
        order by id desc
    </select>
</mapper>
