package com.newland.detb.exceptionmanage.entity;

import java.io.Serializable;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotNull;

/**
 * 脚本配置信息表
 * @TableName HE_SCRIPT_INFO
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ScriptInfo implements Serializable {
    /**
     * id
     */
    private Long id;

    /**
     * 异常大类
     */
    @NotNull
    @Length(min = 0,max = 10,message = "异常大类属性超长")
    private String parentException;

    /**
     * 异常大类编码
     */
    @NotNull
    @Length(min = 0,max = 10,message = "异常大类编码属性超长")
    private String parentCode;

    /**
     * 异常子类
     */
    @NotNull
    @Length(min = 0,max = 10,message = "异常子类属性超长")
    private String childException;

    /**
     * 异常子类编码
     */
    @NotNull
    @Length(min = 0,max = 100,message = "异常子类编码属性超长")
    private String childCode;

    /**
     * 是否删除，默认0：未删除
     */
    private Integer deleted;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date createTime;

    private static final long serialVersionUID = 1L;
}