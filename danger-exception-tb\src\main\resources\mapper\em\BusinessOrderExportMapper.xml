<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.newland.detb.exceptionmanage.mapper.BusinessOrderExportMapper">

    <!-- <select id="queryBusinessOrderList" resultType="com.newland.detb.exceptionmanage.entity.BusinessOrder"> -->
    <!--     select a.business_code businessCode, -->
    <!--     a.opr_code oprCode, -->
    <!--     a.err_scene errScene, -->
    <!--     a.err_desc errDesc, -->
    <!--     a.is_auto_repaired isAutoRepaired, -->
    <!--     a.is_man_made isManMade, -->
    <!--     a.count sumNvl -->
    <!--     from temp_err_order_repair_item a -->
    <!--     <where> -->
    <!--         <if test="beginDate != null and beginDate != ''"> -->
    <!--             a.create_time &gt;= to_date(#{beginDate,jdbcType=VARCHAR}, 'yyyymmdd') -->
    <!--         </if> -->
    <!--         and a.create_time &lt;= to_date(#{endDate,jdbcType=VARCHAR} || '235959', 'yyyymmddhh24miss') -->
    <!--     </where> -->
    <!-- </select> -->

    <select id="queryBusinessOrderList" resultType="com.newland.detb.exceptionmanage.entity.BusinessOrder">
        select t2.business_code businessCode,
        t2.opr_code oprCode,
        t1.err_scene errScene,
        t2.err_desc errDesc,
        t2.is_auto_repaired isAutoRepaired,
        t2.is_man_made isManMade,
        sum(nvl(t2.cnt, 0)) sumNvl

        from  (select distinct a.err_scene from bossmain.err_order_repair_item a where  a.bip_code = 'BIP4E002' and a.activity_code ='T4011E04' ) t1
        left join
        (select a.business_code,
        decode(a.opr_code,'1', '订购', '2', '退订', '3', '暂停', '4', '恢复', '5','资费变更', '6', '资源变更', '23', '续订', '26', '续订变更', '28', '预注销', '40', '绑券', '99', '跨产品变更', a.opr_code) opr_code,
        a.err_scene,
        (case when a.err_scene  in ('EBOSS业务校验','EBOSS内部归档异常', 'EBOSS流程异常') then a.exec_msg_1
        when a.err_scene in ('BBOSS业务校验', 'BBOSS流程异常') then a.exec_msg_2
        when a.err_scene in ('平台归档流程异常') then a.exec_msg_4
        when a.err_scene in ('平台确认流程异常') then a.exec_msg_3 else a.err_scene end) err_desc,
        (case when a.is_auto_repaired = 1 then '是' else '否' end ) is_auto_repaired,
        (case when a.exec_status = '3' or a.exec_status = '4' then '是' else '否' end ) is_man_made,
        count(1) cnt
        from bossmain.err_order_repair_item  a
        <where>
            a.bip_code = 'BIP4E002' and a.activity_code ='T4011E04'
            <if test="beginDate != null and beginDate != ''">
                and a.create_time &gt;= to_date(#{beginDate,jdbcType=VARCHAR}, 'yyyymmdd')
            </if>
            and a.create_time &lt;= to_date(#{endDate,jdbcType=VARCHAR} || '235959', 'yyyymmddhh24miss')
            and (exists(select 1 from customer.bb_operation t where nvl(t.request_time, t.exec_time) > to_date('20231110', 'yyyymmdd') and a.operation_id = t.operation_id)
            or exists(select * from customer.h_bb_operation t where nvl(t.request_time, t.exec_time) > to_date('20231110', 'yyyymmdd') and a.operation_id = t.operation_id) )
        </where>
        group by a.err_scene, a.business_code, opr_code,
        (case when a.is_auto_repaired = 1 then '是' else '否' end ),
        (case when a.exec_status = '3' or a.exec_status = '4' then '是' else '否' end ),
        (case when a.err_scene  in ('EBOSS业务校验','EBOSS内部归档异常', 'EBOSS流程异常') then a.exec_msg_1
        when a.err_scene in ('BBOSS业务校验', 'BBOSS流程异常') then a.exec_msg_2
        when a.err_scene in ('平台归档流程异常') then a.exec_msg_4
        when a.err_scene in ('平台确认流程异常') then a.exec_msg_3
        else a.err_scene end) ) t2
        on t1.err_scene = t2.err_scene
        group by t2.business_code, t2.opr_code, t1.err_scene, t2.err_desc, t2.is_auto_repaired, t2.is_man_made
        order by t1.err_scene
    </select>
</mapper>