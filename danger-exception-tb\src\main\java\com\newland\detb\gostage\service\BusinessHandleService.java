package com.newland.detb.gostage.service;

import com.newland.detb.gostage.entity.BusinessHandle;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 业务场景异常分析及处置方案服务接口
 */
public interface BusinessHandleService {

    /**
     * 新增业务场景异常分析及处置方案
     * @param businessHandle
     * @return
     */
    int save(BusinessHandle businessHandle);

    /**
     * 更新业务场景异常分析及处置方案
     * @param businessHandle
     * @return
     */
    int update(BusinessHandle businessHandle);

    /**
     * 根据id删除业务场景异常分析及处置方案
     * @param id
     * @return
     */
    int delete(Long id);

    /**
     * 根据PID删除业务场景异常分析及处置方案
     * @param id
     * @return
     */
    int deleteByPId(Long id);

    /**
     * 根据id查询业务场景异常分析及处置方案
     * @param id
     * @return
     */
    BusinessHandle queryById(Long id);

    /**
     * 根据父id查询业务场景异常分析及处置方案列表
     * @param pId
     * @return
     */
    List<BusinessHandle> queryListByPId(Long pId);

    /**
     * 查询所有业务场景异常分析及处置方案
     * @return
     */
    List<BusinessHandle> queryList();

    /**
     * 批量新增业务场景异常分析及处置方案
     * @param businessHandleList
     * @return
     */
    int batchSave(List<BusinessHandle> businessHandleList);
}
