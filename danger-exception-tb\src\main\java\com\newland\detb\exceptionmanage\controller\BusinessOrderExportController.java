package com.newland.detb.exceptionmanage.controller;

import cn.com.nlsoft.cas.client.annotation.CasPermissionRequired;
import com.newland.detb.common.Result;
import com.newland.detb.exceptionmanage.entity.BusinessOrder;
import com.newland.detb.exceptionmanage.entity.ExceptionScenario;
import com.newland.detb.exceptionmanage.service.BusinessOrderExportService;
import com.newland.detb.log.annotation.SysLog;
import com.newland.detb.util.ExcelUtil;
import com.newland.detb.util.TimeFormatUtil;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2024-01-04 004 20:11:47
 * @description
 */
@RestController
@RequestMapping("/businessOrder")
public class BusinessOrderExportController {

    @Resource
    private BusinessOrderExportService businessOrderExportService;

    @SysLog("导出业务异常单周报信息")
    @GetMapping("export")
    @CasPermissionRequired("aimt.business.export")
    public void exportBusinessOrder(HttpServletResponse response, @RequestParam(defaultValue = "") String beginDate, @RequestParam(defaultValue = "") String endDate) {
        businessOrderExportService.exportBusinessOrder(response, beginDate, endDate);
    }

    @GetMapping("query")
    @CasPermissionRequired("aimt.business.query")
    public Result queryBusinessOrder(@RequestParam(defaultValue = "") String beginDate, @RequestParam(defaultValue = "") String endDate) {
        List<BusinessOrder> businessOrderList = businessOrderExportService.queryBusinessOrderList(beginDate, endDate);
        return Result.success(businessOrderList);
    }

    /**
     * 生成业务异常单描述信息
     * @param beginDate
     * @param endDate
     * @return
     */
    @GetMapping("product")
    @CasPermissionRequired("aimt.business.query")
    public Result productBusinessOrderDescription(@RequestParam(defaultValue = "") String beginDate, @RequestParam(defaultValue = "") String endDate) {
        return businessOrderExportService.productWeeklyNewsPaperDesc(beginDate, endDate);
    }
}
