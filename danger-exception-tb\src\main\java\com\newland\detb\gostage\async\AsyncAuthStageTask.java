package com.newland.detb.gostage.async;

import com.newland.detb.common.entity.HeExceptionDataBean;
import com.newland.detb.exceptionmanage.async.AsyncGovernanceTask;
import com.newland.detb.exceptionmanage.async.BaseAsyncTask;
import com.newland.detb.exceptionmanage.entity.BusinessType;
import com.newland.detb.exceptionmanage.entity.ExceptionScenario;
import com.newland.detb.exceptionmanage.entity.WorkOrder;
import com.newland.detb.exceptionmanage.service.WorkOrderService;
import com.newland.detb.gostage.entity.ExceptionScenarioStage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2023-05-10 010 17:16:58
 * @description 确认是否需要执行授权操作
 */
// @Lazy
@Slf4j
@Component
public class AsyncAuthStageTask extends BaseAsyncTask {

    @Resource
    private WorkOrderService workOrderService;

    @Lazy
    @Resource
    private AsyncGovernanceStageTask asyncGovernanceStageTask;


    /**
     * 确认是否需要授权异步任务
     * @param heExceptionDataBean 异常工单表数据
     * @param exceptionScenario 异常场景库（保存着是否需要授权和是否需要触发单项巡检信息）
     * @param workOrder 治理工单表信息
     */
    @Async("taskExecutor")
    @Override
    public void executeAsync(String traceId, HeExceptionDataBean heExceptionDataBean, ExceptionScenarioStage exceptionScenario, WorkOrder workOrder,int randomNumber) {
        asyncPrintLogInfo(traceId,"确认是否授权");
        updateHeExceptionDataWithRemark(heExceptionDataBean,3,"正在确认是否需要授权");
        updateProgress(heExceptionDataBean,3,2,"doing","正在确认是否需要授权");
        try {
            Integer isAuth = exceptionScenario.getIsAuth();
            if (isAuth == 0) {
                asyncPrintLogInfo(traceId,3,2,"需要执行授权操作，等待授权完成");
                updateHeExceptionDataWithRemark(heExceptionDataBean,3,"正在等待授权完成");
                // updateHeExceptionDataWithRemark(heExceptionDataBean,5,"正在等待授权完成");
                updateProgress(heExceptionDataBean,3,2,"doing","正在等待授权完成");
                updateProgress(heExceptionDataBean,5,1,"doing","正在等待授权完成");
                updateWorkOrderWhenFailCase(heExceptionDataBean.getDataId(),"正在等待授权完成");
                workOrderService.updateIsAuth(0, workOrder.getId());
            }else if (isAuth == 1) {
                //当不需要进行授权操作时，直接在工单表的是否授权的字段设置为1，代表已经授权完成，继续执行
                asyncPrintLogInfo(traceId,3,2,"不需要执行授权操作，继续后续治理操作");
                workOrderService.updateIsAuth(1, workOrder.getId());
                //继续执行治理操作
                updateHeExceptionDataWithRemark(heExceptionDataBean,3,"授权处理完成");
                updateProgress(heExceptionDataBean,3,2,"success","授权处理完成");
                asyncGovernanceStageTask.executeAsync(traceId,heExceptionDataBean, exceptionScenario, workOrder,randomNumber);
            }else {
                asyncPrintErrorLogInfo(traceId,3,2,"是否需要执行授权操作配置信息读取失败，无法确认");
                updateHeExceptionDataWithRemark(heExceptionDataBean,3,"授权判断失败");
                updateProgress(heExceptionDataBean,3,2,"fail","授权判断失败");
                updateHeExceptionDataWithRemark(heExceptionDataBean,6,"授权判断失败");
                updateProgress(heExceptionDataBean,5,1,"fail","授权判断失败");
                updateWorkOrderWhenFailCase(heExceptionDataBean.getDataId(),"授权判断失败");
            }
        } catch (Exception e) {
            e.printStackTrace();
            asyncPrintErrorLogInfo(traceId,3,2,"是否需要执行授权操作判断时出现异常：" + e.getMessage());
            updateHeExceptionDataWithRemark(heExceptionDataBean,3,"授权判断失败");
            updateProgress(heExceptionDataBean,3,2,"fail","授权判断失败");
            updateHeExceptionDataWithRemark(heExceptionDataBean,6,"授权判断失败");
            updateProgress(heExceptionDataBean,5,1,"fail","授权判断失败");
            updateWorkOrderWhenFailCase(heExceptionDataBean.getDataId(),"授权判断失败");
        }
    }

}
