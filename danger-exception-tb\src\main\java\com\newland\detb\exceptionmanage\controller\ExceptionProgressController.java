package com.newland.detb.exceptionmanage.controller;

import com.newland.detb.common.Result;
import com.newland.detb.exceptionmanage.entity.ProgressDTO;
import com.newland.detb.exceptionmanage.service.ExceptionProgressService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.CopyOnWriteArrayList;

/**
 * <AUTHOR>
 * @date 2023-04-20 020 14:35:37
 * @description
 */
@Slf4j
@RestController
@RequestMapping("/exceptionProgress")
public class ExceptionProgressController {

    @Resource
    private ExceptionProgressService exceptionProgressService;

    /**
     * 根据工单表的id初始化进度表的6条数据
     * @return
     */
    // @GetMapping("/init")
    public Result initProgress() {
        List<ProgressDTO> list = new CopyOnWriteArrayList<>();
        for (int i = 0; i < 6; i++) {
            ProgressDTO progress = new ProgressDTO();
            progress.setParentProgress(i);
            progress.setChildProgress(0);
            list.add(progress);
        }
        int res = exceptionProgressService.insertInitData(1L);
        log.info("进度表初始化结果，插入数据：{} 条",res);
        return Result.success("数据插入成功");
    }
}
