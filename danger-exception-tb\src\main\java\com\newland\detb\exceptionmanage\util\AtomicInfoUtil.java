package com.newland.detb.exceptionmanage.util;

import cn.hutool.core.io.FileUtil;
import com.newland.detb.exception.AtomicException;
import com.newland.detb.exceptionmanage.entity.AtomicInfo;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.concurrent.CopyOnWriteArrayList;

/**
 * <AUTHOR>
 * @date 2023-08-07 007 17:17:37
 * @description 原子信息获取和展示工具类
 */
@Data
@Slf4j
@Component
@ConfigurationProperties(prefix = "atomic")
public class AtomicInfoUtil {
    /*
    目前有四个目录下的脚本需要展示：（只需要展示）
    1、paasscript
    2、process_script
    3、saturnjob_scrpt
    4、Smsend
     */

    private String path;


    /**
     * 获取全部的脚本基础信息
     * @return
     */
    public List<AtomicInfo> getFileBaseInfo() {
        //先构建第一层目录
        List<AtomicInfo> atomicInfoList = new CopyOnWriteArrayList<>();
        //找到tools下的所有目录
        File basePath = new File(path);
        File[] firstDirectorList = basePath.listFiles();
        if (firstDirectorList != null) {
            //第一个循环进入到四个目录中
            for (File file : firstDirectorList) {
                //循环判断是否都是目录
                if (file.isDirectory()) {
                    AtomicInfo directorAtomic = new AtomicInfo();
                    directorAtomic.setFileName(file.getName());
                    directorAtomic.setFilePath(file.getPath());
                    directorAtomic.setFileMark("父级目录");
                    // directorAtomic.setFileSize(file.length());
                    directorAtomic.setFileSize(FileUtil.size(file));
                    directorAtomic.setLastModified(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date(file.lastModified())));
                    File[] secondDirectorAndFileList = file.listFiles();
                    if (secondDirectorAndFileList != null) {
                        //当前目录下的文件夹以sh结尾的文件信息
                        List<AtomicInfo> fileAtomicList = new CopyOnWriteArrayList<>();
                        //第二层循环获取四个目录中每个目录下的脚本名称
                        for (File directoryAndFile : secondDirectorAndFileList) {
                            //判断是否是sh结尾的文件，其他文件暂不显示
                            if (directoryAndFile.isFile() && directoryAndFile.getName().endsWith(".sh")) {
                                //获取脚本标记内容（在脚本第一行）
                                AtomicInfo atomicInfo = new AtomicInfo();
                                atomicInfo.setFileName(directoryAndFile.getName());
                                atomicInfo.setFileMark(getFileMark(directoryAndFile));
                                atomicInfo.setFileSize(directoryAndFile.length());
                                atomicInfo.setFilePath(directoryAndFile.getPath());
                                atomicInfo.setLastModified(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date(directoryAndFile.lastModified())));
                                fileAtomicList.add(atomicInfo);
                            }
                        }
                        //给目录信息添加children
                        directorAtomic.setChildren(fileAtomicList);
                        atomicInfoList.add(directorAtomic);
                    }
                }
            }
        }
        return atomicInfoList;
    }

    /**
     * 获取文件mark标记
     * @param file
     * @return
     */
    private String getFileMark(File file) {
        String line = null;
        StringBuilder sb = new StringBuilder();
        try {
            FileInputStream inputStream = new FileInputStream(file);
            BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(inputStream));
            //只读取第一行内容
            line = bufferedReader.readLine();
            if (line != null) {
                if (!line.contains("#mark")) {
                    sb.append("暂未标记");
                }else {
                    //按照英文冒号分割，#mark:xxxxxx
                    String[] split = line.split(":");
                    sb.append(split[1]);
                }
            }
            return sb.toString();
        } catch (IOException e) {
            e.printStackTrace();
            throw new AtomicException(519,"原子信息标记读取失败");
        }
    }

    /**
     * 根据文件路径获取文件内容
     * @param filePath
     * @return
     */
    public String getFileContent(String filePath) {
        String line = null;
        StringBuffer sb = new StringBuffer();
        try {
            File file = new File(filePath);
            BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(new FileInputStream(file), StandardCharsets.UTF_8));
            while ((line = bufferedReader.readLine()) != null) {
                if (!line.contains("#mark")) {
                    sb.append(line).append("\r\n");
                }
            }
            return sb.toString();
        } catch (IOException e) {
            e.printStackTrace();
            throw new AtomicException(519,"原子信息内容获取失败");
        }
    }
}
