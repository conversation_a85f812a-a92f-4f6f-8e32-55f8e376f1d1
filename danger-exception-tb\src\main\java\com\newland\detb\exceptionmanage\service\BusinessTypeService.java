package com.newland.detb.exceptionmanage.service;

import com.newland.detb.exceptionmanage.entity.BusinessType;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【HE_BUSINESS_TYPE(业务异常场景表)】的数据库操作Service
* @createDate 2023-11-01 18:04:35
*/
public interface BusinessTypeService {

    /**
     * 根据业务场景名称查询是否存在
     * @param type
     * @return
     */
    BusinessType queryBusinessType(String type,String parentName,String childName);

    /**
     * 查询全部业务异常集合
     * @param parentName
     * @param childName
     * @return
     */
    List<BusinessType> queryBusinessTypeList( String parentName, String childName);

    /**
     * 新增业务异常场景
     * @param businessType
     * @return
     */
    int save(BusinessType businessType);

    /**
     *
     * @param businessType
     * @return
     */
    int update(BusinessType businessType);

    /**
     * 根据id删除业务异常场景
     * @param id
     * @return
     */
    int delete(@Param("id") Long id);
}
