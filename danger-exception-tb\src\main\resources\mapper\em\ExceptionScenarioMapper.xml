<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.newland.detb.exceptionmanage.mapper.ExceptionScenarioMapper">

    <resultMap id="BaseResultMap" type="com.newland.detb.exceptionmanage.entity.ExceptionScenario">
            <id property="id" column="ID" jdbcType="DECIMAL"/>
            <result property="module" column="MODULE" jdbcType="VARCHAR"/>
            <result property="attribution" column="ATTRIBUTION" jdbcType="VARCHAR"/>
            <result property="type" column="TYPE" jdbcType="VARCHAR"/>
            <result property="env" column="ENV" jdbcType="DECIMAL"/>
            <result property="parentClass" column="PARENT_CLASS" jdbcType="VARCHAR"/>
            <result property="subClass" column="SUB_CLASS" jdbcType="VARCHAR"/>
            <result property="source" column="SOURCE" jdbcType="VARCHAR"/>
            <result property="isOverride" column="IS_OVERRIDE" jdbcType="DECIMAL"/>
            <result property="exceptionAttribution" column="EXCEPTION_ATTRIBUTION" jdbcType="DECIMAL"/>
            <result property="sceneCode" column="SCENE_CODE" jdbcType="DECIMAL"/>
            <result property="deleted" column="DELETED" jdbcType="DECIMAL"/>
            <result property="createTime" column="CREATE_TIME" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID,MODULE,ATTRIBUTION,
        TYPE,ENV,PARENT_CLASS,
        SUB_CLASS,SOURCE,IS_OVERRIDE,
        EXCEPTION_ATTRIBUTION,SCENE_CODE,DELETED,
        CREATE_TIME,EXCEPTION_DESC,RECOVERY_STEP
    </sql>

    <insert id="insert">
        insert into he_exception_scenario
        (
         module,
         attribution,
         type,
         env,
         parent_class,
         sub_class,
         exception_desc,
         source,
         is_override,
         recovery_step,
         exception_attribution,
         scene_code,
         is_auth,
         is_inspection)
        values
            (
             #{module,jdbcType=VARCHAR},
             #{attribution,jdbcType=VARCHAR},
             #{type,jdbcType=VARCHAR},
             #{env,jdbcType=INTEGER},
             #{parentClass,jdbcType=VARCHAR},
             #{subClass,jdbcType=VARCHAR},
             #{exceptionDesc,jdbcType=VARCHAR},
             #{source,jdbcType=VARCHAR},
             #{isOverride,jdbcType=INTEGER},
             #{recoveryStep,jdbcType=VARCHAR},
             #{exceptionAttribution,jdbcType=INTEGER},
             #{sceneCode,jdbcType=INTEGER},
             #{isAuth,jdbcType=INTEGER},
             #{isInspection,jdbcType=INTEGER})
    </insert>

    <update id="update">
        update he_exception_scenario
        <set>
            <if test="module != null and module != ''">module = #{module,jdbcType=VARCHAR},</if>
            <if test="attribution != null and attribution != ''">attribution = #{attribution,jdbcType=VARCHAR},</if>
            <if test="type != null and type != ''">type = #{type,jdbcType=VARCHAR},</if>
            <if test="env != null">env = #{env,jdbcType=INTEGER},</if>
            <if test="parentClass !=null and parentClass != ''">parent_class = #{parentClass,jdbcType=VARCHAR},</if>
            <if test="subClass != null and subClass != ''">sub_class = #{subClass,jdbcType=VARCHAR},</if>
            <if test="exceptionDesc != null and exceptionDesc != ''">exception_desc = #{exceptionDesc,jdbcType=VARCHAR},</if>
            <if test="source != null and source != ''">source = #{source,jdbcType=VARCHAR},</if>
            <if test="isOverride!=null">is_override = #{isOverride,jdbcType=INTEGER},</if>
            <if test="recoveryStep != null and recoveryStep != ''">recovery_step = #{recoveryStep,jdbcType=CLOB},</if>
            <if test="exceptionAttribution!=null">exception_attribution = #{exceptionAttribution,jdbcType=INTEGER},</if>
            <if test="isAuth!=null">is_auth = #{isAuth,jdbcType=INTEGER},</if>
            <if test="isInspection!=null">is_inspection = #{isInspection,jdbcType=INTEGER}</if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateIsAuthById">
        update he_exception_scenario
        <set>
            <if test="isAuth!=null">is_auth = #{isAuth,jdbcType=INTEGER}</if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateIsInspectionById">
        update he_exception_scenario
        <set>
            <if test="isInspection!=null">is_inspection = #{isInspection,jdbcType=INTEGER}</if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateIsOverrideById">
        update he_exception_scenario
        <set>
            <if test="isOverride!=null">is_override = #{isOverride,jdbcType=INTEGER}</if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <delete id="delete">
        delete from he_exception_scenario where id = #{id,jdbcType=INTEGER}
    </delete>

    <select id="findById" resultType="com.newland.detb.exceptionmanage.entity.ExceptionScenario">
        select id,
            module,
            attribution,
            type,
            env,
            parent_class,
            sub_class,
            exception_desc,
            source,
            is_override,
            recovery_step,
            exception_attribution,
            scene_code,
            is_auth,
            is_inspection,
            deleted,
            create_time
        from he_exception_scenario
        where id = #{id, jdbcType = BIGINT}

    </select>
    <select id="findAll" resultType="com.newland.detb.exceptionmanage.entity.ExceptionScenario">
        SELECT
            a.id,
            a.module,
            a.attribution,
            a.type,
            a.env,
            a.parent_class,
            a.sub_class,
            a.exception_desc,
            a.source,
            a.is_override,
            a.recovery_step,
            a.exception_attribution,
            a.scene_code,
            a.is_auth,
            a.is_inspection,
            a.deleted,
            a.create_time,
            b.remark AS strategy
        FROM
            he_exception_scenario a
                LEFT JOIN
            he_governance_strategy b
            ON
                CAST(a.scene_code AS CHAR) = b.scene_code
        ORDER BY
            a.id DESC
    </select>
    <select id="findByCondition" resultType="com.newland.detb.exceptionmanage.entity.ExceptionScenario">
        SELECT
        a.id,
        a.module,
        a.attribution,
        a.type,
        a.env,
        a.parent_class,
        a.sub_class,
        a.exception_desc,
        a.source,
        a.is_override,
        a.recovery_step,
        a.exception_attribution,
        a.scene_code,
        a.is_auth,
        a.is_inspection,
        a.deleted,
        a.create_time,
        b.remark AS strategy
        FROM
        he_exception_scenario a
        LEFT JOIN
        he_governance_strategy b
        ON
        CAST(a.scene_code AS CHAR) = b.scene_code
        <where>
            <if test="type != null and type != ''">
                a.type LIKE CONCAT('%', #{type}, '%')
            </if>
            <if test="env != null">
                AND a.env = #{env}
            </if>
            <if test="subClass != null and subClass != ''">
                AND a.sub_class LIKE CONCAT('%', #{subClass}, '%')
            </if>
            <if test="isAuth != null">
                AND a.is_auth = #{isAuth}
            </if>
            <if test="isInspection != null">
                AND a.is_inspection = #{isInspection}
            </if>
        </where>
        ORDER BY
        id DESC
    </select>
    <select id="findByThreeCondition" resultType="com.newland.detb.exceptionmanage.entity.ExceptionScenario">
        select id,
        module,
        attribution,
        type,
        env,
        parent_class,
        sub_class,
        exception_desc,
        source,
        is_override,
        recovery_step,
        exception_attribution,
        scene_code,
        is_auth,
        is_inspection,
        deleted,
        create_time
        from he_exception_scenario
        <where>
            <if test="env != null">env = #{env}</if>
            <if test="parentNode != null and parentNode != ''">and parent_class = #{parentNode,jdbcType=VARCHAR}</if>
            <if test="childNode != null and childNode != ''">and sub_class = #{childNode,jdbcType=VARCHAR}</if>
            and is_override = 0
        </where>
    </select>
    <select id="selectBySceneCode" resultType="com.newland.detb.exceptionmanage.entity.ExceptionScenario">
        select
            id,
            module,
            attribution,
            type,
            env,
            parent_class,
            sub_class,
            exception_desc,
            source,
            is_override,
            recovery_step,
            exception_attribution,
            scene_code,
            is_auth,
            is_inspection,
            deleted,
            create_time
        from he_exception_scenario
        where scene_code = #{sceneCode}
    </select>
    <select id="findByIds" resultType="com.newland.detb.exceptionmanage.entity.ExceptionScenario">
        SELECT
        a.id,
        a.module,
        a.attribution,
        a.type,
        a.env,
        a.parent_class,
        a.sub_class,
        a.exception_desc,
        a.source,
        a.is_override,
        a.recovery_step,
        a.exception_attribution,
        a.scene_code,
        a.is_auth,
        a.is_inspection,
        a.deleted,
        a.create_time,
        b.remark AS strategy
        FROM
        he_exception_scenario a
        LEFT JOIN
        he_governance_strategy b
        ON
        CAST(a.scene_code AS CHAR) = b.scene_code
        WHERE
        a.id IN
        <foreach collection="exportList" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </select>
    <select id="getMaxSceneCode" resultType="java.lang.Integer">
        SELECT
            IFNULL(MAX(a.scene_code), 0) + 1 AS maxSceneCode
        FROM
            he_exception_scenario a
    </select>
    <select id="getScenarioByType" resultType="com.newland.detb.exceptionmanage.entity.ExceptionScenario">
        select id,
        module,
        attribution,
        type,
        env,
        parent_class,
        sub_class,
        exception_desc,
        source,
        is_override,
        recovery_step,
        exception_attribution,
        scene_code,
        is_auth,
        is_inspection,
        deleted,
        create_time
        from he_exception_scenario
        <where>
            <if test="type != null">type = #{type}</if>
            and is_override = 0
        </where>
    </select>
</mapper>
