package com.newland.detb.gostage.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 业务场景异常分析及处置方案类
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class BusinessHandle {

    /**
     * id
     */
    private Long id;

    /**
     * 处置场景的父id
     */
    private Long pId;

    /**
     * 异常分析和处置方案的索引
     */
    private int index;

    /**
     * 异常分析
     */
    private String analysis;

    /**
     * 处置方案
     */
    private String handle;
}
