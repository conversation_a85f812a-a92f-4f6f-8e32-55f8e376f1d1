package com.newland.detb.gostage.async;

import com.newland.detb.common.entity.HeExceptionDataBean;
import com.newland.detb.exceptionmanage.async.AsyncQueryGovernanceStrategyTask;
import com.newland.detb.exceptionmanage.async.BaseAsyncTask;
import com.newland.detb.exceptionmanage.entity.DictInfo;
import com.newland.detb.exceptionmanage.entity.ExceptionScenario;
import com.newland.detb.exceptionmanage.entity.ScriptInfo;
import com.newland.detb.exceptionmanage.mapper.BusinessExceptionOrderMapper;
import com.newland.detb.exceptionmanage.service.DictInfoService;
import com.newland.detb.exceptionmanage.service.MessageConfigService;
import com.newland.detb.exceptionmanage.service.MessageInfoService;
import com.newland.detb.exceptionmanage.service.ScriptInfoService;
import com.newland.detb.gostage.entity.ExceptionScenarioStage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Random;

/**
 *  异常场景二次确认
 */
@Slf4j
@Component
public class AsyncSecondaryConfirmStageTask extends BaseAsyncTask {

    @Resource
    private ScriptInfoService scriptInfoService;

    @Lazy
    @Resource
    private AsyncQueryGovernanceStrategyStageTask asyncQueryGovernanceStrategyStageTask;

    @Resource
    private DictInfoService dictInfoService;

    @Resource
    private BusinessExceptionOrderMapper businessExceptionOrderMapper;

    @Resource
    private MessageConfigService messageConfigService;

    @Resource
    private MessageInfoService messageInfoService;

    // 创建Random对象用于生成随机数
    private final Random random = new Random();

    /**
     * 生成0到7之间的随机整数（包含0和7）
     * @return 返回0-7之间的随机数
     */
    public int generateRandomNumber() {
        // nextInt(8)生成0-7之间的随机数
        return random.nextInt(8);
    }

    /**
     * 执行异常二次确认操作
     * @param heExceptionDataBean 工单表数据
     * @param exceptionScenarioStage 异常场景库数据
     */
    @Async("taskExecutor")
    public void executeAsync(String traceId, HeExceptionDataBean heExceptionDataBean, ExceptionScenarioStage exceptionScenarioStage) {
        //生成从0-7的随机数
        int randomNumber = generateRandomNumber();
        asyncPrintLogInfo(traceId,"异常二次确认操作");
        updateProgress(heExceptionDataBean,1,3,"doing","正在执行异常二次确认操作");
        //异常二次确认结果为真，继续进行后续治理流程
        updateHeExceptionDataWithRemark(heExceptionDataBean,1,"异常二次确认成功，" + exceptionScenarioStage.getBusinessHandles().get(randomNumber).getAnalysis());
        updateProgress(heExceptionDataBean,1,3,"success","异常二次确认成功，结果:继续治理");

        //开始查询异常治理策略
        asyncPrintLogInfo(traceId,1,3,"异常二次确认结束，等待查询异常治理策略");

        updateHeExceptionDataWithRemark(heExceptionDataBean,1,"异常场景确认成功");
        updateProgress(heExceptionDataBean,1,4,"success","异常场景确认成功");
        //查询治理策略异步任务
        asyncQueryGovernanceStrategyStageTask.executeAsync(traceId,heExceptionDataBean,exceptionScenarioStage,randomNumber);
    }
}
