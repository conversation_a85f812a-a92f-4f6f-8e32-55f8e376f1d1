package com.newland.detb.exceptionmanage.mapper;

import com.newland.detb.exceptionmanage.entity.SceneInspection;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
* <AUTHOR>
* @description 针对表【HE_SCENE_INSPECTION(单项巡检和异常场景关联配置表)】的数据库操作Mapper
* @createDate 2023-06-13 14:21:37
* @Entity com.newland.detb.exceptionmanage.entity.SceneInspection
*/
@Mapper
public interface SceneInspectionMapper {

    /**
     * 保存关联数据(巡检指标名称手动进行配置)
     * @param sceneInspection
     * @return
     */
    int insert(SceneInspection sceneInspection);

    /**
     * 根据sceneCode删除
     * @param sceneCode
     * @return
     */
    int deleteBySceneCode(@Param("sceneCode") int sceneCode);

    /**
     * 根据场景编码获取关系信息
     * @param sceneCode
     * @return
     */
    SceneInspection getInspectionNameBySceneCode(@Param("sceneCode") int sceneCode);
}




