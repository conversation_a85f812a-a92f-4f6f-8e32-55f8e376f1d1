package com.newland.detb.util;

import com.jcraft.jsch.*;
import com.newland.detb.common.ResEnum;
import com.newland.detb.exception.JschException;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.Optional;
import java.util.Properties;

/**
 * <AUTHOR>
 * @date 2023-04-03 003 10:27:33
 * @description 远程连接工具类
 */
@Data
@Slf4j
@Component
@ConfigurationProperties(prefix = "flagbit")
public class JschUtil {

    private String host;

    private String username;

    private String password;

    private int port;

    private String filepath;

    private String filename;

    /**
     * 需要执行异常治理标志位判断的命令
     */
    private String command;

    /**
     * 创建jsch的session
     * @return
     */
    public Session createSession() {
        JSch jSch = new JSch();
        Session session = null;
        log.info("开始请求连接远程主机：{}",host);
        try {
            session = jSch.getSession(username,host,port);
            //设置主机密码
            session.setPassword(password);
            Properties config = new Properties();
            //去掉首次连接确认
            config.put("StrictHostKeyChecking","no");
            session.setConfig(config);
            //设置超时时间：3秒
            session.setTimeout(3000);
            //连接
            session.connect();
            //获取连接结果
            boolean result = session.isConnected();
            if (result) {
                log.info("与远程主机连接成功：{}",host);
                return session;
            }
        } catch (JSchException e) {
            e.printStackTrace();
            log.error("远程连接：{} ,时发生异常：{}",host,e.getMessage());
            // throw new JschException(ResEnum.jsch_error_code,"连接远程主机失败");
        }
        return null;
    }

    /**
     * 判断标志位文件内容是否为空，进而判断是否需要执行异常治理流程
     * 文件内容为空：正常情况，不上线的时候
     * 文件有内容：开启屏蔽异常告警，正在上线
     * @return 返回文件内容是否为空，返回true时，正常执行异常治理；返回false，异常治理流程结束
     */
    public Boolean getFile() {
        ChannelSftp channel = null;
        //获取session对象
        Session session = createSession();
        if (!Optional.ofNullable(session).isPresent()) {
            log.error("异常治理标志位判断------>jsch的session对象获取失败");
            throw new JschException(ResEnum.jsch_error_code,"连接远程主机失败");
        }
        //创建sftp通道
        try {
            channel = (ChannelSftp) session.openChannel("sftp");
            channel.connect();
            channel.setFilenameEncoding("UTF-8");
            String flagFilePath = filepath.endsWith("/") ? (filepath + filename) : (filepath + "/" + filename);
            log.info("异常治理标志位判断------>channel通道创建成功，开始获取标志位文件内容：{}",flagFilePath);
            InputStream inputStream = channel.get(flagFilePath);
            BufferedReader br = new BufferedReader(new InputStreamReader(inputStream));
            String line = br.readLine();
            if (line == null || line.length() == 0) {
                log.info("异常治理标志位判断------>标志位文件为空，正常执行异常治理流程");
                return true;
            }
            log.info("异常治理标志位判断------>治理标志位文件内容：{}",line);
            log.info("异常治理标志位判断------>标志位文件不为空，终止执行异常治理流程");
            return false;
        } catch (JSchException | SftpException | IOException e) {
            e.printStackTrace();
            log.error("异常治理标志位判断------>获取远程文件内容失败：{}",e.getMessage());
            throw new JschException(ResEnum.jsch_error_code,"获取远程文件内容失败");
        } finally {
            if (channel != null && channel.isConnected()) {
                channel.disconnect();
                log.info("异常治理标志位判断------>获取标志位状态的channel通道关闭完成");
            }
            if (session.isConnected()) {
                session.disconnect();
            }
            log.info("异常治理标志位判断------>获取标志位状态的jsch的session关闭完成");
        }
    }

    /**
     * 判断标志位文件内容是否为空，进而判断是否需要执行异常治理流程
     * 当文件数为0时，不治理，返回false
     * 当文件数大于0时，治理，返回true
     * @return
     */
    public Boolean getFileStatus() {
        ChannelExec channel = null;
        //获取session对象
        Session session = createSession();
        if (!Optional.ofNullable(session).isPresent()) {
            log.error("【异常治理标志位判断】------>jsch的session对象获取失败");
            throw new JschException(ResEnum.jsch_error_code,"连接远程主机失败");
        }
        //创建sftp通道
        try {
            channel = (ChannelExec) session.openChannel("exec");
            //cd /home/<USER>/liuning/ebosshome/eboss/app_nas/mt_tools/eboss_monitor/smsend && cat sendflag.txt | wc -l
            String resultCommand = "cd " + filepath + " && " + command;
            log.info("【异常治理标志执行的命令】：{}",resultCommand);
            channel.setCommand(resultCommand);
            channel.connect();
            //错误输出
            channel.setErrStream(System.err);
            //获取命令执行结果
            InputStream inputStream = channel.getInputStream();
            BufferedReader br = new BufferedReader(new InputStreamReader(inputStream));
            String line = br.readLine();
            int flag = Integer.parseInt(line);
            if (flag == 0) {
                log.info("【异常治理标志位判断】------>标志位文件数为0，结束异常治理流程(文件数：{})",flag);
                return false;
            }else {
                log.info("【异常治理标志位判断】------>标志位文件数大于0，开始执行异常治理流程(文件数：{})",flag);
                return true;
            }
        } catch (JSchException | IOException e) {
            e.printStackTrace();
            log.error("【异常治理标志位判断】------>获取远程文件内容失败：{}",e.getMessage());
            throw new JschException(ResEnum.jsch_error_code,"获取远程文件内容失败");
        } finally {
            if (channel != null && channel.isConnected()) {
                channel.disconnect();
                log.info("【异常治理标志位判断】------>获取标志位状态的channel通道关闭完成");
            }
            if (session.isConnected()) {
                session.disconnect();
            }
            log.info("【异常治理标志位判断】------>获取标志位状态的jsch的session关闭完成");
        }
    }

    /**
     * 测试异常治理操作
     * @return
     */
    public Boolean governance() {
        // ChannelSftp channel = null;
        // //获取session对象
        // Session session = createSession();
        // if (!Optional.ofNullable(session).isPresent()) {
        //     log.error("异常治理标志位判断------>jsch的session对象获取失败");
        //     throw new JschException(ResEnum.jsch_error_code,"连接远程主机失败");
        // }
        // //创建sftp通道
        // try {
        //     channel = (ChannelSftp) session.openChannel("sftp");
        //     channel.connect();
        //     channel.setFilenameEncoding("UTF-8");
        //     String flagFilePath = filepath.endsWith("/") ? (filepath + filename) : (filepath + "/" + filename);
        //     log.info("异常治理标志位判断------>channel通道创建成功，开始获取标志位文件内容：{}",flagFilePath);
        //     InputStream inputStream = channel.get(flagFilePath);
        //     BufferedReader br = new BufferedReader(new InputStreamReader(inputStream));
        //     String line = br.readLine();
        //     if (line == null || line.length() == 0) {
        //         log.info("异常治理标志位判断------>标志位文件为空，正常执行异常治理流程");
        //         return true;
        //     }
        //     log.info("异常治理标志位判断------>治理标志位文件内容：{}",line);
        //     log.info("异常治理标志位判断------>标志位文件不为空，终止执行异常治理流程");
        //     return false;
        // } catch (JSchException | SftpException | IOException e) {
        //     e.printStackTrace();
        //     log.error("异常治理标志位判断------>获取远程文件内容失败：{}",e.getMessage());
        //     throw new JschException(ResEnum.jsch_error_code,"获取远程文件内容失败");
        // } finally {
        //     if (channel != null && channel.isConnected()) {
        //         channel.disconnect();
        //         log.info("异常治理标志位判断------>获取标志位状态的channel通道关闭完成");
        //     }
        //     if (session.isConnected()) {
        //         session.disconnect();
        //     }
        //     log.info("异常治理标志位判断------>获取标志位状态的jsch的session关闭完成");
        // }
        return true;
    }
}
