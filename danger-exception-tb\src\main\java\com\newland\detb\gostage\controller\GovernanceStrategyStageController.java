package com.newland.detb.gostage.controller;

import cn.com.nlsoft.cas.client.annotation.CasPermissionRequired;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.newland.detb.common.Result;
import com.newland.detb.exceptionmanage.entity.GovernanceStrategy;
import com.newland.detb.exceptionmanage.service.GovernanceStrategyService;
import com.newland.detb.gostage.entity.GovernanceStrategyStage;
import com.newland.detb.gostage.service.GovernanceStrategyStageService;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.validator.constraints.Length;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.constraints.Max;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @date 2023-08-17 017 11:31:32
 * @description 治理策略信息
 */
@Slf4j
@RestController
@RequestMapping("/stage/governanceStrategy")
@Validated
public class GovernanceStrategyStageController {

    @Resource
    private GovernanceStrategyStageService governanceStrategyStageService;

    /**
     * 获取全部的
     * @param currentPage
     * @param pageSize
     * @return
     */
    @GetMapping("getAll")
    @CasPermissionRequired("aimt.strategy.query")
    public Result getAll(@RequestParam(value = "currentPage",defaultValue = "1") int currentPage,
                         @RequestParam(value = "pageSize",defaultValue = "10") int pageSize,
                         @RequestParam(value = "remark",defaultValue = "") @Length(min = 0,max = 50,message = "治理策略长度最多50位") String remark) {
        PageHelper.startPage(currentPage, pageSize);
        List<GovernanceStrategyStage> governanceStrategyList = governanceStrategyStageService.getAllGovernanceStrategies(remark);
        PageInfo<GovernanceStrategyStage> governanceStrategyPageInfo = new PageInfo<>(governanceStrategyList);
        Map<String ,Object> map = new ConcurrentHashMap<>();
        map.put("list",governanceStrategyPageInfo.getList());
        map.put("total",governanceStrategyPageInfo.getTotal());
        return Result.success(map);
    }

    /**
     * 更新治理策略信息(只更新remark字段)
     * @param remark
     * @return
     */
    @GetMapping("update")
    @CasPermissionRequired("aimt.strategy.update")
    public Result updateGovernanceStrategy(@RequestParam("id") @Max(value = 9223372036854774807L,message = "治理策略ID超过最大值") Long id, @RequestParam("remark") @Length(min = 1,max = 50,message = "治理策略长度为1到50位") String remark) {
        int res = governanceStrategyStageService.updateGovernanceStrategy(id, remark);
        if (res < 1) {
            return Result.error(500,"治理策略信息更新失败！");
        }
        return Result.success();
    }
}
