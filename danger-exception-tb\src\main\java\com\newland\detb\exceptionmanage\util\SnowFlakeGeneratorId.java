package com.newland.detb.exceptionmanage.util;

import cn.hutool.core.lang.Snowflake;
import cn.hutool.core.util.IdUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2023-07-26 026 14:44:13
 * @description 使用hutool的雪花算法生成唯一ID
 */
@Slf4j
@Component
public class SnowFlakeGeneratorId {

    /**
     * 工作机器ID，此值的区间值是0～31
     */
    private long workerId = 1;
    /**
     * 数据中心ID，此值的区间值是0～31
     */
    private long datacenterId = 1;

    /**
     * 通过糊涂工具包的IdUtill创建雪花对象
     */
    private Snowflake snowflake = IdUtil.getSnowflake(workerId,datacenterId);

    /**
     * 使用默认参数获得全局ID
     */
    public synchronized long snowflakeId() {
        return snowflake.nextId();
    }

    /**
     * 使用自定义参数获得全局ID
     */
    public synchronized long snowflakeId(long workerId, long datacenterId){
        Snowflake snowflake = IdUtil.getSnowflake(workerId, datacenterId);
        return snowflake.nextId();
    }
}
