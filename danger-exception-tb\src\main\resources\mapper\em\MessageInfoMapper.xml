<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.newland.detb.exceptionmanage.mapper.MessageInfoMapper">

    <resultMap id="BaseResultMap" type="com.newland.detb.exceptionmanage.entity.MessageInfo">
            <id property="id" column="ID" jdbcType="DECIMAL"/>
            <result property="dataId" column="DATA_ID" jdbcType="DECIMAL"/>
            <result property="name" column="NAME" jdbcType="VARCHAR"/>
            <result property="phone" column="PHONE" jdbcType="VARCHAR"/>
            <result property="email" column="EMAIL" jdbcType="VARCHAR"/>
            <result property="governLevel" column="GOVERN_LEVEL" jdbcType="VARCHAR"/>
            <result property="isComplete" column="IS_COMPLETE" jdbcType="DECIMAL"/>
            <result property="deleted" column="DELETED" jdbcType="DECIMAL"/>
            <result property="createTime" column="CREATE_TIME" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID,DATA_ID,NAME,
        PHONE,EMAIL,GOVERN_LEVEL,
        IS_COMPLETE,DELETED,CREATE_TIME,
        MESSAGE
    </sql>

    <insert id="insert">
        INSERT INTO he_message_info
        (
        data_id,
        name,
        phone,
        email,
        message,
        govern_level)
        values
        (
        #{dataId,jdbcType=BIGINT},
        #{name,jdbcType=VARCHAR},
        #{phone,jdbcType=VARCHAR},
        #{email,jdbcType=VARCHAR},
        #{message,jdbcType=VARCHAR},
        #{governLevel,jdbcType=VARCHAR})
    </insert>
    <insert id="insertInner">
        INSERT INTO he_message_info
        (
        data_id,
        name,
        phone,
        email,
        message,
        govern_level)
        values
        (
        #{dataId,jdbcType=BIGINT},
        #{name,jdbcType=VARCHAR},
        #{phone,jdbcType=VARCHAR},
        #{email,jdbcType=VARCHAR},
        #{message,jdbcType=VARCHAR},
        #{governLevel,jdbcType=VARCHAR})
    </insert>
    <update id="update">
        update he_message_info
        <set>
            <if test="dataId!=null">data_id = #{dataId,jdbcType=VARCHAR},</if>
            <if test="name!=null">name = #{name,jdbcType=VARCHAR},</if>
            <if test="phone!=null">phone = #{phone,jdbcType=VARCHAR},</if>
            <if test="email!=null">email = #{email,jdbcType=VARCHAR},</if>
            <if test="message!=null">message = #{message,jdbcType=VARCHAR},</if>
            <if test="governLevel!=null">govern_level = #{governLevel,jdbcType=VARCHAR},</if>
            <if test="isComplete!=null">is_complete = #{isComplete,jdbcType=INTEGER}</if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <delete id="deleteById">
        delete from he_message_info where id = #{id}
    </delete>
    <select id="selectById" resultType="com.newland.detb.exceptionmanage.entity.MessageInfo">
        select
            id,
            data_id,
            name,
            phone,
            email,
            message,
            govern_level,
            is_complete,
            deleted,
            create_time
        from he_message_info
        where id = #{id}
    </select>
    <select id="selectAll" resultType="com.newland.detb.exceptionmanage.entity.MessageInfo">
        select
            id,
            data_id,
            name,
            phone,
            email,
            message,
            govern_level,
            is_complete,
            deleted,
            create_time
        from he_message_info
        <where>
            <if test="dataId != null">
                data_id = #{dataId}
            </if>
            <if test="name != null">
                and name like concat('%',#{name},'%')
            </if>
            <if test="phone != null and phone != ''">
                and phone like concat('%',#{phone},'%')
            </if>
            <if test="email != null and email != ''">
                and email like concat('%',#{email},'%')
            </if>
            <if test="message != null and message != ''">
                and message like concat('%',#{message},'%')
            </if>
            <if test="governLevel != null and governLevel != ''">
                and govern_level = #{governLevel}
            </if>
            <if test="isComplete != null">
                and is_complete = #{isComplete}
            </if>
        </where>
        order by id desc
    </select>
</mapper>
