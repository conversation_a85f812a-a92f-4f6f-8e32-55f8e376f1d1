package com.newland.detb.exceptionmanage.entity;

import java.io.Serializable;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.newland.detb.common.annotation.Phone;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.Email;
import javax.validation.constraints.NotNull;

/**
 * 短信配置表
 * @TableName HE_MESSAGE_CONFIG
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class MessageConfig implements Serializable {
    /**
     * id
     */
    private Long id;

    /**
     * 姓名
     */
    @NotNull
    @Length(min = 0,max = 6,message = "姓名超长")
    private String name;

    /**
     * 手机号
     */
    @NotNull
    @Phone
    private String phone;

    /**
     * 邮箱
     */
    @NotNull
    @Email
    @Length(min = 0,max = 35,message = "邮箱地址过长")
    private String email;

    /**
     * 职位等级
     */
    @NotNull
    @Length(min = 0,max = 10,message = "职位名称过长")
    private String position;

    /**
     * 是否需要发送，0：需要，1：不需要
     */
    private Integer isSend;

    /**
     * 是否删除，默认0：未删除
     */
    private Integer deleted;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date createTime;

    /**
     * 是否内部发送，默认：0代表全部发送，1：代表只发送内部负责人
     */
    private Integer isInner;

    private static final long serialVersionUID = 1L;
}