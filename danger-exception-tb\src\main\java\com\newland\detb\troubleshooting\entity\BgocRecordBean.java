package com.newland.detb.troubleshooting.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * @Author: xumengfan
 * @CreateTime: 2023-04-18  10:00
 * @Description: TODO
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class BgocRecordBean {
    /**
     *
     */
    private String resourceid;
    /**
     *
     */
    private String subtaskname;
    /**
     *
     */
    private String resourcename;
    /**
     *
     */
    private String dn;
    /**
     *
     */
    private String type;
    /**
     *
     */
    private String subtaskid;
    /**
     *
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;


    private List<BgocPerfDetailBean> perf;
}
