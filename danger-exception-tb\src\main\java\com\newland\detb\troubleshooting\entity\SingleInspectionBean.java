package com.newland.detb.troubleshooting.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import oracle.sql.DATE;

/**
 * @Author: xumengfan
 * @CreateTime: 2023-04-27  18:39
 * @Description: TODO
 */
@Data
public class SingleInspectionBean {
    /**
     * 资源编码
     **/
    private String resourceid;
    /**
     * 主机IP
     **/
    private String ipAddress;
    /**
     *单项值
     **/
    private String templateName;
    /**
     * 单项ID
     **/
    private String kpiTemplateId;
    /**
     * 对应值
     **/
    private String kpiValue;
    /**
     * 明细项
     **/
    private String kpiName;

    /**
     * 明细项编码
     **/
    private String kpiId;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private DATE createTime;

}
