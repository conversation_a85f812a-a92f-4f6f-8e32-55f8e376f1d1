<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.newland.detb.exceptionmanage.mapper.WorkOrderHistoryMapper">

    <resultMap id="BaseResultMap" type="com.newland.detb.exceptionmanage.entity.WorkOrderHistory">
            <id property="id" column="ID" jdbcType="DECIMAL"/>
            <result property="dataId" column="DATA_ID" jdbcType="DECIMAL"/>
            <result property="sceneCode" column="SCENE_CODE" jdbcType="VARCHAR"/>
            <result property="strategyRemark" column="STRATEGY_REMARK" jdbcType="VARCHAR"/>
            <result property="env" column="ENV" jdbcType="VARCHAR"/>
            <result property="hostName" column="HOST_NAME" jdbcType="VARCHAR"/>
            <result property="appName" column="APP_NAME" jdbcType="VARCHAR"/>
            <result property="isAuth" column="IS_AUTH" jdbcType="DECIMAL"/>
            <result property="isInspection" column="IS_INSPECTION" jdbcType="DECIMAL"/>
            <result property="deleted" column="DELETED" jdbcType="DECIMAL"/>
            <result property="createTime" column="CREATE_TIME" jdbcType="TIMESTAMP"/>
            <result property="governanceRes" column="GOVERNANCE_RES" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID,DATA_ID,SCENE_CODE,
        STRATEGY_REMARK,ENV,HOST_NAME,
        APP_NAME,IS_AUTH,IS_INSPECTION,
        DELETED,CREATE_TIME,GOVERNANCE_RES,
        RECOVERY_COMMAND
    </sql>
    <insert id="insert">
        insert into he_work_order_history(id,data_id,scene_code,strategy_remark,env,host_name,app_name,is_auth,is_inspection,governance_res,recovery_command,create_time)
        values
               (#{id,jdbcType=BIGINT},
                #{dataId,jdbcType=BIGINT},
                #{sceneCode,jdbcType=VARCHAR},
                #{strategyRemark,jdbcType=VARCHAR},
                #{env,jdbcType=VARCHAR},
                #{hostName,jdbcType=VARCHAR},
                #{appName,jdbcType=VARCHAR},
                #{isAuth,jdbcType=INTEGER},
                #{isInspection,jdbcType=INTEGER},
                #{governanceRes,jdbcType=VARCHAR},
                #{recoveryCommand,jdbcType=CLOB},
                #{createTime,jdbcType=TIMESTAMP})
    </insert>
</mapper>
