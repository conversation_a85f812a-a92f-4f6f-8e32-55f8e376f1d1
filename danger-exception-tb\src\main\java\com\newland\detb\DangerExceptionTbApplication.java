package com.newland.detb;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.web.servlet.ServletComponentScan;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

@EnableScheduling
@ServletComponentScan(basePackages={"cn.com.nlsoft.cas.client"})
@SpringBootApplication(scanBasePackages = {"com.newland.detb", "cn.com.nlsoft.cas.client"})
@Configuration
//开启异步
@EnableAsync
@EnableAspectJAutoProxy(proxyTargetClass = true)
public class DangerExceptionTbApplication {

    public static void main(String[] args) {
        SpringApplication.run(DangerExceptionTbApplication.class, args);
    }

}
