package com.newland.detb.exceptionmanage.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.metadata.Sheet;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.fill.FillConfig;
import com.newland.detb.common.CustomCellHandler;
import com.newland.detb.common.CustomCellWriteHandler;
import com.newland.detb.common.Result;
import com.newland.detb.exceptionmanage.entity.BusinessOrder;
import com.newland.detb.exceptionmanage.entity.ExceptionScenario;
import com.newland.detb.exceptionmanage.mapper.BusinessOrderExportMapper;
import com.newland.detb.exceptionmanage.service.BusinessOrderExportService;
import com.newland.detb.util.ExcelUtil;
import com.newland.detb.util.TimeFormatUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2024-01-04 004 16:58:51
 * @description
 */
@Slf4j
@Service
public class BusinessOrderExportServiceImpl implements BusinessOrderExportService {

    private static final String inspection_order_total = "巡检总异常订单数";
    private static final String EBOSS_business_check = "EBOSS业务校验";
    private static final String platform_file_process_exception = "平台归档流程异常";
    private static final String platform_confirm_process_exception = "平台确认流程异常";
    private static final String BBOSS_process_exception = "BBOSS流程异常";
    private static final String BBOSS_business_check = "BBOSS业务校验";

    @Resource
    private BusinessOrderExportMapper businessOrderExportMapper;


    /**
     * 查询满足时间范围的异常单信息
     *
     * @param beginDate
     * @param endDate
     * @return
     */
    @Override
    public List<BusinessOrder> queryBusinessOrderList(String beginDate, String endDate) {
        return businessOrderExportMapper.queryBusinessOrderList(beginDate,endDate);
    }

    /**
     * 导出业务异常单周报数据
     *
     * @param beginDate
     * @param endDate
     */
    @Override
    public void exportBusinessOrder(HttpServletResponse response,String beginDate, String endDate) {
        try {
            //查询数据
            List<BusinessOrder> businessOrderList = businessOrderExportMapper.queryBusinessOrderList(beginDate, endDate);
            Optional<List<BusinessOrder>> optional = Optional.ofNullable(businessOrderList);
            if (optional.isPresent()) {
                // 设置响应头
                response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
                //设置字符编码
                response.setCharacterEncoding("utf-8");
                String fileName = URLEncoder.encode(beginDate + "-" + endDate + "业务异常单统计", "UTF-8").replaceAll("\\+", "%20");
                response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
                // response.setHeader("Content-disposition", "attachment;filename=" + beginDate + "-" + endDate + "业务异常单统计.xlsx");

                OutputStream outputStream = response.getOutputStream();
                // ExcelWriter writer = EasyExcel.write(outputStream,BusinessOrder.class).excelType(ExcelTypeEnum.XLSX).registerWriteHandler(new CustomCellWriteHandler()).build();
                log.info("开始导出业务异常单周报内容，共计条数：{}",optional.get().size());
                EasyExcel.write(outputStream, BusinessOrder.class)
                        .registerWriteHandler(new CustomCellHandler())
                        .registerWriteHandler(new CustomCellWriteHandler())
                        .sheet("业务异常单统计")
                        .doWrite(optional.get());
            }else {
                log.info("业务异常单信息为空无需导出");
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    /**
     * 生成业务异常单的汇总信息
     *
     * @param beginDate
     * @param endDate
     * @return
     */
    @Override
    public Result productWeeklyNewsPaperDesc(String beginDate, String endDate) {
        List<BusinessOrder> businessOrderList = businessOrderExportMapper.queryBusinessOrderList(beginDate, endDate);
        //初始化Map
        Map<String, Integer> map = initializeMap();
        for (BusinessOrder businessOrder : businessOrderList) {
            getBusinessOrderCount(businessOrder,map);
        }
        String productDescription = productDescription(map, beginDate, endDate);
        return Result.success(productDescription);
    }

    /**
     * 对业务异常单数据进行统计
     * @param businessOrder
     * @param map
     */
    private void getBusinessOrderCount(BusinessOrder businessOrder,Map<String, Integer> map) {
        //对每一行数据的数量都要进行计算求和
        Integer total = map.get("inspection_order_total");
        total = total + (int)businessOrder.getSumNvl().longValue();
        map.put("inspection_order_total", total);
        //获取map中对应key的值
        String mapKey = getMapKey(businessOrder.getErrScene());
        if (!"".equals(mapKey)) {
            Integer count = map.get(mapKey);
            //进行计算
            count = count + (int)businessOrder.getSumNvl().longValue();
            //重新放入map中
            map.put(mapKey, count);
        }
    }

    /**
     * 获取Map中的key
     * @param errScene
     * @return
     */
    private String getMapKey(String errScene) {
        String key = "";
        if ("EBOSS业务校验".equals(errScene)) {
            key = "EBOSS_business_check";
        }else if ("平台归档流程异常".equals(errScene)) {
            key = "platform_file_process_exception";
        }else if ("平台确认流程异常".equals(errScene)) {
            key = "platform_confirm_process_exception";
        }else if ("BBOSS流程异常".equals(errScene)) {
            key = "BBOSS_process_exception";
        }else if ("BBOSS业务校验".equals(errScene)) {
            key = "BBOSS_business_check";
        }else {
            key = "";
        }
        return key;
    }

    /**
     * 生成对应的描述内容
     * 2024年1月1号0时-1月7号23时异常治理工具业务异常单治理恢复情况如下：
     * 2024年1月1号0时-1月7号23时，巡检总异常订单数206笔。其中EBOSS业务校验192笔；
     * 平台归档流程异常1笔；平台确认流程异常2笔，并进行了自动处理；0笔BBOSS流程异常；BBOSS业务校验7笔。详情业务异常单恢复情况请查阅附附件！
     * @param map
     * @return
     */
    private String productDescription(Map<String, Integer> map,String beginDate, String endDate) {
        try {
            StringBuilder builder = new StringBuilder();
            int inspectionTotal = 0;
            Integer EBOSSCheck = map.get("EBOSS_business_check");
            Integer platformFile = map.get("platform_file_process_exception");
            Integer platformConfirm = map.get("platform_confirm_process_exception");
            Integer BBOSSProcess = map.get("BBOSS_process_exception");
            Integer BBOSSBusiness = map.get("BBOSS_business_check");
            inspectionTotal = map.get("inspection_order_total");
            //时间格式化
            SimpleDateFormat dateFormatIn = new SimpleDateFormat("yyyyMMdd");
            SimpleDateFormat dateFormatOut = new SimpleDateFormat("yyyy年MM月dd日");
            Date begin = dateFormatIn.parse(beginDate);
            Date end = dateFormatIn.parse(endDate);
            beginDate = dateFormatOut.format(begin);
            endDate = dateFormatOut.format(end);
            builder.append(beginDate).append("0时-").append(endDate).append("23时异常治理工具业务异常单治理恢复情况如下：").append("\r\n")
                    .append(beginDate).append("0时-").append(endDate).append("23时，").append(inspection_order_total).append(inspectionTotal).append("笔。其中")
                    .append(EBOSS_business_check).append(EBOSSCheck).append("笔;")
                    .append(platform_file_process_exception).append(platformFile).append("笔；")
                    .append(platform_confirm_process_exception).append(platformConfirm).append("笔，并进行了自动处理；")
                    .append(BBOSSProcess).append("笔").append(BBOSS_process_exception)
                    .append("；").append(BBOSS_business_check).append(BBOSSBusiness).append("笔。详情业务异常单恢复情况请查阅附附件！");
            log.info("业务异常单周报描述信息生成结果：{}",builder.toString());
            return builder.toString();
        } catch (ParseException e) {
            e.printStackTrace();
            log.error("业务异常单周报描述信息生成异常：{}",e.getMessage());
            return "";
        }
    }

    /**
     * 进行Map初始化
     * @return
     */
    private Map<String, Integer> initializeMap() {
        Map<String, Integer> map = new HashMap<>();
        map.put("inspection_order_total",0);
        map.put("EBOSS_business_check",0);
        map.put("platform_file_process_exception",0);
        map.put("platform_confirm_process_exception",0);
        map.put("BBOSS_process_exception",0);
        map.put("BBOSS_business_check",0);
        return map;
    }
}
