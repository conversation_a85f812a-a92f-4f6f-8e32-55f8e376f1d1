package com.newland.detb.config;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.common.serialization.StringDeserializer;
import org.apache.kafka.common.serialization.StringSerializer;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.config.ConcurrentKafkaListenerContainerFactory;
import org.springframework.kafka.config.KafkaListenerContainerFactory;
import org.springframework.kafka.core.*;
import org.springframework.kafka.listener.ConcurrentMessageListenerContainer;
import org.springframework.kafka.listener.DeadLetterPublishingRecoverer;
import org.springframework.kafka.listener.ErrorHandler;
import org.springframework.kafka.listener.SeekToCurrentErrorHandler;
import org.springframework.kafka.support.converter.RecordMessageConverter;
import org.springframework.kafka.support.converter.StringJsonMessageConverter;
import org.springframework.util.backoff.FixedBackOff;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023-03-17 017 14:00:09
 * @description 配置第二个kafka
 */
@Data
@Slf4j
@Configuration
@ConfigurationProperties(prefix = "spring.kafka.two")
public class KafkaTwoConfig {

    private String bootstrapServers;

    @Bean
    public KafkaTemplate<String,String> kafkaTwoTemplate() {
        return new KafkaTemplate<>(producerFactory());
    }

    @Bean
    public KafkaListenerContainerFactory<ConcurrentMessageListenerContainer<Integer,String>> kafkaTwoListenerContainerFactory() {
        ConcurrentKafkaListenerContainerFactory<Integer, String> factory = new ConcurrentKafkaListenerContainerFactory<>();
        factory.setConsumerFactory(consumerFactory());
        //最好在消费者监听注解中使用concurrency
        // factory.setConcurrency(3);
        factory.getContainerProperties().setPollTimeout(3000);
        //设置异常重试
        factory.setErrorHandler(kafkaErrorHandlerTwo(kafkaTwoTemplate()));
        return factory;
    }

    private ProducerFactory<String,String> producerFactory() {
        return new DefaultKafkaProducerFactory<>(producerConfigs());
    }

    public ConsumerFactory<Integer,String> consumerFactory() {
        return new DefaultKafkaConsumerFactory<>(consumerConfigs());
    }

    private Map<String,Object> producerConfigs() {
        Map<String, Object> props = new HashMap<>();
        props.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG,bootstrapServers);
        props.put(ProducerConfig.RETRIES_CONFIG,0);
        //注意，不要写成：1
        props.put(ProducerConfig.ACKS_CONFIG,"1");
        props.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, StringSerializer.class);
        props.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG,StringSerializer.class);
        return props;
    }

    private Map<String, Object> consumerConfigs() {
        Map<String, Object> props = new HashMap<>();
        props.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG,bootstrapServers);
        //设置offset的消费方式，从启动后消费最新的数据
        props.put(ConsumerConfig.AUTO_OFFSET_RESET_CONFIG,"latest");
        // props.put(ConsumerConfig.GROUP_ID_CONFIG,"");
        // props.put(ConsumerConfig.ENABLE_AUTO_COMMIT_CONFIG,"");
        props.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
        props.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG,StringDeserializer.class);
        return props;
    }

    //seek操作实现消费者操作偏移offset复位，使丢弃记录在下一次轮询再取出
    public ErrorHandler kafkaErrorHandlerTwo(KafkaTemplate<?,?> template) {
        log.error("kafka-2消费异常，开始进行重试-----------------------------------------");
        //设置死信队列
//        DeadLetterPublishingRecoverer recoverer = new DeadLetterPublishingRecoverer(template);
        //创建FixedBackOff对象(设置重试间隔为：10秒，重试次数为：3次)
        FixedBackOff backOff = new FixedBackOff(10 * 1000L, 3L);
        //创建SeekToCurrentErrorHandler对象
//        return new SeekToCurrentErrorHandler(recoverer,backOff);
//        去除死信队列配置
        return new SeekToCurrentErrorHandler(backOff);
    }

    //反序列化异常
    @Bean
    public RecordMessageConverter converterTwo() {
        return new StringJsonMessageConverter();
    }
}