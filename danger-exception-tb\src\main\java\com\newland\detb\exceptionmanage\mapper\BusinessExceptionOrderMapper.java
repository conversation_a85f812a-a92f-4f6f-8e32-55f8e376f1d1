package com.newland.detb.exceptionmanage.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Map;

/**
 * 调用存储过程（巡检、分析、恢复）进行业务异常单进行治理
 */
@Mapper
@DS("slave")
public interface BusinessExceptionOrderMapper {

    /**
     * 调用巡检存储过程
     * @param paramsMap
     * @return
     */
    void invokeInspection(Map<String, Object> paramsMap);

    /**
     * 调用分析存储过程
     * @param paramsMap
     * @return
     */
    void invokeAnalyze(Map<String, Object> paramsMap);

    /**
     * 调用恢复存储过程
     * @param paramsMap
     * @return
     */
    void invokeRecovery(Map<String, Object> paramsMap);

    /**
     * 订单-正向工单异常
     */
    void retryOperation();

    /**
     * 修复业务异常单
     */
    void repairBusinessExceptionOrder(@Param("name") String name);

}
