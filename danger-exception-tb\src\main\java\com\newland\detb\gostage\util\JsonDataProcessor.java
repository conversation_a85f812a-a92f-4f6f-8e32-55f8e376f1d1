package com.newland.detb.gostage.util;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.newland.detb.gostage.entity.BusinessHandle;
import com.newland.detb.gostage.entity.GovernanceStrategyStage;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

/*
解析json数据
 */
@Slf4j
public class JsonDataProcessor {

    private static final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 判断字符串是否为合法的JSON格式
     * @param jsonStr 待检查的JSON字符串
     * @return 是否为合法JSON
     */
    public static boolean isValidJson(String jsonStr) {
        if (!StringUtils.hasText(jsonStr)) {
            return false;
        }

        try {
            objectMapper.readTree(jsonStr);
            return true;
        } catch (JsonProcessingException e) {
            log.error("JSON解析失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 从JSON数据中提取BusinessHandle列表和GovernanceStrategyStage
     * @param jsonStr JSON字符串
     * @param pId 父ID
     * @param sceneCode 场景编码
     * @return 包含BusinessHandle列表和GovernanceStrategyStage的结果
     */
    public static JsonProcessResult processJsonData(String jsonStr, Long pId, String sceneCode) {
        if (!isValidJson(jsonStr)) {
            return null;
        }

        try {
            JsonNode rootNode = objectMapper.readTree(jsonStr);
            List<BusinessHandle> businessHandles = new ArrayList<>();
            StringBuilder strategyRemark = new StringBuilder();

            // 遍历JSON节点
            Iterator<Map.Entry<String, JsonNode>> fields = rootNode.fields();
            while (fields.hasNext()) {
                Map.Entry<String, JsonNode> entry = fields.next();
                String key = entry.getKey();
                String value = entry.getValue().asText();

                // 检查值是否包含":"
                if (value.contains(":")) {
                    String[] parts = value.split(":", 2);
                    String analysis = parts[0].trim();
                    String handle = parts[1].trim();

                    // 创建BusinessHandle对象
                    BusinessHandle businessHandle = new BusinessHandle();
                    businessHandle.setPId(pId);
                    businessHandle.setIndex(Integer.parseInt(key));
                    businessHandle.setAnalysis(analysis);
                    businessHandle.setHandle(handle);
                    businessHandles.add(businessHandle);

                    // 添加到策略备注
                    strategyRemark.append(handle).append("\n");
                } else {
                    // 如果没有":"，则整个值作为处理方案
                    BusinessHandle businessHandle = new BusinessHandle();
                    businessHandle.setPId(pId);
                    businessHandle.setIndex(Integer.parseInt(key));
                    businessHandle.setAnalysis(value);
                    businessHandle.setHandle("");
                    businessHandles.add(businessHandle);

                    // 添加到策略备注
                    strategyRemark.append(value).append("\n");
                }
            }

            // 创建GovernanceStrategyStage对象
            GovernanceStrategyStage strategyStage = new GovernanceStrategyStage();
            strategyStage.setSceneCode(sceneCode);
            strategyStage.setStrategyCode(sceneCode);
            strategyStage.setRemark(strategyRemark.toString().trim());

            return new JsonProcessResult(businessHandles, strategyStage);
        } catch (Exception e) {
            log.error("处理JSON数据失败: {}", e.getMessage());
            return null;
        }
    }

    /**
     * JSON处理结果类
     */
    @Getter
    public static class JsonProcessResult {
        private List<BusinessHandle> businessHandles;
        private GovernanceStrategyStage governanceStrategyStage;

        public JsonProcessResult(List<BusinessHandle> businessHandles, GovernanceStrategyStage governanceStrategyStage) {
            this.businessHandles = businessHandles;
            this.governanceStrategyStage = governanceStrategyStage;
        }

    }
}
