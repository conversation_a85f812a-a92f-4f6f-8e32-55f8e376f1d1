package com.newland.detb.exceptionmanage.entity;

import java.io.Serializable;
import java.util.Date;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * 非云生产资料信息表-计费和账务
 * 云环境相关的也在这个类中
 * @TableName HE_NONCLOUD_INFO
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class NoncloudInfo implements Serializable {
    /**
     * id
     */
    @NotNull(message = "ID属性不能为空",groups = {Update.class})
    @Min(value = 0,message = "ID最小为0",groups = {Update.class})
    @Max(value = Long.MAX_VALUE,message = "ID超过了最大值",groups = {Update.class})
    @ExcelIgnore
    private Long id;

    /**
     * 程序名称
     */
    @NotNull(message = "程序名称不能为空",groups = {Save.class,Update.class})
    @Length(min = 0, max = 50,message = "进程名称的最大长度为50位",groups = {Save.class,Update.class})
    @ExcelProperty("程序名称")
    private String appName;

    /**
     * 进程名
     */
    @NotNull(message = "进程名不能为空",groups = {Save.class,Update.class})
    @Length(min = 0, max = 50,message = "进程名的最大长度为50位",groups = {Save.class,Update.class})
    @ExcelProperty("进程名")
    private String processName;

    /**
     * 部署主机IP
     */
    // @NotNull(message = "部署主机IP不能为空",groups = {Save.class,Update.class})
    @Length(min = 0, max = 50,message = "部署主机IP的最大长度为50位",groups = {Save.class,Update.class})
    @ExcelProperty("部署主机IP")
    private String hostIp;

    /**
     * 主机名
     */
    // @NotNull(message = "主机名不能为空",groups = {Save.class,Update.class})
    @Length(min = 0, max = 100,message = "主机名的最大长度为100位",groups = {Save.class,Update.class})
    @ExcelProperty("主机名")
    private String hostName;

    /**
     * 进程数
     */
    @NotNull(message = "进程数不能为空",groups = {Save.class,Update.class})
    @Min(value = 0,message = "进程数最小为0",groups = {Save.class,Update.class})
    @Max(value = Integer.MAX_VALUE,message = "超过进程数最大数",groups = {Save.class,Update.class})
    @ExcelProperty("进程数")
    private String processNum;

    /**
     * 主备机情况说明
     */
    // @NotNull(message = "主备机不能为空",groups = {Save.class,Update.class})
    @Length(min = 0, max = 50,message = "主备机的最大长度为50位",groups = {Save.class,Update.class})
    @ExcelProperty("主备机")
    private String backupIp;

    /**
     * 部署路径
     * 改为脚本路径
     */
    @NotNull(message = "部署路径不能为空",groups = {Save.class,Update.class})
    @Length(min = 0, max = 500,message = "脚本路径的最大长度为500位",groups = {Save.class,Update.class})
    @ExcelProperty("脚本路径")
    private String scriptPath;

    /**
     * 应用启动脚本
     * 改为部署路径
     */
    @NotNull(message = "应用启动脚本不能为空",groups = {Save.class,Update.class})
    @Length(min = 0, max = 500,message = "部署路径的最大长度为500位",groups = {Save.class,Update.class})
    @ExcelProperty("部署路径")
    private String deployPath;

    /**
     * 关键字类型
     */
    @NotNull(message = "关键字类型不能为空",groups = {Save.class,Update.class})
    @Length(min = 0, max = 50,message = "关键字类型最大长度为50位",groups = {Save.class,Update.class})
    @ExcelProperty("关键字类型")
    private String typeName;

    /**
     * 应用停止脚本
     * 删除
     */
    // @NotNull(message = "应用停止脚本不能为空",groups = {Save.class,Update.class})
    // @Length(min = 0, max = 50,message = "应用停止脚本的最大长度为50位",groups = {Save.class,Update.class})
    // @ExcelProperty("应用停止脚本")
    // private String stopScript;

    /**
     * 是否删除，默认0，代表未删除
     */
    @ExcelIgnore
    private Integer deleted;

    /**
     * 创建时间
     */
    @ExcelIgnore
    @ExcelProperty("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date createTime;

    private static final long serialVersionUID = 1L;

    /**
     * 保存时校验的分组
     */
    public interface Save {

    }

    /**
     * 更新时校验的分组
     */
    public interface Update {

    }
}