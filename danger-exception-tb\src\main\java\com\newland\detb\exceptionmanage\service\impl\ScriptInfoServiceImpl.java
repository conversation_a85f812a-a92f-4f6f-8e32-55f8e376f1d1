package com.newland.detb.exceptionmanage.service.impl;

import com.newland.detb.exceptionmanage.entity.ScriptInfo;
import com.newland.detb.exceptionmanage.service.ScriptInfoService;
import com.newland.detb.exceptionmanage.mapper.ScriptInfoMapper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【HE_SCRIPT_INFO(脚本配置信息表)】的数据库操作Service实现
* @createDate 2023-05-17 15:42:47
*/
@Service
public class ScriptInfoServiceImpl implements ScriptInfoService{

    @Resource
    private ScriptInfoMapper scriptInfoMapper;

    /**
     * 保存scriptInfo
     *
     * @param scriptInfo
     * @return
     */
    @Override
    public int insert(ScriptInfo scriptInfo) {
        return scriptInfoMapper.insert(scriptInfo);
    }

    /**
     * 根据id删除
     *
     * @param id
     * @return
     */
    @Override
    public int deleteById(Long id) {
        return scriptInfoMapper.deleteById(id);
    }

    /**
     * 更新scriptInfo
     *
     * @param scriptInfo
     * @return
     */
    @Override
    public int update(ScriptInfo scriptInfo) {
        return scriptInfoMapper.update(scriptInfo);
    }

    /**
     * 根据id查询scriptInfo
     *
     * @param id
     * @return
     */
    @Override
    public ScriptInfo selectById(Long id) {
        return scriptInfoMapper.selectById(id);
    }

    /**
     * 查询全部scriptInfo
     *
     * @return
     */
    @Override
    public List<ScriptInfo> selectAll(String parentException,String childException) {
        return scriptInfoMapper.selectAll(parentException, childException);
    }

    /**
     * 根据child_exception查询脚本对应关系
     *
     * @param childException
     * @return
     */
    @Override
    public ScriptInfo selectByChildException(String childException) {
        return scriptInfoMapper.selectByChildException(childException);
    }
}




