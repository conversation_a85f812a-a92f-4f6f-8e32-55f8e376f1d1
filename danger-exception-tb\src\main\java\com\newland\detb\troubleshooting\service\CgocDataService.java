package com.newland.detb.troubleshooting.service;

import com.newland.detb.troubleshooting.entity.CgocDataDetailBean;

public interface CgocDataService {


    int insertHeCgocDataDetailMapper(CgocDataDetailBean cgocDataDetailBean);

    /**
     * 通过resourceId和当前日期判断数据是否存在
     * @param cgocDataDetailBean
     * @param time
     * @return
     */
    int ifExistsCgocData(CgocDataDetailBean cgocDataDetailBean,String time);

    /**
     * 根据resourceId更新C-GOC数据
     * @param cgocDataDetailBean
     * @return
     */
    int updateCgocDataDetailMapper(CgocDataDetailBean cgocDataDetailBean,String time);
}
