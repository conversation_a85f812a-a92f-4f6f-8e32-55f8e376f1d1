package com.newland.detb.exceptionmanage.controller;

import com.newland.detb.common.Result;
import com.newland.detb.util.JschUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2023-03-28 028 14:21:24
 * @description
 */
@Slf4j
@RestController
@RequestMapping("/manageTest")
public class ExceptionManageTestController {

    @Autowired
    private JschUtil jschUtil;

    @RequestMapping(value = "/hello",method = RequestMethod.GET)
    public Result hello() {
        return Result.success("请求成功");
    }

    // @RequestMapping(value = "/ssh",method = RequestMethod.GET)
    public Result testSSH() {
        Boolean flag = jschUtil.getFile();
        log.info("==========================标志位判断的结果是：{}",flag);
        return Result.success("请求成功");
    }

}
