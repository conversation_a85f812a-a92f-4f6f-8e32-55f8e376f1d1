package com.newland.detb.gostage.async;

import com.newland.detb.common.entity.HeExceptionDataBean;
import com.newland.detb.exceptionmanage.async.AsyncProduceGovernanceStepTask;
import com.newland.detb.exceptionmanage.async.BaseAsyncTask;
import com.newland.detb.exceptionmanage.entity.ExceptionScenario;
import com.newland.detb.exceptionmanage.entity.GovernanceStrategy;
import com.newland.detb.gostage.entity.ExceptionScenarioStage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * 生成治理策略-升台
 */
@Slf4j
@Component
public class AsyncQueryGovernanceStrategyStageTask extends BaseAsyncTask {

    @Lazy
    @Resource
    private AsyncProduceGovernanceStepStageTask asyncProduceGovernanceStepStageTask;

    @Async("taskExecutor")
    public void executeAsync(String traceId, HeExceptionDataBean heExceptionDataBean, ExceptionScenarioStage exceptionScenarioStage,int randomNumber) {
        asyncPrintLogInfo(traceId,"查询治理策略");
        updateHeExceptionDataWithRemark(heExceptionDataBean,2,"正在执行查询治理策略模块");
        updateProgress(heExceptionDataBean,2,1,"doing","正在执行查询治理策略模块");
        asyncPrintLogInfo(traceId,2,1,"正在查询异常治理策略");
        //正常执行后续生成治理步骤操作
        asyncPrintLogInfo(traceId,2,1,"已经查询到异常治理策略，治理策略：" + exceptionScenarioStage.getBusinessHandles().get(randomNumber).getHandle());
        updateProgress(heExceptionDataBean,2,1,"success","治理策略查询成功:" + exceptionScenarioStage.getBusinessHandles().get(randomNumber).getHandle());

        //调用生成治理策略异步任务处理
        asyncProduceGovernanceStepStageTask.executeAsync(traceId,heExceptionDataBean,exceptionScenarioStage,randomNumber);
//        List<GovernanceStrategy> governanceStrategies = governanceStrategyService.queryBySceneCode(exceptionScenarioStage.getSceneCode().toString());
//        if (governanceStrategies != null && governanceStrategies.size() == 1) {
//            //正常执行后续生成治理步骤操作
//            GovernanceStrategy strategy = governanceStrategies.get(0);
//            asyncPrintLogInfo(traceId,2,1,"已经查询到异常治理策略，编码：" + strategy.getStrategyCode() + "，治理策略：" + strategy.getRemark());
//            updateProgress(heExceptionDataBean,2,1,"success","治理策略查询成功:" + governanceStrategies.get(0).getRemark());
//            //调用生成治理策略异步任务处理
//            asyncProduceGovernanceStepTask.executeAsync(traceId,heExceptionDataBean,exceptionScenarioStage,strategy);
//        }else {
//            asyncPrintErrorLogInfo(traceId,2,1,"查询到的异常治理策略不唯一，请管理员检查");
//            updateHeExceptionDataWithRemark(heExceptionDataBean,6,"查询到的治理策略不唯一，请手动处理，治理结束");
//            updateProgress(heExceptionDataBean,2,1,"fail","查询到的治理策略不唯一，请手动处理");
//            updateProgress(heExceptionDataBean,5,1,"fail","查询到的治理策略不唯一，请手动处理，治理结束");
//            updateWorkOrderWhenFailCase(heExceptionDataBean.getDataId(), "查询到的治理策略不唯一，需要手动处理");
//        }
    }
}
