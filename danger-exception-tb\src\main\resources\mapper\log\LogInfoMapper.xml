<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.newland.detb.log.mapper.LogInfoMapper">

    <insert id="save">
        insert into he_log_info
            (
            username,
            operation,
            method,
            params,
            execute_time,
            ip
            )
            values (
                    #{username,jdbcType=VARCHAR},
                    #{operation,jdbcType=VARCHAR},
                    #{method,jdbcType=VARCHAR},
                    #{params,jdbcType=CLOB},
                    #{executeTime,jdbcType=BIGINT},
                    #{ip,jdbcType=VARCHAR}
                   )
    </insert>

    <delete id="deleteLogInfoById">
        delete from he_log_info where id = #{id}
    </delete>

    <delete id="deleteLogInfoBatch">
        DELETE FROM he_log_info
        WHERE id IN
        <foreach collection="array" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="queryLogInfoById" resultType="com.newland.detb.log.entity.LogInfo">
        select id,username,operation,method,params,execute_time,ip,create_time from he_log_info where id = #{id}
    </select>

    <select id="queryLogInfo" resultType="com.newland.detb.log.entity.LogInfo">
        select id,
               username,
               operation,
               method,
               params,
               execute_time,
               ip,
               create_time
        from he_log_info
        <where>
            <if test="operation != null and operation != ''">
                operation like concat('%',#{operation},'%')
            </if>
        </where>
        order by id desc
    </select>
</mapper>