package com.newland.detb.exceptionmanage.mapper;

import com.newland.detb.exceptionmanage.entity.ExceptionProgress;
import com.newland.detb.exceptionmanage.entity.ProgressDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【HE_EXCEPTION_PROGRESS(异常治理进度表)】的数据库操作Mapper
* @createDate 2023-04-20 11:19:09
* @Entity com.newland.detb.exceptionmanage.entity.ExceptionProgress`
*/
@Mapper
public interface ExceptionProgressMapper {

    /**
     * 定时任务扫描工单表时，当扫描到异常后，需要同步初始化进度表，需要初始化创建6条数据
     * @param dataId 工单表数据的id
     * @param list 保存着每一个ProgressDTO实体类的对象，有两个属性，parentProgress和childProgress
     * @return
     */
    int insertInitData(@Param("dataId") Long dataId, @Param("list") List<ProgressDTO> list) ;

    /**
     * 根据工单表的dataId修改进度表的对应子状态
     * @param dataId
     * @return
     */
    int updateChildProgressByDataId(@Param("dataId") Long dataId, @Param("parentProgress") int parentProgress, @Param("childProgress") int childProgress, @Param("execStatus") String execStatus, @Param("execResult") String execResult, @Param("finishTime")Date finishTime);


}
