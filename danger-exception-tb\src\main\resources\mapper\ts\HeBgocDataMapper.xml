<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.newland.detb.troubleshooting.mapper.HeBgocDataMapper">

    <insert id="insertHeBgocRecord">
insert into he_bgoc_record
  (RESOURCEID, SUBTASKNAME, RESOURCENAME, DN, TYPE, SUBTASKID, CREATE_TIME)
values
            (
                #{resourceid,jdbcType=VARCHAR},
                #{subtaskname,jdbcType=VARCHAR},
                #{resourcename,jdbcType=VARCHAR},
                #{dn,jdbcType=VARCHAR},
                #{type,jdbcType=VARCHAR},
                #{subtaskid,jdbcType=VARCHAR},
                #{createTime,jdbcType=DATE}
            )
    </insert>

    <insert id="insertHeBgocPerfDetail">
insert into he_bgoc_perf_detail (RESOURCEID, KPIVALUE, MODELID, MODULE, KPIID, KPINAME, MAINMODELID, BELONGTOSYSTEM, IP_ADDRESS, MODELNAME, MAINMODELNAME, TENANTID, STARTTIME, ENDTIME, KEY)
values
(
                #{resourceId,jdbcType=VARCHAR},
                #{kpiValue,jdbcType=VARCHAR},
                #{modelId,jdbcType=VARCHAR},
                #{module,jdbcType=VARCHAR},
                #{kpiId,jdbcType=VARCHAR},
                #{kpiName,jdbcType=VARCHAR},
                #{mainModelId,jdbcType=VARCHAR},
                #{belongToSystem,jdbcType=VARCHAR},
                #{ip_address,jdbcType=VARCHAR},
                #{modelName,jdbcType=VARCHAR},
                #{mainModelName,jdbcType=VARCHAR},
                #{tenantId,jdbcType=VARCHAR},
                #{startTime,jdbcType=DATE},
                #{endTime,jdbcType=DATE},
                #{key,jdbcType=VARCHAR}
)
    </insert>
    <update id="truncateBGocRecordTable">
        TRUNCATE TABLE he_bgoc_record
    </update>
    <update id="truncateBGocPerfTable">
        TRUNCATE TABLE he_bgoc_perf_detail
    </update>
</mapper>
