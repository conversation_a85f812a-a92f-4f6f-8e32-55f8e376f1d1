package com.newland.detb.quartz.mapper;

import com.newland.detb.common.entity.HeExceptionDataBean;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
@Mapper
public interface HeExceptionDataMapper {
    //隐患排查异常数据
    List<HeExceptionDataBean> selectExceptionAlarmArk();
    //得到所有异常数据
    List<HeExceptionDataBean> selectAllExceptionData();
    //插入异常工单表
    int insertExceptionData(HeExceptionDataBean heExceptionData);

    /**
     * 根据id修改工单表的status
     * @param id
     * @return
     */
    int updateExceptionDataById(@Param("id") Long id, @Param("status") int status);

    /**
     * 根据id修改工单表的status和remark
     * @param id
     * @return
     */
    int updateExceptionDataRemarkById(@Param("id") Long id, @Param("status") int status,@Param("remark") String remark);

    /**
     * 查询所有工单表数据status为0的记录
     * @return
     */
    List<HeExceptionDataBean> selectExceptionDataOfStatusZero();

    /**
     * 根据id查询heExceptionData
     * @param dataId
     * @return
     */
    HeExceptionDataBean selectById(Long dataId);
}
