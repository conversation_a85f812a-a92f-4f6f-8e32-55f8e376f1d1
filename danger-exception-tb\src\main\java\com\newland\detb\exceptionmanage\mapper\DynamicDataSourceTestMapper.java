package com.newland.detb.exceptionmanage.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.newland.detb.exceptionmanage.entity.WorkOrder;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-07-31 031 17:57:33
 * @description
 */
@Mapper
@DS("slave")
public interface DynamicDataSourceTestMapper {

    /**
     * 测试查询治理工单表数据
     * @return
     */
    List<WorkOrder> getWorkOrderList();
}
