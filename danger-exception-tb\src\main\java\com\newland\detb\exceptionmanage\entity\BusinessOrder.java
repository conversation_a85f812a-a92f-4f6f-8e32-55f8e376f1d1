package com.newland.detb.exceptionmanage.entity;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.NumberFormat;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024-01-04 004 16:46:22
 * @description 业务异常单导出excel对应的bean
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class BusinessOrder implements Serializable {

    /**
     * 业务异常单编码
     */
    @ExcelProperty("业务类型")
    @ColumnWidth(20)
    private String businessCode;

    /**
     * 操作编码
     */
    @ExcelProperty("操作类型")
    @ColumnWidth(12)
    private String oprCode;

    /**
     * 异常场景
     */
    @ExcelProperty("异常大类")
    @ColumnWidth(15)
    private String errScene;

    /**
     * 错误描述
     */
    @ExcelProperty("异常描述")
    @ColumnWidth(105)
    private String errDesc;

    /**
     * 是否由工具自动处理
     */
    @ExcelProperty("是否自动处理")
    @ColumnWidth(13)
    private String isAutoRepaired;

    /**
     * 是否手工干预
     */
    @ExcelProperty("是否人为处理")
    @ColumnWidth(13)
    private String isManMade;

    /**
     * 数量
     */
    @ExcelProperty("数量")
    @ColumnWidth(6)
    private Long sumNvl;

    /**
     * 占比
     */
    @ExcelProperty("占比")
    @ColumnWidth(10)
    private Double proportion;
}
