package com.newland.detb.exceptionmanage.service.impl;

import cn.hutool.core.util.DesensitizedUtil;
import cn.hutool.core.util.ObjectUtil;
import com.newland.detb.exceptionmanage.entity.MessageInfo;
import com.newland.detb.exceptionmanage.service.MessageInfoService;
import com.newland.detb.exceptionmanage.mapper.MessageInfoMapper;
import lombok.val;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.stream.Collectors;

/**
* <AUTHOR>
* @description 针对表【HE_MESSAGE_INFO(信息同步表)】的数据库操作Service实现
* @createDate 2023-05-17 11:20:28
*/
@Service
public class MessageInfoServiceImpl implements MessageInfoService{

    @Resource
    private MessageInfoMapper messageInfoMapper;


    /**
     * 插入messageInfo
     *
     * @param messageInfo
     * @return
     */
    @Override
    public int insert(MessageInfo messageInfo) {
        return messageInfoMapper.insert(messageInfo);
    }

    /**
     * 根据id删除
     *
     * @param id
     * @return
     */
    @Override
    public int deleteById(Long id) {
        return messageInfoMapper.deleteById(id);
    }

    /**
     * 更新messageInfo
     *
     * @param messageInfo
     * @return
     */
    @Override
    public int update(MessageInfo messageInfo) {
        return messageInfoMapper.update(messageInfo);
    }

    /**
     * 根据id查询
     *
     * @param id
     * @return
     */
    @Override
    public MessageInfo selectById(Long id) {
        return messageInfoMapper.selectById(id);
    }

    /**
     * 查询全部
     *
     * @return
     */
    @Override
    public List<MessageInfo> selectAll(Long dataId,String name, String phone,String email,String message, String governLevel,Integer isComplete) throws CloneNotSupportedException {
        //进行脱敏展示
        List<MessageInfo> messageInfos = messageInfoMapper.selectAll(dataId, name, phone, email, message, governLevel, isComplete);
        if (messageInfos != null && messageInfos.size() > 0) {
            for (MessageInfo messageInfo : messageInfos) {
                messageInfo.setPhone(DesensitizedUtil.mobilePhone(messageInfo.getPhone()));
                messageInfo.setEmail(DesensitizedUtil.email(messageInfo.getEmail()));
            }
        }
        return messageInfos;
    }

    /**
     * 保存内部的负责人信息的messageInfo
     *
     * @param messageInfo
     * @return
     */
    @Override
    public int insertInner(MessageInfo messageInfo) {
        return messageInfoMapper.insertInner(messageInfo);
    }
}




