package com.newland.detb.exceptionmanage.controller;

import cn.com.nlsoft.cas.client.annotation.CasPermissionRequired;
import com.newland.detb.common.Result;
import com.newland.detb.exceptionmanage.async.AsyncFirstStepTask;
import com.newland.detb.log.annotation.SysLog;
import com.newland.detb.util.JschUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2023-04-04 004 11:40:29
 * @description 用于查询异常治理标志位状态
 */
@Slf4j
@RestController
@RequestMapping(value = "/flagbit")
public class FlagBitController {

    @Resource
    private JschUtil jschUtil;

    /**
     * 查询治理标志位，当标志位文件内容为空的时候，说明正常治理；当文件内容不为空时，治理流程终止
     * 即：正常执行治理流程，返回true；终止治理流程：false
     * @return
     */
    // @SysLog("查询治理标志位信息")
    @GetMapping(value = "query")
    @CasPermissionRequired("aimt.flag.query")
    public Result judgementFlagBit() {
        Boolean flag = jschUtil.getFileStatus();
        log.info("标志位判断成功，结果是：{}",flag);
        return Result.success(flag);
    }

    /**
     * 查询异常治理标记的值
     * @return
     */
    @GetMapping("get")
    @CasPermissionRequired("aimt.flag.query")
    public Result getFlagBit() {
        Boolean flagBit = AsyncFirstStepTask.flagBit;
        return Result.success(flagBit);
    }

    /**
     * 修改异常治理标记的值，用于测试数据
     * @return
     */
    @GetMapping("change")
    @CasPermissionRequired("aimt.flag.change")
    public Result changeFlagBit() {
        Boolean flagBit = AsyncFirstStepTask.flagBit;
        AsyncFirstStepTask.flagBit = !flagBit;
        return Result.success(AsyncFirstStepTask.flagBit);
    }
}
