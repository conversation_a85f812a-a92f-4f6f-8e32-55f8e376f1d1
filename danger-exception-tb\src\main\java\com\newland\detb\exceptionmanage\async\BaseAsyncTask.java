package com.newland.detb.exceptionmanage.async;

import com.newland.detb.common.entity.HeExceptionDataBean;
import com.newland.detb.exceptionmanage.entity.ExceptionScenario;
import com.newland.detb.exceptionmanage.entity.GovernanceStrategy;
import com.newland.detb.exceptionmanage.entity.WorkOrder;
import com.newland.detb.exceptionmanage.log.GovernanceLogger;
import com.newland.detb.exceptionmanage.mapper.ExceptionProgressMapper;
import com.newland.detb.exceptionmanage.service.GovernanceStrategyService;
import com.newland.detb.exceptionmanage.service.WorkOrderService;
import com.newland.detb.exceptionmanage.util.ProcessUtil;
import com.newland.detb.gostage.entity.ExceptionScenarioStage;
import com.newland.detb.quartz.service.HeExceptionDataService;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-05-11 011 14:35:17
 * @description 异步任务的父类
 */
@Slf4j
// @Lazy
@Component
public class BaseAsyncTask {

    //自定义治理日志配置
    private static final Logger LOG = GovernanceLogger.LOG;

    @Resource
    private HeExceptionDataService heExceptionDataService;

    @Resource
    private ExceptionProgressMapper exceptionProgressMapper;

    //执行本地脚本命令
    @Resource
    private ProcessUtil processUtil;
    public ProcessUtil getProcessUtil() {
        return this.processUtil;
    }

    // @Resource
    // private GovernanceStrategyService governanceStrategyService;
    //
    // public GovernanceStrategyService getGovernanceStrategyService() {
    //     return this.governanceStrategyService;
    // }

    /**
     * 获取治理工单service接口
     */
    @Resource
    private WorkOrderService workOrderService;
    public WorkOrderService getWorkOrderService() {
        return this.workOrderService;
    }

    /**
     * 根据data_id查询WorkOrder对象
     * @param dataId
     * @return
     */
    public WorkOrder getWorkOrder(Long dataId) {
        return this.workOrderService.getWorkOrderByDataId(dataId);
    }

    /**
     * 根据heExceptionData的id进行查询，然后更新workOrder对象的governanceRes
     * @param id
     * @param governanceRes
     */
    public void updateWorkOrderWhenFailCase(Long id,String governanceRes) {
        WorkOrder workOrder = getWorkOrder(id);
        workOrder.setGovernanceRes(governanceRes);
        getWorkOrderService().update(workOrder);
        log.info("更新WorkOrder对象的data_id:{},governanceRes:{}",id,governanceRes);
    }


    // @Lazy
    // @Resource
    // private AsyncSummaryMessageTask asyncSummaryMessageTask;
    // public AsyncSummaryMessageTask getAsyncSummaryMessageTask() {
    //     return this.asyncSummaryMessageTask;
    // }

    /**
     * 6、是否执行授权AsyncAuthTask
     * 7、执行异常治理AsyncGovernanceTask
     * 8、是否触发单项巡检AsyncSingleInspectionTask
     * 9、汇总同步消息AsyncSummaryMessageTask
     * @param heExceptionDataBean
     * @param exceptionScenario
     * @param workOrder
     */
    public void executeAsync(HeExceptionDataBean heExceptionDataBean, ExceptionScenario exceptionScenario, WorkOrder workOrder) {

    }

    /**
     * 6、是否执行授权AsyncAuthTask
     * 7、执行异常治理AsyncGovernanceTask
     * 8、是否触发单项巡检AsyncSingleInspectionTask
     * 9、汇总同步消息AsyncSummaryMessageTask
     * @param heExceptionDataBean
     * @param exceptionScenario
     * @param workOrder
     */
    public void executeAsync(String traceId,HeExceptionDataBean heExceptionDataBean, ExceptionScenario exceptionScenario, WorkOrder workOrder) {

    }

    /**
     * 5、生成治理工单AsyncGenerateWorkOrderTask
     * @param heExceptionDataBean
     * @param exceptionScenario
     * @param governanceStrategy
     * @param productionInfo
     * @param recoveryCommand
     */
    public void executeAsync(HeExceptionDataBean heExceptionDataBean, ExceptionScenario exceptionScenario, GovernanceStrategy governanceStrategy, Object productionInfo, String recoveryCommand) {

    }

    /**
     * 5、生成治理工单AsyncGenerateWorkOrderTask
     * @param heExceptionDataBean
     * @param exceptionScenario
     * @param governanceStrategy
     * @param stepFlag
     * @param recoveryCommand
     */
    public void executeAsync(String traceId,HeExceptionDataBean heExceptionDataBean, ExceptionScenario exceptionScenario, GovernanceStrategy governanceStrategy, Boolean stepFlag, String recoveryCommand) {

    }

    /**
     * 使用随机数生成治理步骤AsyncProduceGovernanceStepTask
     * @param traceId
     * @param heExceptionDataBean
     * @param exceptionScenarioStage
     * @param randomNumber
     */
    public void executeAsync(String traceId, HeExceptionDataBean heExceptionDataBean, ExceptionScenarioStage exceptionScenarioStage, int randomNumber) {

    }

    /**
     * 使用随机数生成治理步骤AsyncProduceGovernanceStepTask
     * @param traceId
     * @param heExceptionDataBean
     * @param exceptionScenario
     * @param workOrder
     * @param randomNumber
     */
    public void executeAsync(String traceId, HeExceptionDataBean heExceptionDataBean, ExceptionScenarioStage exceptionScenario, WorkOrder workOrder,int randomNumber) {

    }

    /**
     * 4、生成治理步骤AsyncProduceGovernanceStepTask
     * @param heExceptionDataBean
     * @param exceptionScenario
     * @param governanceStrategy
     */
    public void executeAsync(HeExceptionDataBean heExceptionDataBean, ExceptionScenario exceptionScenario, GovernanceStrategy governanceStrategy) {

    }

    /**
     * 4、生成治理步骤AsyncProduceGovernanceStepTask
     * @param heExceptionDataBean
     * @param exceptionScenario
     * @param governanceStrategy
     */
    public void executeAsync(String traceId,HeExceptionDataBean heExceptionDataBean, ExceptionScenario exceptionScenario, GovernanceStrategy governanceStrategy) {

    }

    /**
     * 2、异常二次确认AsyncSecondaryConfirmTask
     * 3、查询治理策略AsyncQueryGovernanceStrategyTask
     * @param heExceptionDataBean
     * @param exceptionScenario
     */
    public void executeAsync(HeExceptionDataBean heExceptionDataBean, ExceptionScenario exceptionScenario) {

    }

    /**
     * 2、异常二次确认AsyncSecondaryConfirmTask
     * 3、查询治理策略AsyncQueryGovernanceStrategyTask
     * @param heExceptionDataBean
     * @param exceptionScenario
     */
    public void executeAsync(String traceId,HeExceptionDataBean heExceptionDataBean, ExceptionScenario exceptionScenario) {

    }

    /**
     * 1、异常场景匹配AsyncSceneMatchingTask
     * @param heExceptionDataBeans
     */
    public void executeAsync(List<HeExceptionDataBean> heExceptionDataBeans) {

    }


    /**
     * 更新异常数据工单表状态：status
     * 添加remark描述
     * @param heExceptionDataBean
     * @param status
     * @param remark
     * @return
     */
    public int updateHeExceptionDataWithRemark(HeExceptionDataBean heExceptionDataBean,int status,String remark) {
        return heExceptionDataService.updateExceptionDataStatusById(heExceptionDataBean,status,remark);
    }

    /**
     * 更新进度表父子节点状态
     * @param heExceptionDataBean 异常数据工单表数据
     * @param parentNum 父节点
     * @param childNum 字节点
     * @param statusDesc 执行的状态：success、fail、doing、waiting；默认是waiting，表示正在执行
     * @param resultDesc 执行的结果
     * @return
     */
    public int updateProgress(HeExceptionDataBean heExceptionDataBean,int parentNum,int childNum,String statusDesc,String resultDesc) {
        return exceptionProgressMapper.updateChildProgressByDataId(heExceptionDataBean.getDataId(), parentNum,childNum,statusDesc,resultDesc,new Date());
    }

    /**
     * 异步日志打印
     * @param num1 父节点
     * @param num2 子节点
     * @param desc 描述
     */
    public void asyncPrintLogInfo(int num1,int num2,String desc) {
        LOG.info("[{}-{}]------>{}",num1,num2,desc);
    }

    /**
     * 带跟踪id的异步打印方法
     * @param traceId 唯一id
     * @param num1 父节点
     * @param num2 子节点
     * @param desc 描述信息
     */
    public void asyncPrintLogInfo(String traceId,int num1,int num2,String desc) {
        LOG.info("[{}-{}-{}]------>{}",traceId,num1,num2,desc);
    }

    /**
     * 打印异常日志信息
     * @param num1 父节点
     * @param num2 子节点
     * @param desc 描述
     */
    public void asyncPrintErrorLogInfo(int num1,int num2,String desc) {
        LOG.error("[{}-{}]------>{}",num1,num2,desc);
    }

    /**
     * 带唯一id打印异常日志信息
     * @param num1 父节点
     * @param num2 子节点
     * @param desc 描述
     */
    public void asyncPrintErrorLogInfo(String traceId,int num1,int num2,String desc) {
        LOG.error("[{}-{}-{}]------>{}",traceId,num1,num2,desc);
    }

    /**
     * 异步任务打印开头
     * @param desc 描述
     */
    public void asyncPrintLogInfo(String desc) {
        LOG.info("【======{}======】",desc);
        LOG.info("正在执行【{}】操作，线程名称：{}",desc,Thread.currentThread().getName());
    }

    /**
     * 带唯一id异步任务打印开头
     * @param desc 描述
     */
    public void asyncPrintLogInfo(String traceId,String desc) {
        LOG.info("[{}]-【======{}======】",traceId,desc);
        LOG.info("正在执行【{}】操作，线程名称：{}",desc,Thread.currentThread().getName());
    }

}
