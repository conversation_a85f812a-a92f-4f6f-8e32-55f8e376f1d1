package com.newland.detb.quartz.controller;

import cn.com.nlsoft.cas.client.annotation.CasPermissionRequired;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.newland.detb.common.ResEnum;
import com.newland.detb.common.Result;
import com.newland.detb.log.annotation.SysLog;
import com.newland.detb.quartz.entity.QuartzTask;
import com.newland.detb.quartz.service.QuartzJobService;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.validator.constraints.Length;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @date 2023-03-17 017 10:35:10
 * @description 实现对定时任务的添加和暂停、恢复
 */
@Slf4j
@RestController
@RequestMapping("/quartz")
@Validated
public class QuartzJobController {

    @Autowired
    private QuartzJobService quartzJobService;

    @GetMapping("getAll")
    @CasPermissionRequired("aimt.quartz.query")
    public Result getAllQuartz(@RequestParam(value = "currentPage",defaultValue = "1") int currentPage,
                               @RequestParam(value = "pageSize",defaultValue = "10") int pageSize,
                               @RequestParam(value = "jobName",required = false,defaultValue = "") @Length(min = 0,max = 10,message = "任务名称超长") String jobName,
                               @RequestParam(value = "jobGroup",required = false,defaultValue = "") @Length(min = 0,max = 10,message = "任务分组名称超长") String jobGroup,
                               @RequestParam(value = "jobStatus",required = false,defaultValue = "") @Length(min = 0,max = 1,message = "任务状态只能为运行或暂停") String jobStatus) {
        PageHelper.startPage(currentPage,pageSize);
        List<QuartzTask> quartzTasks = quartzJobService.selectAllTasks(jobName, jobGroup, jobStatus);
        PageInfo<QuartzTask> pageInfo = new PageInfo<>(quartzTasks);
        Map<String, Object> map = new ConcurrentHashMap<>();
        map.put("list",pageInfo.getList());
        map.put("total",pageInfo.getTotal());
        return Result.success(map);
    }

    @GetMapping("getById/{id}")
    @CasPermissionRequired("aimt.quartz.query")
    public Result getByIdQuartz(@PathVariable("id") int id) {
        QuartzTask quartzTask = quartzJobService.selectTaskById(id);
        return Result.success(quartzTask);
    }

    @SysLog("新增定时任务")
    @RequestMapping(value = "add",method = RequestMethod.POST)
    @CasPermissionRequired("aimt.quartz.change")
    public Result add(@RequestBody @Validated QuartzTask quartzTask) {
        log.info("收到的任务信息：{}",quartzTask);
        int task = quartzJobService.insertTask(quartzTask);
        if (task < 1) {
            return Result.error(ResEnum.error_code,"定时任务添加失败");
        }
        return Result.success("定时任务添加成功");
    }

    @SysLog("暂停定时任务")
    @RequestMapping(value = "pause",method = RequestMethod.GET)
    @CasPermissionRequired("aimt.quartz.change")
    public Result pause(@RequestParam("id") @Min(value = 1,message = "请求的任务ID错误") @Max(value = Integer.MAX_VALUE,message = "请求的任务ID太大") int id) {
        try {
            quartzJobService.pause(id);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.error(ResEnum.error_code,"定时任务暂停失败" + e.getMessage());
        }
        return Result.success("定时任务暂停成功");
    }

    @SysLog("重启定时任务")
    @RequestMapping(value = "resume",method = RequestMethod.GET)
    @CasPermissionRequired("aimt.quartz.change")
    public Result resume(@RequestParam("id") @Min(value = 1,message = "请求的任务ID错误") @Max(value = Integer.MAX_VALUE,message = "请求的任务ID太大") int id) {
        try {
            quartzJobService.resume(id);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.error(ResEnum.error_code,"定时任务恢复失败" + e.getMessage());
        }
        return Result.success("定时任务恢复成功");
    }

    @SysLog("删除定时任务")
    @RequestMapping(value = "del/{id}",method = RequestMethod.GET)
    @CasPermissionRequired("aimt.quartz.change")
    public Result delete(@PathVariable("id") @Min(value = 1,message = "请求的任务ID错误") @Max(value = Integer.MAX_VALUE,message = "请求的任务ID太大") int id) {
        try {
            int delFlag = quartzJobService.deleteTaskById(id);
            if (delFlag < 1) {
                return Result.error(ResEnum.error_code,"定时任务删除失败");
            }
        } catch (Exception e) {
            e.printStackTrace();
            return Result.error(ResEnum.error_code,"定时任务删除失败" + e.getMessage());
        }
        return Result.success("定时任务删除成功");
    }

    @SysLog("更新定时任务cron表达式")
    @RequestMapping(value = "update",method = RequestMethod.GET)
    @CasPermissionRequired("aimt.quartz.change")
    public Result updateCron(@RequestParam("cron") @Length(min = 1,max = 16,message = "请求的任务cron表达式长度不合法") String cron, @RequestParam("id") @Min(value = 1,message = "请求的任务ID错误") @Max(value = Integer.MAX_VALUE,message = "请求的任务ID太大") int id) {
        quartzJobService.updateQuartzJobCron(cron,id);
        // int res = quartzJobService.updateTaskCron(cron, id);
        // if (res > 0) {
        //     return "定时任务cron表达式修改成功";
        // }else {
        //     return "定时任务cron表达式修改失败";
        // }
        return Result.success("定时任务cron修改完成");
    }

}
