package com.newland.detb.troubleshooting.service.impl;


import com.newland.detb.troubleshooting.entity.BgocPerfDetailBean;
import com.newland.detb.troubleshooting.entity.BgocRecordBean;
import com.newland.detb.troubleshooting.mapper.HeBgocDataMapper;
import com.newland.detb.troubleshooting.service.BgocDataService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

/**
* <AUTHOR>
* @description bgoc数据记录
* @createDate 2023-04-17 16:54:20
*/
@Service
@Slf4j
public class BgocDataServiceImpl implements BgocDataService {


    @Resource
    private HeBgocDataMapper heBgocDataMapper;


    @Override
    @Transactional
    public int insertHeBgocRecord(BgocRecordBean bgocRecordBean) {
        return heBgocDataMapper.insertHeBgocRecord(bgocRecordBean);
    }

    @Override
    @Transactional
    public int insertHeBgocPerfDetail(BgocPerfDetailBean BgocPerfDetailBean) {
        return heBgocDataMapper.insertHeBgocPerfDetail(BgocPerfDetailBean);
    }
}
