package com.newland.detb.exceptionmanage.mapper;

import com.newland.detb.exceptionmanage.entity.WorkOrder;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【HE_WORK_ORDER(治理工单信息表)】的数据库操作Mapper
* @createDate 2023-05-10 15:28:03
* @Entity com.newland.detb.exceptionmanage.entity.WorkOrder
*/
@Mapper
public interface WorkOrderMapper {

    /**
     * 插入治理工单表
     * @param workOrder
     * @return
     */
    int insert(WorkOrder workOrder);

    /**
     * 根据id查询
     * @param id
     * @return
     */
    WorkOrder selectById(Long id);

    /**
     * 查询全部治理工单表数据
     * @return
     */
    List<WorkOrder> selectAll();

    /**
     * 根据id删除
     * @param id
     * @return
     */
    int deleteById(Long id);

    /**
     * 更新工单表数据
     * @param workOrder
     * @return
     */
    int update(WorkOrder workOrder);

    /**
     * 更新是否授权
     * @param isAuth
     * @param id
     * @return
     */
    int updateIsAuth(@Param("isAuth") int isAuth, @Param("id") Long id);

    /**
     * 更新是否触发单项巡检
     * @param isInspection
     * @param id
     * @return
     */
    int updateIsInspection(@Param("isInspection") int isInspection, @Param("id") Long id);

    /**
     * 更新最终的治理结果
     * @param governanceRes
     * @return
     */
    int updateGovernanceResInt(@Param("governanceRes") String governanceRes,@Param("id") Long id);

    /**
     * 根据data_id查询WorkOrder对象
     * @param dataId
     * @return
     */
    WorkOrder getWorkOrderByDataId(Long dataId);

    /**
     * @description 分页查询治理工单表
     * @param dataId
     * @param appName
     * @param env
     * @return
     */
    List<WorkOrder> findPage(@Param("dataId")Long dataId, @Param("appName") String appName, @Param("env") String env, @Param("isAuth") Integer isAuth,@Param("isInspection") Integer isInspection);

    /**
     * 查询出四个月之前的全部数据，因为数据迁移到历史表中的规则是：3+1（前三个月+当前月的数据需要留在原表中，其他的数据迁移到历史表中）
     * @return
     */
    List<WorkOrder> selectWorkOrderDataForMoveToHistory();

    /**
     * 根据dataId给workOrder表设置traceId
     * @param traceId
     * @param dataId
     * @return
     */
    int updateTraceIdByDataId(@Param("traceId") String traceId,@Param("dataId") Long dataId);
}




