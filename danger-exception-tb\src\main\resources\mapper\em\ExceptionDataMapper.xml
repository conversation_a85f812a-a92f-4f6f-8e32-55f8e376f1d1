<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.newland.detb.exceptionmanage.mapper.ExceptionDataMapper">
    <delete id="delByDataId">
        delete from he_excepion_data where data_id = #{dataId}
    </delete>

    <select id="queryExceptionDataBeans" resultType="com.newland.detb.common.entity.HeExceptionDataBean">
        select data_id,
               environment,
               alarm_type,
               alarm_level,
               home_module,
               process_name,
               child_process_name,
               host_name,
               host_ip,
               exception_desc,
               status,
               create_time,
               remark
        from he_excepion_data
        order by data_id desc
    </select>
    <select id="selectExceptionDataWithProgress"
            resultType="com.newland.detb.exceptionmanage.entity.ExceptionDataVO" resultMap="exceptionDataWithProgress">
        select  a.data_id,
               a.environment,
               a.alarm_type,
               a.home_module,
               a.process_name,
               a.exception_desc,
               a.status,
               a.create_time,
               a.remark,
               a.alarm_level,
               a.child_process_name,
               a.host_name,
               a.host_ip
        from he_excepion_data a

        <where>
            <if test="dataId != null">
                a.data_id = #{dataId}
            </if>
            <if test="env != ''">
                and a.environment = #{env}
            </if>
            <if test="keyWords != ''">
                and a.alarm_type like concat('%',#{keyWords},'%')
            </if>
            <if test="status != ''">
                and a.status = #{status}
            </if>
            <if test="alarmLevel != ''">
                and a.alarm_level = #{alarmLevel}
            </if>
        </where>
    order by a.create_time desc
    </select>

    <resultMap id="exceptionDataWithProgress" type="com.newland.detb.exceptionmanage.entity.ExceptionDataVO">
        <id column="data_id" property="dataId"/>
        <result column="environment" property="environment"/>
        <result column="alarm_type" property="alarmType"/>
        <result column="home_module" property="homeModule"/>
        <result column="process_name" property="processName"/>
        <result column="exception_desc" property="exceptionDesc"/>
        <result column="status" property="status"/>
        <result column="create_time" property="createTime"/>
        <result column="remark" property="remark"/>
        <result column="alarm_level" property="alarmLevel"/>
        <result column="child_process_name" property="childProcessName"/>
        <result column="host_name" property="hostName"/>
        <result column="host_ip" property="hostIp"/>
        <collection property="exceptionProgressList" ofType="com.newland.detb.exceptionmanage.entity.ExceptionProgress" select="getProgress" column="{dataId=data_id}">

        </collection>
    </resultMap>

    <select id="getProgress" resultMap="progressList">
        select b.id,
               b.data_id as data_id2,
               b.parent_progress,
               b.child_progress,
               b.exec_status,
               b.exec_result,
               b.create_time as create_time2,
               b.finish_time
        from he_excepion_data a
                 left join he_exception_progress b
                           on a.data_id = b.data_id
                    where b.data_id = #{dataId}
    </select>
    <select id="selectExceptionDataForMoveToHistory" resultType="com.newland.detb.common.entity.HeExceptionDataBean">
        SELECT a.data_id,
        a.environment,
        a.alarm_type,
        a.home_module,
        a.process_name,
        a.exception_desc,
        a.status,
        a.remark,
        a.alarm_level,
        a.child_process_name,
        a.host_name,
        a.host_ip,
        a.create_time
        FROM he_excepion_data a
        WHERE a.create_time &lt; DATE_SUB(DATE_FORMAT(CURDATE(), '%Y-%m-01'), INTERVAL 3 MONTH)
    </select>

    <resultMap id="progressList" type="com.newland.detb.exceptionmanage.entity.ExceptionProgress">
        <id column="id" property="id"/>
        <result column="data_id2" property="dataId"/>
        <result column="parent_progress" property="parentProgress"/>
        <result column="child_progress" property="childProgress"/>
        <result column="exec_status" property="execStatus"/>
        <result column="exec_result" property="execResult"/>
        <result column="create_time2" property="createTime"/>
        <result column="finish_time" property="finishTime"/>
    </resultMap>
</mapper>