<?xml version="1.0" encoding="UTF-8" ?>
        <!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.newland.detb.quartz.mapper.HeExceptionDataMapper">

    <update id="updateExceptionDataById">
        update he_excepion_data set status = #{status} where data_id = #{id}
    </update>
    <update id="updateExceptionDataRemarkById">
        update he_excepion_data set status = #{status},remark = #{remark} where data_id = #{id}
    </update>

    <select id="selectAllExceptionData" resultType="com.newland.detb.common.entity.HeExceptionDataBean">
        select data_id,
               environment,
               alarm_type,
               alarm_level,
               home_module,
               process_name,
               child_process_name,
               host_name,
               host_ip,
               exception_desc,
               status,
               create_time,
               remark
        from he_excepion_data
        order by data_id desc
    </select>
    <select id="selectExceptionAlarmArk" resultType="com.newland.detb.common.entity.HeExceptionDataBean">
    select a.env_mark environment,
           a.warm_level alarmLevel,
           a.module_type alarmType,
           a.node_name processName,
           a.zi_node_name childProcessName,
           a.machiname_name hostName,
           a.machiname_ip hostIp,
           a.warm_content exceptionDesc,
           a.module_name homeModule from exception_alarm_ark a where a.warm_level in ('严重告警','重要告警')
    </select>
    <insert id="insertExceptionData">
        insert into he_excepion_data
          (
           ENVIRONMENT,
           ALARM_TYPE,
           HOME_MODULE,
           PROCESS_NAME,
           EXCEPTION_DESC,
           STATUS,
           CREATE_TIME,
           REMARK,
           ALARM_LEVEL,
           CHILD_PROCESS_NAME,
           HOST_NAME,
           HOST_IP)
        values(
            #{environment,jdbcType=VARCHAR},
            #{alarmType,jdbcType=VARCHAR},
            #{homeModule,jdbcType=VARCHAR},
            #{processName,jdbcType=VARCHAR},
            #{exceptionDesc,jdbcType=VARCHAR},
            #{status,jdbcType=VARCHAR},
            #{createTime,jdbcType=DATE},
            #{remark,jdbcType=VARCHAR},
            #{alarmLevel,jdbcType=VARCHAR},
            #{childProcessName,jdbcType=VARCHAR},
            #{hostName,jdbcType=VARCHAR},
            #{hostIp,jdbcType=VARCHAR}
        )
    </insert>

    <select id="selectExceptionDataOfStatusZero" resultType="com.newland.detb.common.entity.HeExceptionDataBean">
        select data_id,
               environment,
               alarm_type,
               alarm_level,
               home_module,
               process_name,
               child_process_name,
               host_name,
               host_ip,
               exception_desc,
               status,
               create_time,
               remark
        from he_excepion_data
        where status = 0
        order by data_id desc
    </select>
    <select id="selectById" resultType="com.newland.detb.common.entity.HeExceptionDataBean">
        select data_id,
               environment,
               alarm_type,
               alarm_level,
               home_module,
               process_name,
               child_process_name,
               host_name,
               host_ip,
               exception_desc,
               status,
               create_time,
               remark
        from he_excepion_data
        where data_id = #{dataId}
    </select>
</mapper>