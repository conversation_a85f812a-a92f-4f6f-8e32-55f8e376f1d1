package com.newland.detb.tools.alarmMonitoring.service.impl;

import com.newland.detb.tools.alarmMonitoring.entity.AlarmInfo;
import com.newland.detb.tools.alarmMonitoring.mapper.AlarmMapper;
import com.newland.detb.tools.alarmMonitoring.service.AlarmService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
public class AlarmServiceImpl implements AlarmService {

    @Resource
    private AlarmMapper alarmMapper;

    /**
     * 保存告警信息
     * @param alarmInfo 告警信息
     * @return
     */
    @Override
    public Integer saveAlarm(AlarmInfo alarmInfo) {
        return alarmMapper.saveAlarm(alarmInfo);
    }
}
