# cas配置
cas:
  server:
    addr: "http://**************:8600"
    isLegacyVersion: true
  client:
    id: "aimt"
    jwtKey: "aimt"
    loginRequiredStatusCode: -1
    permissionDeniedStatusCode: -2
    clientLoginUrl: "/cas/login"
    frontendServerWhitelist: "https?://127\\.0\\.0\\.\\d+:8[67]09"
    tokenMaxAgeInMinutes: 360
    permsValidityPeriodInMinutes: 1

#业务标记
business:
  name: "EBOSS"

# 数据库连接
spring:
  servlet:
    multipart:
      max-file-size: 50MB
      max-request-size: 50MB
  datasource:
    dynamic:
      primary: master #设置默认的数据源或者数据源组,默认值即为master
      strict: false #严格匹配数据源,默认false. true未匹配到指定数据源时抛异常,false使用默认数据源
      datasource:
        master:
          driver-class-name: oracle.jdbc.driver.OracleDriver
          # 235预出账库
          url: **********************************************
          username: ENC(G3VgAuzb5VXP1uurvVuTZQ==)
          password: ENC(e5wuF1GZS95bw6ZKcz7CHQva6dGNNna4)
#          因预出账库升级操作系统，迁移到237
#          url: *********************************************
#          username: ENC(BDw4PiLYyLU44yubLrofLA==)
#          password: ENC(R9t/5402xrV1i5Xdhxe7a0RXkDji95xO)
        slave:
          driver-class-name: oracle.jdbc.driver.OracleDriver
          # 232云库
          url: *********************************************
          username: ENC(XrrvahLhHrdNt/gDwaEb47yt7CbXX5M4)
          password: ENC(/a96cN6bnD9yAYVw2rb3idReHLQybPYE)
        alarm:
          driver-class-name: oracle.jdbc.driver.OracleDriver
          url: **********************************************
          username: ENC(RswOd/sDXWymHjFsgCVgug==)
          password: ENC(J+b4eYLUwMJYpdwBWkJoCEmAgvzWI8SN)
#    driver-class-name: oracle.jdbc.driver.OracleDriver
#    # 开发
#    url: **********************************************
#    username: ENC(G3VgAuzb5VXP1uurvVuTZQ==)
#    password: ENC(e5wuF1GZS95bw6ZKcz7CHQva6dGNNna4)
  #    定时任务Quartz配置
  quartz:
    #    指定job的存储类型：内存/jdbc
    job-store-type: JDBC
    jdbc:
      #      是否初始化数据库 never,always,embedded
      initialize-schema: NEVER
    #      调度器名称
    #    scheduler-name: quartzScheduler
    #    设置应用启动后，延迟多长时间启动调度器
    startup-delay: 5s
    #    设置是否自动启动quartz
    auto-startup: true
    #    是否等待所有的job完成调度后才关闭scheduler
    wait-for-jobs-to-complete-on-shutdown: true
    #    是否覆盖已存在的任务，用于quartz集群，设置为true，则quartzScheduler启动会更新已存在的job
    overwrite-existing-jobs: false
    #    用于客户化定义quartz的属性
    properties:
      org:
        quartz:
          scheduler:
            instanceId: AUTO
            instanceName: demo-quartz
          jobStore:
            # 是否为集群
            isClustered: false
            clusterCheckinInterval: 20000
          # 线程池配置
          threadPool:
            threadCount: 10
  # kafka配置
  kafka:
    #   第一个kafka-B-GOC
    one:
      bootstrap-servers: 10.250.161.11:19092,10.250.161.13:19092,10.250.161.14:19092
#      producer:
#        key-serializer: org.apache.kafka.common.serialization.StringSerializer
#        value-serializer: org.apache.kafka.common.serialization.StringSerializer
      consumer:
        enable-auto-commit: true
        key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
        value-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      properties:
        security:
          protocol: SASL_PLAINTEXT
        sasl:
          mechanism: PLAIN
          jaas:
            config: ENC(YeciEmjbzoJEJj+0SAH+IvGRy/32xoArvilwaqpUc2Ao5Fz8Dwa/3Iwe+ep76TzMPJXHsoPRkAJ7QzU9jY5AMd+S3qGiDfmUPkOfARooLClRru/qeKNHKcqEr9w8YVwEsrbjgGjVJIvrcLy2bYF4n6551hSlm2Fh)
#        properties:
          # SASL鉴权方式
#          sasl.mechanism: PLAIN
#          # 加密协议
#          security.protocol: SASL_PLAINTEXT
#          # 设置jaas帐号和密码
#          sasl.jaas.config: ENC(YeciEmjbzoJEJj+0SAH+IvGRy/32xoArvilwaqpUc2Ao5Fz8Dwa/3Iwe+ep76TzMPJXHsoPRkAJ7QzU9jY5AMd+S3qGiDfmUPkOfARooLClRru/qeKNHKcqEr9w8YVwEsrbjgGjVJIvrcLy2bYF4n6551hSlm2Fh)
          #        配置kafka用户和密码
#          security:
#            protocol: SASL_PLAINTEXT
#          sasl:
#            mechanism: PLAIN
#            jaas:
#              config: ENC(YeciEmjbzoJEJj+0SAH+IvGRy/32xoArvilwaqpUc2Ao5Fz8Dwa/3Iwe+ep76TzMPJXHsoPRkAJ7QzU9jY5AMd+S3qGiDfmUPkOfARooLClRru/qeKNHKcqEr9w8YVwEsrbjgGjVJIvrcLy2bYF4n6551hSlm2Fh)
    #   第二个kafka-C-GOC
    two:
      #      bootstrap-servers: **************:9001
      bootstrap-servers: 10.255.83.18:9092,10.255.82.23:9092,10.255.82.30:9092
      producer:
        key-serializer: org.apache.kafka.common.serialization.StringSerializer
        value-serializer: org.apache.kafka.common.serialization.StringSerializer
      consumer:
        enable-auto-commit: true
        key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
        value-deserializer: org.apache.kafka.common.serialization.StringDeserializer

# httpclient配置
http:
  maxTotal: 100         #最大连接数
  defaultMaxPerRoute: 20  #并发数
  connectionTimeout: 1000   #创建连接的最长时间
  connectionRequestTimeout: 500  #从连接池中获取到连接的最长时间
  socketTimeout: 10000 #数据传输的最长时间
  staleConnectionCheckEnabled: true  #提交请求前测试连接是否可用
  validateAfterInactivity: 3000000   #可用空闲连接过期时间,重用空闲连接时会先检查是否空闲时间超过这个时间，如果超过，释放socket重新建立

# 用于远程连接获取标志位文件内容
flagbit:
  host: *************
  port: 22
  username: ENC(+kVhQXNNV9IxeIScyvMJJtzPtJgEaTtz)
  password: ENC(L2DZR/OymMLXGB35gbQGVSLPrjCsQTv9)
  filepath: /ebosshome/eboss/app_nas/mt_tools/eboss_monitor/smsend
  filename: sendflag.txt
  command: cat sendflag.txt | wc -l

# 二次确认连接主机
script:
  host: **************
  port: 22
  username: ENC(rHz3hoXumyS9mIect1gUxA==)
  password: ENC(XSVYvxkOQBRn8IzTy6EQo5Ne/4Rn6mvL)
  scpath: /home/<USER>/liuning/governance
  governpath: /home/<USER>/liuning/governance

# pageHelper配置
pagehelper:
  helper-dialect: oracle
  #  分页合理化，当页数小于0时，会查询第一页，当页数大于总页数时，会查询最后一页（可能导致重复问题）
  reasonable: true
  support-methods-arguments: true
  params: count=countSql

# 执行二次确认和异常治理的脚本存放位置
governance:
  confirm: /ebosshome/eboss/app_nas/mt_tools/Abnormal_governance/alarm_confirmation/
  recover: /ebosshome/eboss/app_nas/mt_tools/Abnormal_governance/alarm_recover/

# kafka消费数据日志配置-com.newland.detb.troubleshooting.kafka.ConsumerKafka
#logging:
#  file:
#    name: /home/<USER>/exception_hidden/log/kafka-consumer.log
#  level:
#    com:
#      newland:
#        detb:
#          troubleshooting:
#            kafka:
#              ConsumerKafka: DEBUG

# 原子性操作信息存储地址
atomic:
#  passscript: /ebosshome/eboss/app_nas/mt_tools/Abnormal_governance/tools/paasscript
#  process-script: /ebosshome/eboss/app_nas/mt_tools/Abnormal_governance/tools/process_script
  path: /ebosshome/eboss/app_nas/mt_tools/Abnormal_governance/tools

# 数据持久化文件保存路径
backup:
  path: /home/<USER>/exception_hidden/backup

