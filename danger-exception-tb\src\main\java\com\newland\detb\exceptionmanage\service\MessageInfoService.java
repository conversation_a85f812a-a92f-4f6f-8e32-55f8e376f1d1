package com.newland.detb.exceptionmanage.service;

import com.newland.detb.exceptionmanage.entity.MessageInfo;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【HE_MESSAGE_INFO(信息同步表)】的数据库操作Service
* @createDate 2023-05-17 11:20:28
*/
public interface MessageInfoService {

    /**
     * 插入messageInfo
     * @param messageInfo
     * @return
     */
    int insert(MessageInfo messageInfo);

    /**
     * 根据id删除
     * @param id
     * @return
     */
    int deleteById(Long id);

    /**
     * 更新messageInfo
     * @param messageInfo
     * @return
     */
    int update(MessageInfo messageInfo);

    /**
     * 根据id查询
     * @param id
     * @return
     */
    MessageInfo selectById(Long id);

    /**
     * 查询全部
     * @return
     */
    List<MessageInfo> selectAll(Long dataId,String name, String phone,String email,String message, String governLevel,Integer isComplete) throws CloneNotSupportedException;

    /**
     * 保存内部的负责人信息的messageInfo
     * @param messageInfo
     * @return
     */
    int insertInner(MessageInfo messageInfo);

}
