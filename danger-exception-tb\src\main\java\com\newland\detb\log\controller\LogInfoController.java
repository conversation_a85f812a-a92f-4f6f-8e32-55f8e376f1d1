package com.newland.detb.log.controller;

import cn.com.nlsoft.cas.client.annotation.CasPermissionRequired;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.newland.detb.common.Result;
import com.newland.detb.log.entity.LogInfo;
import com.newland.detb.log.service.LogInfoService;
import org.hibernate.validator.constraints.Length;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @date 2023-04-06 006 11:23:21
 * @description
 */
@RestController
@RequestMapping(value = "/log")
@Validated
public class LogInfoController {

    @Resource
    private LogInfoService logInfoService;

    /**
     * 分页查询全部
     * @return
     */
    @GetMapping(value = "getAll")
    @CasPermissionRequired("aimt.log.query")
    public Result queryAllLogInfo(@RequestParam(value = "currentPage",defaultValue = "1") int currentPage,
                                  @RequestParam(value = "pageSize",defaultValue = "10") int pageSize,
                                  @RequestParam(value = "operation",required = false,defaultValue = "") @Length(min = 0,max = 20,message = "请求的操作信息超长") String operation) {
        PageHelper.startPage(currentPage,pageSize);
        List<LogInfo> logInfos = logInfoService.queryLogInfo(operation);
        PageInfo<LogInfo> pageInfo = new PageInfo<>(logInfos);
        Map<String, Object> map = new ConcurrentHashMap<>();
        map.put("list",pageInfo.getList());
        map.put("total",pageInfo.getTotal());
        return Result.success(map);
    }
}
