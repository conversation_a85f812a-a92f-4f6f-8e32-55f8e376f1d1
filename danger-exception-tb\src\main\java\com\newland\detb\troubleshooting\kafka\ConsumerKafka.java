package com.newland.detb.troubleshooting.kafka;


import com.fasterxml.jackson.databind.ObjectMapper;
import com.newland.detb.common.util.FunctionUtils;
import com.newland.detb.troubleshooting.component.MyWebSocketHandler;
import com.newland.detb.troubleshooting.component.WebSocketServer;
import com.newland.detb.troubleshooting.entity.BgocRecordBean;
import com.newland.detb.troubleshooting.entity.BgocPerfDetailBean;
import com.newland.detb.troubleshooting.entity.CgocDataDetailBean;
import com.newland.detb.troubleshooting.log.KafkaLogger;
import com.newland.detb.troubleshooting.service.CgocDataService;
import com.newland.detb.util.TimeFormatUtil;
import net.sf.ezmorph.object.DateMorpher;
import net.sf.json.JSONObject;
import com.newland.detb.troubleshooting.service.BgocDataService;
import lombok.extern.slf4j.Slf4j;
import net.sf.json.util.JSONUtils;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;


import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import javax.websocket.Session;
import java.sql.SQLException;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;


/**
 * <AUTHOR>
 * @date 2023-03-17 017 14:48:42
 * @description 测试的kafka消费者
 */
@Slf4j
@Component
public class ConsumerKafka {

    //自定义日志配置
    private static final Logger LOG = KafkaLogger.LOG;

    @Autowired
    private BgocDataService bgocDataService;
    @Autowired
    private CgocDataService cgocDataService;

    /**
     * 发送websocket
     */
    // @Resource
    private WebSocketServer webSocketServer;

    //自定义websocket处理器
    @Resource
    private MyWebSocketHandler myWebSocketHandler;

    /**
     * 最短耗时
     */
    private long miniTime = 0;

    /**
     * 最大耗时
     */
    private long maxTime = 0;

    /**
     * B-GOC当日采集次数
     */
    private int bgocCount = 0;

    /**
     * C-GOC当日采集次数
     */
    private int cgocCount = 0;

    //containerFactory的值要与配置中KafkaListenerContainerFactory的Bean名称相同
    @KafkaListener(topics = "BGOCyc1",groupId = "groupBGOCyc1",containerFactory = "kafkaOneListenerContainerFactory")
    //@KafkaListener(topics = "test-demo",groupId = "pumkin1",containerFactory = "kafkaOneListenerContainerFactory")
    public void listenOne(ConsumerRecord<?,?> record) throws Exception {
        //开始处理时间
        LocalDateTime startTime = LocalDateTime.now();
        // Session session = WebSocketServer.getSession("kafkaData");
        // log.info("kafka-1收到消息B-GOC侧消息,当前日期：{}", TimeFormatUtil.getStringTime(new Date()));
        LOG.info("[B-GOC]侧消息,当前日期：{}---{}", TimeFormatUtil.getStringTime(new Date()),record.value().toString().substring(0,300));
        //拼接前端显示的消息
        String message = "收到消息B-GOC侧消息,当前日期：" + TimeFormatUtil.getStringTime(new Date()) + "，消息：" + record.value().toString().substring(0,200);
        cn.hutool.json.JSONObject jsonObject = new cn.hutool.json.JSONObject();
        jsonObject.set("type","message");
        jsonObject.set("message",message);

        // log.info("=====================================================================");
        //消费BGOG数据
        JSONObject jsonRequest = JSONObject.fromObject(record.value());

        // String nowTime = FunctionUtils.formatDate("yyyyMMddHHmmss");
        // log.info("时间：{}",nowTime);
        try {
            //先插入记录表
            BgocRecordBean bgocRecordBean = new BgocRecordBean();
            bgocRecordBean.setResourceid(jsonRequest.getString("resourceId"));
            bgocRecordBean.setResourcename(jsonRequest.getString("resourceName"));
            bgocRecordBean.setSubtaskid(jsonRequest.getString("subTaskId"));
            bgocRecordBean.setSubtaskname(jsonRequest.getString("subTaskName"));
            bgocRecordBean.setType(jsonRequest.getString("type"));
            bgocRecordBean.setDn(jsonRequest.getString("dn"));
            bgocRecordBean.setCreateTime(new Date());
            int i = bgocDataService.insertHeBgocRecord(bgocRecordBean);
            if (i < 1) {
                LOG.info("插入HeBgocRecord表失败：{}",i);
                throw new SQLException("插入HeBgocRecord表失败");
            }


            ObjectMapper objectMapper = new ObjectMapper();
            BgocRecordBean bgocDetailContent = objectMapper.readValue(jsonRequest.toString(), BgocRecordBean.class);

            //插入性能明细表
            List<BgocPerfDetailBean> perfList = bgocDetailContent.getPerf();
            for (BgocPerfDetailBean perf:perfList) {
                // log.info("perf：{}",perf);
                // BgocPerfDetailBean bgocPerfDetailBean = new BgocPerfDetailBean();
                // FunctionUtils.copyAsm(perf,bgocPerfDetailBean);
                int j = bgocDataService.insertHeBgocPerfDetail(perf);
                //2023-0707 hh:mm:ss 消费了B-GOC侧一笔资源编码为：【resourceId】，【bgocPerfDetailBean.getKpiName】数据
                if (j < 1) {
                    LOG.info("插入he_bgoc_perf_detail表失败：{}",j);
                    throw new SQLException("插入he_bgoc_perf_detail表失败");
                }
            }
            //消息处理完成时间
            LocalDateTime endTime = LocalDateTime.now();
            //计算处理时间毫秒差
            long time = Duration.between(startTime, endTime).toMillis();
            //计算最小耗时和最大耗时
            miniTime = miniTime == 0 ? time : Math.min(miniTime, time);
            maxTime = Math.max(time,maxTime);
            cn.hutool.json.JSONObject jsonObject1 = new cn.hutool.json.JSONObject();
            jsonObject1.set("type","miniTime");
            jsonObject1.set("message",miniTime);
            cn.hutool.json.JSONObject jsonObject2 = new cn.hutool.json.JSONObject();
            jsonObject2.set("type","maxTime");
            jsonObject2.set("message",maxTime);

            cn.hutool.json.JSONObject jsonObject3 = new cn.hutool.json.JSONObject();
            bgocCount++;
            jsonObject3.set("type","bgocCount");
            jsonObject3.set("message",bgocCount);

            if (myWebSocketHandler.getOnlineSum() > 0) {
                // webSocketServer.sendMessage(jsonObject.toString(),session);
                // webSocketServer.sendMessage(jsonObject1.toString(),session);
                // webSocketServer.sendMessage(jsonObject2.toString(),session);
                // webSocketServer.sendMessage(jsonObject3.toString(),session);
                myWebSocketHandler.sendAllMessage(jsonObject.toString());
                myWebSocketHandler.sendAllMessage(jsonObject1.toString());
                myWebSocketHandler.sendAllMessage(jsonObject2.toString());
                myWebSocketHandler.sendAllMessage(jsonObject3.toString());
            }
        }catch (Exception e){
            LOG.info("B-goc数据处理时失败："+e.getMessage());
            throw new Exception("解析B-GOC数据资源失败");
        }


    }

    @KafkaListener(topics = "IaaS_Performance_zq",groupId = "IaaS_Performance_zq_Group",containerFactory = "kafkaTwoListenerContainerFactory")
    public void listenTwo(ConsumerRecord<?,?> record) throws Exception {
        //开始处理时间
        LocalDateTime startTime = LocalDateTime.now();
        // Session session = WebSocketServer.getSession("kafkaData");
        LOG.info("[C-GOC]侧消息,当前日期：{}---{}", TimeFormatUtil.getStringTime(new Date()),record.value().toString().substring(0,300));
        //拼接前端显示的消息
        String message = "收到C-GOC侧消息,当前日期：" + TimeFormatUtil.getStringTime(new Date()) + "，消息：" + record.value().toString().substring(0,200);
        cn.hutool.json.JSONObject jsonObject = new cn.hutool.json.JSONObject();
        jsonObject.set("type","message");
        jsonObject.set("message",message);

        // log.info("=====================================================================");

        try{
            //消费CGOC数据
            JSONObject jsonRequest = JSONObject.fromObject(record.value());
            JSONUtils.getMorpherRegistry().registerMorpher(new DateMorpher(new String[] {"yyyy-MM-dd HH:mm:ss"}) ); //时间格式转化
            CgocDataDetailBean cgocDataDetailConcent= (CgocDataDetailBean) JSONObject.toBean(jsonRequest, CgocDataDetailBean.class);

            //在保存之前先进行判断，此条数据今日是否已经保存过（判断条件：resourceID和今日日期）
            int ifExists = cgocDataService.ifExistsCgocData(cgocDataDetailConcent, TimeFormatUtil.getStringDate(new Date()));
            if (ifExists == 0) {
                int i = cgocDataService.insertHeCgocDataDetailMapper(cgocDataDetailConcent);
                if (i < 1) {
                    LOG.error("插入he_cgoc_data_detail表失败：{}",i);
                    throw new SQLException("插入he_cgoc_data_detail表失败");
                }
            }else {
                //更新操作，根据resourceId进行更新
                int i = cgocDataService.updateCgocDataDetailMapper(cgocDataDetailConcent, TimeFormatUtil.getStringDate(new Date()));
                if (i < 1) {
                    LOG.error("更新he_cgoc_data_detail表失败：{}",i);
                    throw new SQLException("更新he_cgoc_data_detail表失败");
                }
            }
            //处理完成时间
            LocalDateTime endTime = LocalDateTime.now();
            long time = Duration.between(startTime, endTime).toMillis();
            //计算最小耗时和最大耗时
            miniTime = miniTime == 0 ? time : Math.min(miniTime, time);
            maxTime = Math.max(time,maxTime);
            cn.hutool.json.JSONObject jsonObject1 = new cn.hutool.json.JSONObject();
            jsonObject1.set("type","miniTime");
            jsonObject1.set("message",miniTime);
            cn.hutool.json.JSONObject jsonObject2 = new cn.hutool.json.JSONObject();
            jsonObject2.set("type","maxTime");
            jsonObject2.set("message",maxTime);

            cn.hutool.json.JSONObject jsonObject3 = new cn.hutool.json.JSONObject();
            cgocCount++;
            jsonObject3.set("type","cgocCount");
            jsonObject3.set("message",cgocCount);

            if (myWebSocketHandler.getOnlineSum() > 0) {
                // webSocketServer.sendMessage(jsonObject.toString(),session);
                // webSocketServer.sendMessage(jsonObject1.toString(),session);
                // webSocketServer.sendMessage(jsonObject2.toString(),session);
                // webSocketServer.sendMessage(jsonObject3.toString(),session);
                myWebSocketHandler.sendAllMessage(jsonObject.toString());
                myWebSocketHandler.sendAllMessage(jsonObject1.toString());
                myWebSocketHandler.sendAllMessage(jsonObject2.toString());
                myWebSocketHandler.sendAllMessage(jsonObject3.toString());
            }
        }catch (Exception e){
            LOG.info("C-GOC数据处理时失败：{}",e.getMessage());
            throw new Exception("CGOC数据解析异常"+e.getMessage());
        }
    }

    /**
     * 每天的00:00执行
     * 不主动配置 TaskScheduler, SpringBoot 会默认使用一个单线程的scheduler来处理用 @Scheduled 注解实现的定时任务
     */
    @Async("taskExecutor")
    @Scheduled(cron = "0 0 0 * * ?")
    public void resetVariables() {
        this.bgocCount = 0;

        this.cgocCount = 0;

        this.miniTime = 0;

        this.maxTime = 0;
    }

}
