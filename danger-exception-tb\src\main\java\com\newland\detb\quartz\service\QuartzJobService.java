package com.newland.detb.quartz.service;

import com.newland.detb.quartz.entity.QuartzTask;
import org.apache.ibatis.annotations.Param;

import java.util.List;


public interface QuartzJobService {

    //查询全部定时任务
    List<QuartzTask> selectAllTasks(String jobName,String jobGroup,String jobStatus);

    //查询单条定时任务
    QuartzTask selectTaskById(int id);

    //插入定时任务
    int insertTask(QuartzTask quartzTask);

    //删除定时任务
    int deleteTaskById(int id);

    //暂停定时任务
    void pause(int id);

    //恢复定时任务
    void resume(int id);

    //更新定时任务的cron表达式
    int updateTaskCron(String cron, int id);

    void updateQuartzJobCron(String cron,int id);

}