package com.newland.detb.common;

import com.newland.detb.common.annotation.Phone;
import org.springframework.util.StringUtils;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.util.Collection;
import java.util.Iterator;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @date 2023-05-29 029 11:12:07
 * @description 自定义手机号校验规则
 */
public class PhoneValidator implements ConstraintValidator<Phone, String> {

    //正则表达式
    private static final Pattern mobile_patten = Pattern.compile("[1]([3-9])[0-9]{9}$");

    @Override
    public void initialize(Phone constraintAnnotation) {
        ConstraintValidator.super.initialize(constraintAnnotation);
    }

    @Override
    public boolean isValid(String s, ConstraintValidatorContext constraintValidatorContext) {
        System.out.println("================[待验证的手机号]===============" + s);
        return isMobile(s);
    }

    /**
     * 手机号验证
     * @param mobile
     * @return
     */
    public static boolean isMobile(String mobile) {
        if (StringUtils.isEmpty(mobile)) {
            return false;
        }
        //匹配原则
        Matcher matcher = mobile_patten.matcher(mobile);
        return matcher.matches();
    }
}
