package com.newland.detb.exceptionmanage.service;

import com.newland.detb.exceptionmanage.entity.WorkOrderHistory;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【HE_WORK_ORDER_HISTORY(治理历史工单信息表)】的数据库操作Service
* @createDate 2023-06-06 10:43:31
*/
public interface WorkOrderHistoryService {

    /**
     * 保存治理工单表信息
     * @param workOrderHistory
     * @return
     */
    int insert(WorkOrderHistory workOrderHistory);

    /**
     * 迁移历史数据
     * @param historyList
     * @return
     */
    int migrateData(List<WorkOrderHistory> historyList);

}
