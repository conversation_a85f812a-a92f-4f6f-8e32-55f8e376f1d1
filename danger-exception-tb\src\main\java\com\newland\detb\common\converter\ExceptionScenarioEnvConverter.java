package com.newland.detb.common.converter;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.CellData;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.property.ExcelContentProperty;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023-04-19 019 11:30:14
 * @description 异常场景库环境env格式转换
 */
public class ExceptionScenarioEnvConverter implements Converter<Integer> {

    //0表示云环境，1表示非云环境
    private static final String cloud = "云环境";
    private static final String nonCloud = "非云环境";

    private static final Integer cloudNum = 0;
    private static final Integer nonCloudNum = 1;

    @Override
    public Class supportJavaTypeKey() {
        return Integer.class;
    }

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        return CellDataTypeEnum.STRING;
    }

    /**
     * 导入
     * @param cellData
     * @param excelContentProperty
     * @param globalConfiguration
     * @return
     * @throws Exception
     */
    @Override
    public Integer convertToJavaData(CellData cellData, ExcelContentProperty excelContentProperty, GlobalConfiguration globalConfiguration) throws Exception {
        String value = cellData.getStringValue();
        if(Objects.equals(value,cloud)) {
            return cloudNum;
        }else if(Objects.equals(value,nonCloud)) {
            return nonCloudNum;
        }else {
            return null;
        }
    }

    /**
     * 导出
     * @param s
     * @param excelContentProperty
     * @param globalConfiguration
     * @return
     * @throws Exception
     */
    @Override
    public CellData convertToExcelData(Integer s, ExcelContentProperty excelContentProperty, GlobalConfiguration globalConfiguration) throws Exception {
        if (Objects.equals(s,cloudNum)) {
            return new CellData<>(cloud);
        }else if (Objects.equals(s,nonCloudNum)) {
            return new CellData<>(nonCloud);
        }else {
            return new CellData<>("");
        }
    }
}
