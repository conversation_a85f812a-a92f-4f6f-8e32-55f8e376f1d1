<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.newland.detb.exceptionmanage.mapper.DictInfoMapper">

    <resultMap id="BaseResultMap" type="com.newland.detb.exceptionmanage.entity.DictInfo">
            <result property="name" column="NAME" jdbcType="VARCHAR"/>
            <result property="value" column="VALUE" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        NAME,VALUE
    </sql>
    <insert id="insert">
        insert into he_dict_info (name,value) values (#{name,jdbcType=VARCHAR},#{value,jdbcType=VARCHAR})
    </insert>
    <update id="update">
        update he_dict_info set value = #{value,jdbcType=VARCHAR} where name = #{name,jdbcType=VARCHAR}
    </update>
    <delete id="deleteDictInfoByName">
        delete from he_dict_info where name = #{name,jdbcType=VARCHAR}
    </delete>
    <select id="selectDictInfo" resultType="com.newland.detb.exceptionmanage.entity.DictInfo">
        select name,value,create_time from he_dict_info where name = #{name,jdbcType=VARCHAR}
    </select>
    <select id="selectAllDictInfo" resultType="com.newland.detb.exceptionmanage.entity.DictInfo">
        select name,value,create_time from he_dict_info
        <where>
            <if test="name != null and name != ''">
                name like concat('%', #{name}, '%')
            </if>
            <if test="value != null and value != ''">
                and value like concat('%', #{value}, '%')
            </if>
        </where>
    </select>
</mapper>
