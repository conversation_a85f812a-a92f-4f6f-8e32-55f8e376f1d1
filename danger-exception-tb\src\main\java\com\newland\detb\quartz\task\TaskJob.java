package com.newland.detb.quartz.task;

import com.newland.detb.quartz.mapper.QuartzJobMapper;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.quartz.JobDetail;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.quartz.PersistJobDataAfterExecution;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.quartz.QuartzJobBean;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2023-03-17 017 10:34:09
 * @description 具体的定时任务逻辑
 */
@Slf4j
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@Component
@PersistJobDataAfterExecution
public class TaskJob extends QuartzJobBean {

    @Resource
    private QuartzJobMapper quartzJobMapper;



    @Override
    protected void executeInternal(JobExecutionContext context) throws JobExecutionException {
        log.info("-------------------------------------------任务执行中");
        JobDetail jobDetail = context.getJobDetail();
        log.info("任务名称：{}，组名：{}，要执行固定的任务：{}",jobDetail.getKey().getName(),jobDetail.getKey().getGroup(),jobDetail.getDescription());
    }
}
