package com.newland.detb.exceptionmanage.async;

import com.newland.detb.common.entity.HeExceptionDataBean;
import com.newland.detb.exceptionmanage.entity.*;
import com.newland.detb.exceptionmanage.mapper.BusinessExceptionOrderMapper;
import com.newland.detb.exceptionmanage.mapper.ExceptionProgressMapper;
import com.newland.detb.exceptionmanage.mapper.ExceptionScenarioMapper;
import com.newland.detb.exceptionmanage.service.BusinessTypeService;
import com.newland.detb.exceptionmanage.service.MessageConfigService;
import com.newland.detb.exceptionmanage.service.MessageInfoService;
import com.newland.detb.exceptionmanage.util.SnowFlakeGeneratorId;
import com.newland.detb.gostage.async.AsyncSecondaryConfirmStageTask;
import com.newland.detb.gostage.entity.ExceptionScenarioStage;
import com.newland.detb.gostage.mapper.ExceptionScenarioStageMapper;
import com.newland.detb.quartz.mapper.HeExceptionDataMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CountDownLatch;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023-04-24 024 11:26:08
 * @description 异步任务：异常场景匹配流程
 */
// @Lazy
@EqualsAndHashCode(callSuper = true)
@Slf4j
@Component
@Data
public class AsyncSceneMatchingTask extends BaseAsyncTask{

    @Resource
    private ExceptionScenarioMapper exceptionScenarioMapper;

    @Resource
    private ExceptionScenarioStageMapper exceptionScenarioStageMapper;

    @Lazy
    @Resource
    private AsyncSecondaryConfirmTask asyncSecondaryConfirmTask;

    @Lazy
    @Resource
    private AsyncSecondaryConfirmStageTask asyncSecondaryConfirmStageTask;

    @Lazy
    @Resource
    private AsyncSendMessage asyncSendMessage;

    //雪花生成id
    @Resource
    private SnowFlakeGeneratorId snowFlakeGeneratorId;

    @Resource
    private MessageInfoService messageInfoService;

    @Resource
    private MessageConfigService messageConfigService;

    @Resource
    private BusinessTypeService businessTypeService;


    @Lazy
    @Resource
    private AsyncBusinessExceptionTask asyncBusinessExceptionTask;

    @Value("${business.name}")
    private String businessName;

    /**
     * 批量进行异常场景匹配操作
     * @param heExceptionDataBeans 状态status为0的工单表数据，需要进行异常场景匹配的数据
     */
    @Async("taskExecutor")
    @Override
    public void executeAsync(List<HeExceptionDataBean> heExceptionDataBeans) {
        asyncPrintLogInfo("开始异常场景匹配");
        for (HeExceptionDataBean bean : heExceptionDataBeans) {
            //判断是业务异常还是系统异常(进行改造)，先判断是否是业务异常，再判断是否为系统异常
            if (Objects.equals(bean.getAlarmType().trim(),"BUSINESS")) {
                boolean checkResult = false;
                BusinessType businessType = null;
                //业务异常
                try {
                    businessType = checkExceptionType(bean.getAlarmType(), bean.getProcessName(), bean.getChildProcessName());
                    checkResult = businessType != null;
                } catch (Exception e) {
                    e.printStackTrace();
                    asyncPrintErrorLogInfo(1,2,"业务异常单-是否业务异常查询时程序异常：" + e.getMessage());
                }
                if (checkResult) {
                    //进行业务异常处理，调用存储过程开始处理
                    asyncBusinessExceptionTask.executeAsync(bean,businessType);
                }else {
                    //没匹配打对应的异常场景（修改进度表信息，通知去手动处理），修改工单表的status为7，代表未匹配到对应的场景，需要手动处理
                    updateHeExceptionDataWithRemark(bean,7,"工单表id：" + bean.getDataId() + "未匹配到异常场景定义，需手动处理");
                    updateProgress(bean,5,1,"fail","未匹配到异常场景定义，需手动处理");
                    updateProgress(bean,1,2,"fail","未匹配到异常场景定义，需手动处理");
                    //更新工单表数据
                    updateWorkOrderWhenFailCase(bean.getDataId(), "未匹配到异常场景定义，需手动处理");
                    //拼接内部短信，保存messageInfo
                    // spliceInnerMessageInfo(bean, "异常场景匹配数为零，需手动处理!",snowflakeId);
                    asyncSendMessage.spliceInnerMessageInfo(bean,"未匹配到异常场景定义，需手动处理!","业务异常单");
                }
            } else if (Objects.equals(bean.getAlarmType().trim(),businessName)) {
                //获取唯一id
                String snowflakeId = String.valueOf(snowFlakeGeneratorId.snowflakeId());
                //给workOrder表设置traceId，在授权的时候进行获取traceId执行后续流程
                getWorkOrderService().updateTraceIdByDataId(snowflakeId, bean.getDataId());
                asyncPrintLogInfo(snowflakeId, 1, 2, "正在处理的异常场景匹配操作的工单id：" + bean.getDataId());
                updateHeExceptionDataWithRemark(bean, 1, "异常匹配模块开始处理");
                updateProgress(bean, 1, 2, "doing", "正在执行异常场景匹配");
                //匹配到的异常场景（包含异常场景编码）
                // List<ExceptionScenario> scenarioList = exceptionScenarioMapper.findByThreeCondition(Integer.parseInt(bean.getEnvironment()), bean.getProcessName(), bean.getChildProcessName());
                List<ExceptionScenarioStage> scenarioList = exceptionScenarioStageMapper.getScenarioByType(bean.getProcessName(), bean.getChildProcessName());
                if (scenarioList.isEmpty()) {
                    //没匹配打对应的异常场景（修改进度表信息，通知去手动处理），修改工单表的status为7，代表未匹配到对应的场景，需要手动处理
                    updateHeExceptionDataWithRemark(bean, 7, "工单表id：" + bean.getDataId() + "未匹配到异常场景定义，需手动处理");
                    updateProgress(bean, 5, 1, "fail", "未匹配到异常场景定义，需手动处理");
                    updateProgress(bean, 1, 2, "fail", "未匹配到异常场景定义，需手动处理");
                    //更新工单表数据
                    updateWorkOrderWhenFailCase(bean.getDataId(), "未匹配到异常场景定义，需手动处理");
                    //拼接内部短信，保存messageInfo
                    // spliceInnerMessageInfo(bean, "异常场景匹配数为零，需手动处理!",snowflakeId);
                    asyncSendMessage.spliceInnerMessageInfo(bean, "未匹配到异常场景定义，需手动处理!", snowflakeId);
                } else {
                    asyncPrintLogInfo(snowflakeId, 1, 2, "异常场景匹配结果：" + scenarioList.get(0));
                    //异常创建匹配成功
                    updateProgress(bean, 1, 2, "success", "异常场景匹配成功,异常类型:" + scenarioList.get(0).getType() + ",应用名称:" + bean.getHomeModule());
                    asyncPrintLogInfo(snowflakeId, 1, 2, "异常场景匹配成功，对应异常场景匹配对象的场景编码：" + scenarioList.get(0).getSceneCode());
                    //匹配到异常场景编码之后，需要去执行异常二次确认操作
                    //CountDownLatch用于控制多个线程的协同问题
                    // CountDownLatch countDownLatch = new CountDownLatch(1);
                    // countDownLatch.await();
                    //去执行异常二次确认
                    //异步任务：执行二次确认操作（使用第一个匹配到的异常场景进行）
                    asyncSecondaryConfirmStageTask.executeAsync(snowflakeId, bean, scenarioList.get(0));
                }
            } else {
                //系统异常
                //获取唯一id
                String snowflakeId = String.valueOf(snowFlakeGeneratorId.snowflakeId());
                //给workOrder表设置traceId，在授权的时候进行获取traceId执行后续流程
                getWorkOrderService().updateTraceIdByDataId(snowflakeId, bean.getDataId());
                asyncPrintLogInfo(snowflakeId, 1, 2, "正在处理的异常场景匹配操作的工单id：" + bean.getDataId());
                updateHeExceptionDataWithRemark(bean, 1, "异常匹配模块开始处理");
                updateProgress(bean, 1, 2, "doing", "正在执行异常场景匹配");
                //匹配到的异常场景（包含异常场景编码）
                // List<ExceptionScenario> scenarioList = exceptionScenarioMapper.findByThreeCondition(Integer.parseInt(bean.getEnvironment()), bean.getProcessName(), bean.getChildProcessName());
                List<ExceptionScenario> scenarioList = exceptionScenarioMapper.getScenarioByType(bean.getAlarmType());
                if (scenarioList.size() == 0) {
                    //没匹配打对应的异常场景（修改进度表信息，通知去手动处理），修改工单表的status为7，代表未匹配到对应的场景，需要手动处理
                    updateHeExceptionDataWithRemark(bean, 7, "工单表id：" + bean.getDataId() + "未匹配到异常场景定义，需手动处理");
                    updateProgress(bean, 5, 1, "fail", "未匹配到异常场景定义，需手动处理");
                    updateProgress(bean, 1, 2, "fail", "未匹配到异常场景定义，需手动处理");
                    //更新工单表数据
                    updateWorkOrderWhenFailCase(bean.getDataId(), "未匹配到异常场景定义，需手动处理");
                    //拼接内部短信，保存messageInfo
                    // spliceInnerMessageInfo(bean, "异常场景匹配数为零，需手动处理!",snowflakeId);
                    asyncSendMessage.spliceInnerMessageInfo(bean, "未匹配到异常场景定义，需手动处理!", snowflakeId);
                } else {
                    asyncPrintLogInfo(snowflakeId, 1, 2, "异常场景匹配结果：" + scenarioList.get(0));
                    //异常创建匹配成功
                    updateProgress(bean, 1, 2, "success", "异常场景匹配成功,异常类型:" + scenarioList.get(0).getType() + ",应用名称:" + bean.getHomeModule());
                    asyncPrintLogInfo(snowflakeId, 1, 2, "异常场景匹配成功，对应异常场景匹配对象的场景编码：" + scenarioList.get(0).getSceneCode());
                    //匹配到异常场景编码之后，需要去执行异常二次确认操作
                    //CountDownLatch用于控制多个线程的协同问题
                    // CountDownLatch countDownLatch = new CountDownLatch(1);
                    // countDownLatch.await();
                    //去执行异常二次确认
                    //异步任务：执行二次确认操作（使用第一个匹配到的异常场景进行）
                    asyncSecondaryConfirmTask.executeAsync(snowflakeId, bean, scenarioList.get(0));
                }
            }
        }
    }

    /**
     * 用于判断是否属于业务异常
     * @param type
     * @return
     */
    private BusinessType checkExceptionType(String type,String parentName,String childName) {
        BusinessType businessType = businessTypeService.queryBusinessType(type,parentName,childName);
        asyncPrintLogInfo(1,2,"业务异常单-[判断是否业务异常结返回结果]：" + businessType);
        return businessType;
    }
}
