package com.newland.detb.exceptionmanage.entity;

import java.io.Serializable;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotNull;

/**
 * 字典表
 * @TableName HE_DICT_INFO
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class DictInfo implements Serializable {
    /**
     * 名称
     */
    @NotNull(message = "属性名称不能为空")
    @Length(min = 1, max = 10,message = "属性名称的长度为1到10之间")
    private String name;

    /**
     * 对应的值
     */
    @NotNull(message = "属性值不能为空")
    @Length(min = 1, max = 20,message = "属性值的长度为1到20之间")
    private String value;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date createTime;

    private static final long serialVersionUID = 1L;
}