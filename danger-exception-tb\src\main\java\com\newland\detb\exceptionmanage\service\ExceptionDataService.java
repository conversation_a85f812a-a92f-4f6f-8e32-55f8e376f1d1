package com.newland.detb.exceptionmanage.service;

import com.newland.detb.common.entity.HeExceptionDataBean;
import com.newland.detb.exceptionmanage.entity.ExceptionDataVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

//异常治理工单表service接口
public interface ExceptionDataService {
    //查询全部工单表数据，用于导出数据
    List<HeExceptionDataBean> queryExceptionDataBeans();

    /**
     * 查询告警工单表和进度表
     * @return
     */
    List<ExceptionDataVO> selectExceptionDataWithProgress(Long dataId, String env,String keyWords, String status, String alarmLevel);

    /**
     * 查询出四个月之前的全部数据，因为数据迁移到历史表中的规则是：3+1（前三个月+当前月的数据需要留在原表中，其他的数据迁移到历史表中）
     * @return
     */
    List<HeExceptionDataBean> selectExceptionDataForMoveToHistory();
}
