package com.newland.detb.exceptionmanage.mapper;

import com.newland.detb.exceptionmanage.entity.MessageConfig;
import com.newland.detb.exceptionmanage.entity.MessageInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【HE_MESSAGE_INFO(信息同步表)】的数据库操作Mapper
* @createDate 2023-05-17 11:20:28
* @Entity com.newland.detb.exceptionmanage.entity.MessageInfo
*/
@Mapper
public interface MessageInfoMapper {

    /**
     * 插入messageInfo
     * @param messageInfo
     * @return
     */
    int insert(MessageInfo messageInfo);

    /**
     * 根据id删除
     * @param id
     * @return
     */
    int deleteById(Long id);

    /**
     * 更新messageInfo
     * @param messageInfo
     * @return
     */
    int update(MessageInfo messageInfo);

    /**
     * 根据id查询
     * @param id
     * @return
     */
    MessageInfo selectById(Long id);

    /**
     * 查询全部
     * @return
     */
    List<MessageInfo> selectAll(@Param("dataId") Long dataId,@Param("name") String name, @Param("phone") String phone, @Param("email") String email, @Param("message") String message, @Param("governLevel") String governLevel, @Param("isComplete") Integer isComplete);

    /**
     * 保存内部的负责人信息的messageInfo
     * @param messageInfo
     * @return
     */
    int insertInner(MessageInfo messageInfo);


}




