package com.newland.detb.exceptionmanage.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023-04-25 025 14:07:09
 * @description 执行当前主机脚本
 */
@Slf4j
@Component
public class ExecuteScriptUtil {

    public boolean execute(String command) {
        try {
            Process process = Runtime.getRuntime().exec(command);
            //获取命令行执行的结果
            InputStream inputStream = process.getInputStream();
            BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(inputStream));
            String line;
            String result = "false";
            while ((line = bufferedReader.readLine()) != null) {
                log.info("执行脚本命令返回的结果：{}",line);
                result = line;
            }
            //获取命令执行结果状态
            int waitFor = process.waitFor();
            if (waitFor == 0) {
                // System.out.println("成功");
                log.info("执行脚本命令返回的结果：成功");
            }else {
                // System.out.println("失败");
                log.error("执行脚本命令返回的结果：失败");
            }
            return Objects.equals("true", result);
        } catch (IOException | InterruptedException e) {
            e.printStackTrace();
            log.error("执行脚本命令时出现异常：{}",e.getMessage());
        }
        return false;
    }
}
