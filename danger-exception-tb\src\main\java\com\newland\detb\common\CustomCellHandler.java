package com.newland.detb.common;

import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.CellData;
import com.alibaba.excel.metadata.Head;
import com.alibaba.excel.util.CollectionUtils;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.style.column.AbstractColumnWidthStyleStrategy;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import org.apache.poi.ss.usermodel.Cell;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023-04-17 017 16:22:32
 * @description 导出excel设置列宽度自动-自定义
 * 参照：LongestMatchColumnWidthStyleStrategy
 */
public class CustomCellHandler extends AbstractColumnWidthStyleStrategy {

    //列的最宽为：100
    private static final int MAX_COLUMN_WIDTH = 100;
    private Map<Integer, Map<Integer, Integer>> cache = new HashMap(8);

    /**
     * 这是一个用于设置 Excel 列宽的处理器，继承自 EasyExcel 的 AbstractColumnWidthStyleStrategy 类。作用是根据单元格内容自动设置列宽，保证内容不被隐藏。
     *
     * 具体实现是在 setColumnWidth() 方法中，首先判断是否需要设置宽度，如果是表头或没有数据则不需要设置。然后获取缓存中这个表格的最大列宽信息，如果没有则先创建一个。接着调用 dataLength() 方法计算单元格内容长度，限制最大宽度为 255，与之前的最大宽度进行比较，如果比之前的大则更新最大宽度缓存并设置列宽。
     *
     * dataLength() 方法用于根据不同类型的内容计算单元格的内容长度，返回一个整数。如果是表头，则计算字符串的字节数，如果是数据，则根据数据类型进行计算。如果类型为空则返回 -1。
     * @param writeSheetHolder
     * @param cellDataList
     * @param cell
     * @param head
     * @param relativeRowIndex
     * @param isHead
     */
    @Override
    protected void setColumnWidth(WriteSheetHolder writeSheetHolder, List<CellData> cellDataList, Cell cell, Head head, Integer relativeRowIndex, Boolean isHead) {
        boolean needSetWidth = isHead || !CollectionUtils.isEmpty(cellDataList);
        if (needSetWidth) {
            Map<Integer, Integer> maxColumnWidthMap = (Map)this.cache.get(writeSheetHolder.getSheetNo());
            if (maxColumnWidthMap == null) {
                maxColumnWidthMap = new HashMap(16);
                this.cache.put(writeSheetHolder.getSheetNo(), maxColumnWidthMap);
            }
            //计算宽度
            Integer columnWidth = this.dataLength(cellDataList, cell, isHead);
            if (columnWidth >= 0) {
                if (columnWidth > MAX_COLUMN_WIDTH) {
                    columnWidth = MAX_COLUMN_WIDTH;
                }

                Integer maxColumnWidth = (Integer)((Map)maxColumnWidthMap).get(cell.getColumnIndex());
                if (maxColumnWidth == null || columnWidth > maxColumnWidth) {
                    ((Map)maxColumnWidthMap).put(cell.getColumnIndex(), columnWidth);
                    writeSheetHolder.getSheet().setColumnWidth(cell.getColumnIndex(), columnWidth * 256);
                }

            }
        }
    }

    private Integer dataLength(List<CellData> cellDataList, Cell cell, Boolean isHead) {
        if (isHead) {
            return cell.getStringCellValue().getBytes().length;
        } else {
            CellData cellData = (CellData)cellDataList.get(0);
            CellDataTypeEnum type = cellData.getType();
            if (type == null) {
                return -1;
            } else {
                switch(type) {
                    case STRING:
                        return cellData.getStringValue().getBytes().length;
                    case BOOLEAN:
                        return cellData.getBooleanValue().toString().getBytes().length;
                    case NUMBER:
                        return cellData.getNumberValue().toString().getBytes().length;
                    default:
                        return -1;
                }
            }
        }
    }
}
