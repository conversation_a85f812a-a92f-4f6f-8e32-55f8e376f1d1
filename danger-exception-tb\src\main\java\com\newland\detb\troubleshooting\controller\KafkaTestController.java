package com.newland.detb.troubleshooting.controller;


import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.support.SendResult;
import org.springframework.util.concurrent.ListenableFuture;
import org.springframework.web.bind.annotation.*;


/**
 * <AUTHOR>
 * @date 2023-03-17 017 14:44:59
 * @description
 */
@RestController
@RequestMapping("/kafka")
public class KafkaTestController {

    @Autowired
    private KafkaTemplate<String,String> kafkaOneTemplate;

    @Autowired
    private KafkaTemplate<String,String> kafkaTwoTemplate;

    // @RequestMapping(value = "send1",method = RequestMethod.GET)
    public String send1( String msg) {
        ListenableFuture<SendResult<String, String>> send = kafkaOneTemplate.send("test-demo", msg);
        return "kafka-1消息生产成功";
    }

    // @RequestMapping(value = "sendBgoc",method = RequestMethod.POST)
    public String send11( @RequestBody String msg) {
        ListenableFuture<SendResult<String, String>> send = kafkaOneTemplate.send("BGOCyc1", msg);
        return "kafka-1消息生产成功";
    }

    // @RequestMapping(value = "send2",method = RequestMethod.GET)
    public String send2(String msg) {
        kafkaTwoTemplate.send("test-demo",msg);
        return "kafka-2消息生产成功";
    }

    // @RequestMapping(value = "sendCgoc",method = RequestMethod.POST)
    public String send22(@RequestBody String msg) {
        ListenableFuture<SendResult<String, String>> send = kafkaTwoTemplate.send("IaaS_Performance_zq", msg);
        return "kafka-2消息生产成功";
    }
}
