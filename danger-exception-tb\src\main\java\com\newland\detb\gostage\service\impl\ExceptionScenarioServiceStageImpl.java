package com.newland.detb.gostage.service.impl;

import com.newland.detb.common.ResEnum;
import com.newland.detb.common.Result;
import com.newland.detb.exception.ExceptionScenarioException;
import com.newland.detb.exceptionmanage.entity.ExceptionScenario;
import com.newland.detb.exceptionmanage.entity.GovernanceStrategy;
import com.newland.detb.exceptionmanage.entity.SceneInspection;
import com.newland.detb.exceptionmanage.mapper.ExceptionScenarioMapper;
import com.newland.detb.exceptionmanage.mapper.GovernanceStrategyMapper;
import com.newland.detb.exceptionmanage.mapper.SceneInspectionMapper;
import com.newland.detb.exceptionmanage.service.ExceptionScenarioService;
import com.newland.detb.gostage.entity.ExceptionScenarioStage;
import com.newland.detb.gostage.entity.GovernanceStrategyStage;
import com.newland.detb.gostage.mapper.ExceptionScenarioStageMapper;
import com.newland.detb.gostage.mapper.GovernanceStrategyStageMapper;
import com.newland.detb.gostage.service.BusinessHandleService;
import com.newland.detb.gostage.service.ExceptionScenarioStageService;
import com.newland.detb.gostage.util.JsonDataProcessor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【HE_EXCEPTION_SCENARIO(异常场景库信息表)】的数据库操作Service实现
* @createDate 2023-04-18 14:56:30
*/
@Slf4j
@Service
public class ExceptionScenarioServiceStageImpl implements ExceptionScenarioStageService {

    @Resource
    private ExceptionScenarioStageMapper exceptionScenarioStageMapper;

    @Resource
    private GovernanceStrategyStageMapper governanceStrategyStageMapper;

    @Resource
    private SceneInspectionMapper sceneInspectionMapper;

    @Resource
    private BusinessHandleService businessHandleService;

    /**
     * 保存或者更新异常场景库信息
     *
     * @param exceptionScenario
     * @return
     */
    @Transactional
    @Override
    public Result saveOrUpdate(ExceptionScenarioStage exceptionScenario) {
        //判断是保存还是更新操作
        if (exceptionScenario.getId() == null) {
            //异常编码自动生成，无需确认；在保存之前，去确认异常场景库中是否已经存在对应的异常场景编码
            // ExceptionScenario scenario = exceptionScenarioService.selectBySceneCode(exceptionScenario.getSceneCode());
            int res = exceptionScenarioStageMapper.insert(exceptionScenario);
            if (res < 0) {
                log.error("异常场景库保存失败！");
                throw new ExceptionScenarioException(ResEnum.scenario_error_code,"异常场景库录入失败");
            }else {
                //先校验json格式是否正常
                if (exceptionScenario.getRecoveryStep() != null && JsonDataProcessor.isValidJson(exceptionScenario.getRecoveryStep())) {
                    // 如果是有效的JSON，可以进行JSON处理
                    JsonDataProcessor.JsonProcessResult result = JsonDataProcessor.processJsonData(
                            exceptionScenario.getRecoveryStep(),
                            exceptionScenario.getId(),
                            exceptionScenario.getSceneCode().toString());
                    // 处理结果...
                    log.info("处理JSON数据成功，提取了{}个处理方案", result != null ? result.getBusinessHandles().size() : 0);
                    log.info("处理JSON数据成功，提取了{}个处理方案", result != null ? result.getBusinessHandles() : "");
                    //处理成功，将提取到的处理方案保存到数据库中
                    assert result != null;
                    businessHandleService.batchSave(result.getBusinessHandles());
                }else {
                    log.error("异常场景库信息录入失败，异常场景库的恢复步骤信息格式不正确");
                    throw new ExceptionScenarioException(ResEnum.scenario_error_code,"异常场景库信息录入失败，异常场景库的恢复步骤信息格式不正确");
                }

                //同步保存异常治理策略信息
//                GovernanceStrategyStage strategy = new GovernanceStrategyStage();
//                strategy.setSceneCode(exceptionScenario.getSceneCode().toString());
//                strategy.setStrategyCode(exceptionScenario.getSceneCode().toString());
//                strategy.setRemark(exceptionScenario.getStrategy());
//                int strategyRes = governanceStrategyStageMapper.insert(strategy);
//                if (strategyRes < 0) {
//                    log.error("在保存异常治理策略时发生异常，当前数据：{}",strategy);
//                    throw new ExceptionScenarioException(ResEnum.scenario_error_code,"同步保存异常治理策略时异常");
//                }
//                //继续同步保存在异常场景和单项巡检关系映射表中（如果需要触发单项巡检时，才需要在关联表中保存对应数据）0：需要，1：不需要
//                if (exceptionScenario.getIsInspection() == 0) {
//                    SceneInspection sceneInspection = new SceneInspection();
//                    sceneInspection.setSceneCode(exceptionScenario.getSceneCode());
//                    sceneInspection.setSceneName(exceptionScenario.getType());
//                    sceneInspection.setInspectionCode(exceptionScenario.getSceneCode());
//                    int saveResult = sceneInspectionMapper.insert(sceneInspection);
//                    if (saveResult < 1) {
//                        log.error("异常场景和单项巡检关联表保存失败");
//                    }
//                }
                return Result.success("异常场景库信息录入成功");
            }
        }else {
            //更新操作，先删除异常分析和处置方案，再进行更新操作
            //先判断恢复步骤是否为空
            if (exceptionScenario.getRecoveryStep() == null) {
                //直接删除
                businessHandleService.deleteByPId(exceptionScenario.getId());
            }else if (JsonDataProcessor.isValidJson(exceptionScenario.getRecoveryStep())) {
                //校验json格式是否正常
                // 如果是有效的JSON，可以进行JSON处理
                JsonDataProcessor.JsonProcessResult result = JsonDataProcessor.processJsonData(
                        exceptionScenario.getRecoveryStep(),
                        exceptionScenario.getId(),
                        exceptionScenario.getSceneCode().toString());
                // 处理结果...
                log.info("更新-处理JSON数据成功，提取了{}个处理方案", result != null ? result.getBusinessHandles().size() : 0);
                log.info("更新-处理JSON数据成功，提取了{}个处理方案", result != null ? result.getBusinessHandles() : "");
                //处理成功，将提取到的处理方案保存到数据库中
                assert result != null;
                //先删除
                businessHandleService.deleteByPId(exceptionScenario.getId());
                //再保存
                businessHandleService.batchSave(result.getBusinessHandles());
            }else {
                log.error("异常场景库信息录入失败，异常场景库的恢复步骤信息格式不正确");
                throw new ExceptionScenarioException(ResEnum.scenario_error_code,"异常场景库信息录入失败，异常场景库的恢复步骤信息格式不正确");
            }
            int res = exceptionScenarioStageMapper.update(exceptionScenario);
            if (res < 0) {
                log.error("异常场景库更新失败！");
                throw new ExceptionScenarioException(ResEnum.scenario_error_code,"异常场景库更新失败");
            }else {
                return Result.success("异常场景库信息更新成功");
            }
        }
    }

    /**
     * 保存数据
     *
     * @param exceptionScenario
     * @return
     */
    @Override
    public int insert(ExceptionScenarioStage exceptionScenario) {
        return exceptionScenarioStageMapper.insert(exceptionScenario);
    }

    /**
     * 根据id查询
     *
     * @param id
     * @return
     */
    @Override
    public ExceptionScenarioStage findById(Long id) {
        return exceptionScenarioStageMapper.findById(id);
    }

    /**
     * 倒序查询全部数据
     *
     * @return
     */
    @Override
    public List<ExceptionScenarioStage> findAll() {
        return exceptionScenarioStageMapper.findAll();
    }

    /**
     * 条件查询
     *
     * @param env
     * @param type
     * @return
     */
    @Override
    public List<ExceptionScenarioStage> findByCondition(String type, Integer env, String subClass, Integer isAuth,Integer isInspection) {
        return exceptionScenarioStageMapper.findByCondition(type, env, subClass,isAuth,isInspection);
    }

    /**
     * 更新
     *
     * @param exceptionScenario
     * @return
     */
    @Override
    public int update(ExceptionScenarioStage exceptionScenario) {
        //不更新异常编码
        return exceptionScenarioStageMapper.update(exceptionScenario);
    }

    /**
     * 根据id删除
     *
     * @param id
     * @return
     */
    @Transactional
    @Override
    public int delete(Long id) {
        //需要同步删除治理策略中的信息
//        ExceptionScenarioStage scenario = exceptionScenarioStageMapper.findById(id);
//        if (scenario != null) {
//            //如果需要触发单项巡检操作，则需要同步删除异常场景和单项巡检关联表中的数据
//            if (scenario.getIsInspection() == 0) {
//                int res = sceneInspectionMapper.deleteBySceneCode(scenario.getSceneCode());
//                if (res < 1) {
//                    log.error("删除异常场景和单项巡检关联表中的数据失败");
//                }
//            }
//            int res = governanceStrategyStageMapper.delBySceneCode(scenario.getSceneCode().toString());
//            if (res < 1) {
//                log.error("删除异常场景时，同步删除治理策略失败");
//            }
//        }
        //需要先删除异常分析和处置方案
        businessHandleService.deleteByPId(id);
        return exceptionScenarioStageMapper.delete(id);
    }

    /**
     * 根据异常场景编码查询异常场景库
     *
     * @param sceneCode
     * @return
     */
    @Override
    public ExceptionScenarioStage selectBySceneCode(int sceneCode) {
        return exceptionScenarioStageMapper.selectBySceneCode(sceneCode);
    }

    /**
     * 根据id查询异常场景库信息
     *
     * @param exportList
     * @return
     */
    @Override
    public List<ExceptionScenarioStage> findByIds(List<Long> exportList) {
        return exceptionScenarioStageMapper.findByIds(exportList);
    }

    /**
     * 获取最大的异常场景库编码号
     *
     * @return
     */
    @Override
    public Integer getMaxSceneCode() {
        return exceptionScenarioStageMapper.getMaxSceneCode();
    }

    /**
     * 根据id修改是否授权状态
     *
     * @param id
     * @param isAuth
     * @return
     */
    @Override
    public int updateIsAuthById(Long id, int isAuth) {
        return exceptionScenarioStageMapper.updateIsAuthById(id, isAuth);
    }

    /**
     * 根据id修改是否需要触发单项巡检
     *
     * @param id
     * @param isInspection
     * @return
     */
    @Override
    public int updateIsInspectionById(Long id, int isInspection) {
        return exceptionScenarioStageMapper.updateIsInspectionById(id, isInspection);
    }

    /**
     * 根据id修改是否已经覆盖
     *
     * @param id
     * @param isOverride
     * @return
     */
    @Override
    public int updateIsOverrideById(Long id, int isOverride) {
        return exceptionScenarioStageMapper.updateIsOverrideById(id, isOverride);
    }

    @Override
    public List<Long> findScenarioIdsByCondition(String type, Integer env, String subClass, Integer isAuth, Integer isInspection) {
        return exceptionScenarioStageMapper.findScenarioIdsByCondition(type, env, subClass, isAuth, isInspection);
    }

    @Override
    public List<ExceptionScenarioStage> findByIdList(List<Long> ids) {
        if (ids == null || ids.isEmpty()) {
            return new ArrayList<>();
        }
        return exceptionScenarioStageMapper.findByIdList(ids);
    }

    /**
     * 根据归属和类型查询
     *
     * @param attribution
     * @param type
     * @return
     */
    @Override
    public List<ExceptionScenarioStage> queryByAttributionAndType(String attribution, String type) {
        return exceptionScenarioStageMapper.queryByAttributionAndType(attribution, type);
    }

}
