package com.newland.detb.config;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.CommonClientConfigs;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.common.config.SaslConfigs;
import org.apache.kafka.common.serialization.StringDeserializer;
import org.apache.kafka.common.serialization.StringSerializer;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.annotation.EnableKafka;
import org.springframework.kafka.config.ConcurrentKafkaListenerContainerFactory;
import org.springframework.kafka.config.KafkaListenerContainerFactory;
import org.springframework.kafka.core.*;
import org.springframework.kafka.listener.ConcurrentMessageListenerContainer;
import org.springframework.kafka.listener.DeadLetterPublishingRecoverer;
import org.springframework.kafka.listener.ErrorHandler;
import org.springframework.kafka.listener.SeekToCurrentErrorHandler;
import org.springframework.kafka.support.converter.RecordMessageConverter;
import org.springframework.kafka.support.converter.StringJsonMessageConverter;
import org.springframework.util.backoff.FixedBackOff;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023-03-17 017 10:54:54
 * @description 配置第一个kafka
 */
@Data
@Slf4j
@EnableKafka
@Configuration
@ConfigurationProperties(prefix = "spring.kafka.one")
public class KafkaOneConfig {

    private String bootstrapServers;

    @Value("${spring.kafka.one.properties.sasl.jaas.config}")
    private String saslJaasConfig;

    @Bean
    public KafkaTemplate<String,String> kafkaOneTemplate() {
        return new KafkaTemplate<>(producerFactory());
    }

    //监听器容器工厂
    @Bean
    public KafkaListenerContainerFactory<ConcurrentMessageListenerContainer<Integer,String>> kafkaOneListenerContainerFactory() {
        ConcurrentKafkaListenerContainerFactory<Integer, String> factory = new ConcurrentKafkaListenerContainerFactory<>();
        factory.setConsumerFactory(consumerFactory());
        //不推荐使用这种方式指定消费的线程数，可以在注解KafkaListener中的concurrency指定，因为这个线程数必须要小于等于主题topic的分区数
        // factory.setConcurrency(3);
        factory.getContainerProperties().setPollTimeout(3000);
        //设置消费者重试
        factory.setErrorHandler(kafkaErrorHandlerOne(kafkaOneTemplate()));
        return factory;
    }

    //第一个生产者的bean
    private ProducerFactory<String,String> producerFactory() {
        return new DefaultKafkaProducerFactory<>(producerConfigs());
    }

    //第一个消费者工厂的bean
    public ConsumerFactory<Integer,String> consumerFactory() {
        return new DefaultKafkaConsumerFactory<>(consumerConfigs());
    }

    private Map<String,Object> producerConfigs() {
        Map<String, Object> props = new HashMap<>();
        props.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG,bootstrapServers);
        props.put(ProducerConfig.RETRIES_CONFIG,0);
        //注意，不要写成：1
        props.put(ProducerConfig.ACKS_CONFIG,"1");
        props.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, StringSerializer.class);
        props.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG,StringSerializer.class);
        return props;
    }

    private Map<String, Object> consumerConfigs() {
        Map<String, Object> props = new HashMap<>();
        props.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG,bootstrapServers);
        //设置offset的消费方式，从启动后消费最新的数据
        props.put(ConsumerConfig.AUTO_OFFSET_RESET_CONFIG,"latest");
        // props.put(ConsumerConfig.GROUP_ID_CONFIG,"");
        // props.put(ConsumerConfig.ENABLE_AUTO_COMMIT_CONFIG,"");
        props.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
        props.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG,StringDeserializer.class);
        props.put(CommonClientConfigs.SECURITY_PROTOCOL_CONFIG,"SASL_PLAINTEXT");
        props.put(SaslConfigs.SASL_MECHANISM,"PLAIN");
        props.put("sasl.jaas.config",saslJaasConfig);
        return props;
    }

    //seek操作实现消费者操作偏移offset复位，使丢弃记录在下一次轮询再取出
    public ErrorHandler kafkaErrorHandlerOne(KafkaTemplate<?,?> template) {
        log.error("kafka-1消费异常，开始进行重试-----------------------------------------");
        //设置死信队列
        DeadLetterPublishingRecoverer recoverer = new DeadLetterPublishingRecoverer(template);
        //创建FixedBackOff对象(设置重试间隔为：10秒，重试次数为：3次)
        FixedBackOff backOff = new FixedBackOff(10 * 1000L, 3L);
        //创建SeekToCurrentErrorHandler对象
        return new SeekToCurrentErrorHandler(recoverer,backOff);
    }

    //反序列化异常
    @Bean
    public RecordMessageConverter converterOne() {
        return new StringJsonMessageConverter();
    }
}
