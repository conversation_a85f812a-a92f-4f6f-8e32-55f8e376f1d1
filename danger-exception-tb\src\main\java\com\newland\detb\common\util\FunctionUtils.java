package com.newland.detb.common.util;

import com.esotericsoftware.reflectasm.MethodAccess;

import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * @Author: xumengfan
 * @CreateTime: 2023-04-18  16:52
 * @Description:
 */
public class FunctionUtils {
    /**
     * 复制源对象和目标对象的属性值
     *
     */
    public static void copyAsm(Object source, Object target) {
        Class<? extends Object> sourceClass = source.getClass();// 得到对象的Class
        Class<? extends Object> targetClass = target.getClass();// 得到对象的Class

        MethodAccess sourceMethodAccess = MethodAccess.get(sourceClass);
        MethodAccess targetMethodAccess = MethodAccess.get(targetClass);
        String[] sourceMethods = sourceMethodAccess.getMethodNames();
        String[] targetMethods = targetMethodAccess.getMethodNames();

        for (int index = 0; index < sourceMethods.length; index++) {
            if ("get".equalsIgnoreCase(sourceMethods[index].substring(0, 3))) {
                Object value = sourceMethodAccess.invoke(source, sourceMethods[index]);

                if (value == null || (value instanceof String && ((String) value).trim().isEmpty())) {
                    continue;
                }

                String name = sourceMethods[index].substring(3).replaceAll("_", "");// 属性名
                String targetSetMethodName = findMethodByName(targetMethods, "set" + name);
                if (targetSetMethodName != null) {
                    targetMethodAccess.invoke(target, targetSetMethodName, value);
                }
            }
        }
    }

    private static String findMethodByName(String[] methodNames, String name) {
        for (int index = 0; index < methodNames.length; index++) {
            String result = methodNames[index];
            if (result.replaceAll("_", "").toLowerCase().equalsIgnoreCase(name)) {
                return result;
            }
        }
        return null;
    }

    /**
     * formatDate:(描述这个方法的作用). <br/>
     * <AUTHOR>
     * @param pattern
     * @return
     */
    public static String formatDate(String pattern) {
        Date now = new Date();
        SimpleDateFormat dateFormat = new SimpleDateFormat(pattern);//可以方便地修改日期格式

        return dateFormat.format(now);
    }
}
