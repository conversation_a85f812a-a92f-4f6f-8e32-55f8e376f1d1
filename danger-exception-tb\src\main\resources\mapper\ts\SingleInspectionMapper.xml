<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.newland.detb.troubleshooting.mapper.SingleInspectionMapper">
    <select id="querySingleInspection"
            resultType="com.newland.detb.troubleshooting.entity.SingleInspectionBean">
        select a.kpi_template_id kpiTemplateId, a.kpi_category templateName
        from he_bgoc_collection_index a
        where a.kpi_category is not null
        group by a.kpi_template_id, a.kpi_category
    </select>
    <select id="selectPage" resultType="com.newland.detb.troubleshooting.entity.SingleInspectionBean">
        SELECT
        a.ip_address,
        b.template_name,
        a.kpiid,
        a.kpivalue,
        b.kpi_name,
        b.kpi_template_id,
        a.resourceid,
        c.create_time
        FROM
        he_bgoc_perf_detail a,
        he_bgoc_collection_index b,
        he_bgoc_record c
        <where>
            a.resourceid = c.resourceid
            <if test="kpi_template_id != null and kpi_template_id != ''">
                AND b.kpi_template_id LIKE CONCAT('%', #{kpi_template_id}, '%')
            </if>
            AND b.kpi_id = a.kpiid
            AND b.is_show = 1
        </where>
        ORDER BY
        DATE_FORMAT(c.create_time, '%Y%m%d%H%i'),
        b.kpi_name DESC
    </select>


</mapper>