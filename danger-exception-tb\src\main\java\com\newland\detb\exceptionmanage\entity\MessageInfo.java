package com.newland.detb.exceptionmanage.entity;

import java.io.Serializable;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.newland.detb.common.annotation.Phone;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.Email;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * 信息同步表
 * @TableName HE_MESSAGE_INFO
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class MessageInfo implements Serializable {
    /**
     * id
     */
    private Long id;

    /**
     * data_id
     */
    @NotNull
    @Max(value = 9223372036854774807L,message = "告警工单ID超出最大值")
    @Min(value = 0,message = "告警工单ID值错误")
    private Long dataId;

    /**
     * 姓名
     */
    @NotNull
    @Length(min = 0,max = 6,message = "请求的姓名参数超长")
    private String name;

    /**
     * 手机号
     */
    @NotNull
    @Phone
    private String phone;

    /**
     * 邮箱地址
     */
    @NotNull
    @Email
    @Length(min = 0,max = 30,message = "邮箱地址过长")
    private String email;

    /**
     * 告警等级
     */
    @NotNull
    @Length(min = 0,max = 10,message = "告警等级过长")
    private String governLevel;

    /**
     * 是否发送完成，默认0，未发送，1：发送完成
     */
    private Integer isComplete;

    /**
     * 是否删除，默认0：未删除
     */
    private Integer deleted;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date createTime;

    /**
     * 同步的消息
     */
    @NotNull
    private String message;

    /**
     * 是否内部发送，默认：0代表全部发送，1：代表只发送内部负责人
     */
    private Integer isInner;

    private static final long serialVersionUID = 1L;
}