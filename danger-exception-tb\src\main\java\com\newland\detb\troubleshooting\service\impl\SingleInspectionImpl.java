package com.newland.detb.troubleshooting.service.impl;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.newland.detb.troubleshooting.entity.SingleInspectionBean;
import com.newland.detb.troubleshooting.mapper.SingleInspectionMapper;
import com.newland.detb.troubleshooting.service.SingleInspectionService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

/**
 * @Author: xumengfan
 * @CreateTime: 2023-04-28  15:08
 * @Description: TODO
 */
@Service
@Slf4j
public class SingleInspectionImpl implements SingleInspectionService {

    @Resource
    private SingleInspectionMapper singleInspectionMapper;
    @Override
    public List<SingleInspectionBean> querySingleInspection() {
        return singleInspectionMapper.querySingleInspection();
    }

    @Override
    public List<SingleInspectionBean> findPage(Integer pageNum,Integer pageSize,String kpi_template_id) {

        List<SingleInspectionBean> sysMenus = singleInspectionMapper.selectPage(kpi_template_id);

        return sysMenus;
    }


}
