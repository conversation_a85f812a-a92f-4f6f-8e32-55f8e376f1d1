<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.newland.detb.exceptionmanage.mapper.DynamicDataSourceTestMapper">

    <select id="getWorkOrderList" resultType="com.newland.detb.exceptionmanage.entity.WorkOrder">
        select id,data_id,scene_code,env,host_name,app_name,is_auth,governance_res,recovery_command from he_work_order
    </select>
</mapper>