package com.newland.detb.exceptionmanage.async;

import com.newland.detb.common.entity.HeExceptionDataBean;
import com.newland.detb.exception.ExceptionDataException;
import com.newland.detb.exceptionmanage.log.GovernanceLogger;
import com.newland.detb.exceptionmanage.mapper.ExceptionProgressMapper;
import com.newland.detb.exceptionmanage.service.ExceptionProgressService;
import com.newland.detb.quartz.service.HeExceptionDataService;
import com.newland.detb.util.JschUtil;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-06-14 014 9:28:21
 * @description 接收状态status是0的所有告警数据，然后去处理
 */
@Slf4j
@Component
public class AsyncFirstStepTask extends BaseAsyncTask{

    public static Boolean flagBit = true;

    //自定义治理日志配置
    private static final Logger LOG = GovernanceLogger.LOG;

    //异常治理工单表操作
    @Resource
    private HeExceptionDataService heExceptionDataService;

    //异常治理标志位判断
    @Resource
    private JschUtil jschUtil;

    @Resource
    private ExceptionProgressMapper exceptionProgressMapper;

    @Resource
    private ExceptionProgressService exceptionProgressService;

    /**
     * 接收定时任务扫描到的status为0的所有告警信息
     * @param heExceptionDataBeans
     */
    @Async("taskExecutor")
    @Override
    public void executeAsync(List<HeExceptionDataBean> heExceptionDataBeans) {
        LOG.info("==================[开始进行异常治理前期初始化操作]===================");
        //1、先循环初始化进度表(初始化24条数据：一个异常数据需要在进度表里面初始化24条数据)
        for (HeExceptionDataBean bean : heExceptionDataBeans) {
            int res = exceptionProgressService.insertInitData(bean.getDataId());
            //判断进度表是否初始化成功（每条数据需要初始化24条数据）
            if (res != 24) {
                LOG.error("异常治理初始化操作------>初始化进度表操作失败，data_id：{}",bean.getDataId());
                heExceptionDataService.updateExceptionDataStatusById(bean,10,"进度表初始化异常，治理结束，请联系管理员查看");
                return;
            }else {
                // log.info("------>初始化进度表操作成功，dataId为：{}",bean.getDataId());
                LOG.info("异常治理初始化操作------>进度表初始化成功，data_id为：{}",bean.getDataId());
                exceptionProgressMapper.updateChildProgressByDataId(bean.getDataId(), 0,1,"success","告警接收完成",new Date());
                exceptionProgressMapper.updateChildProgressByDataId(bean.getDataId(), 0,2,"success","告警入库完成",new Date());
            }
        }
        //2、查询到异常治理工单表中存储等待处理的工单信息后，需要判断异常治理标志位状态
        Boolean flag = jschUtil.getFileStatus();
        flag = flagBit && flag;
        //在此步就需要生成治理工单表，WorkOrder对象信息
        if (flag) {
            //当异常治理标志位为true时，表示可以去处理异常治理工单，接着去和异常场景库信息进行匹配
            //同时修改异常治理工单表数据的status为1，表示开始异常匹配阶段
            LOG.info("异常治理标志位判断成功，正常处理异常治理流程");
            //修改status为1，表示开始进行异常场景匹配操作(还创建了对应的workOrder工单)
            int updateCount = heExceptionDataService.updateExceptionDataStatusById(heExceptionDataBeans, 1, true);
            if (updateCount == heExceptionDataBeans.size()) {
                LOG.info("批量修改工单表信息为异常场景匹配(status=1)状态成功，共修改条数：{}",updateCount);
            }else {
                LOG.error("批量修改工单表信息为异常场景匹配(status=1)状态部分失败，共修改条数：{}，失败条数：{}",updateCount,heExceptionDataBeans.size() - updateCount);
            }
            //3、去和异常场景库信息进行匹配，修改status为1后，继续进行异常场景匹配操作(异步任务进行匹配)
            String result = heExceptionDataService.sceneMatching(heExceptionDataBeans);
            // log.info("ScanExceptionWorkOrderJob------>[1-2]------>异常场景匹配执行结果：{}",result);
        }else {
            //标志位为false，说明不需要治理，因此需要将所有工单数据的status修改为5，表示治理结束
            LOG.info("异常治理标志位判断成功，结束异常治理流程");
            int updateCount = heExceptionDataService.updateExceptionDataStatusById(heExceptionDataBeans, 5, false);
            if (updateCount == heExceptionDataBeans.size()) {
                LOG.info("批量修改工单表信息为治理结束状态成功，共修改条数：{}",updateCount);
            }
        }
    }
}
