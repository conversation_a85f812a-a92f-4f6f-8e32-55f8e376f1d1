package com.newland.detb.gostage.async;

import com.newland.detb.common.entity.HeExceptionDataBean;
import com.newland.detb.exceptionmanage.async.AsyncSummaryMessageTask;
import com.newland.detb.exceptionmanage.async.BaseAsyncTask;
import com.newland.detb.exceptionmanage.entity.ExceptionScenario;
import com.newland.detb.exceptionmanage.entity.WorkOrder;
import com.newland.detb.gostage.entity.ExceptionScenarioStage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2023-05-11 011 15:13:36
 * @description
 */
// @Lazy
@Slf4j
@Component
public class AsyncSingleInspectionStageTask extends BaseAsyncTask {

    @Lazy
    @Resource
    private AsyncSummaryMessageStageTask asyncSummaryMessageStageTask;


    /**
     * 判断是否需要触发单项巡检操作
     * @param heExceptionDataBean 异常数据工单
     * @param exceptionScenario 异常场景库
     * @param workOrder 治理工单
     */
    @Async("taskExecutor")
    @Override
    public void executeAsync(String traceId, HeExceptionDataBean heExceptionDataBean, ExceptionScenarioStage exceptionScenario, WorkOrder workOrder, int randomNumber)  {
        asyncPrintLogInfo(traceId,"是否触发单项巡检");
        Integer isInspection = exceptionScenario.getIsInspection();
        updateHeExceptionDataWithRemark(heExceptionDataBean,3,"正在处理是否触发单项巡检");
        updateProgress(heExceptionDataBean,3,4,"doing","正在判断是否触发单项巡检");
        asyncPrintLogInfo(traceId,3,4,"不需要执行单项巡检操作");
        // workOrderService.updateIsInspection(1, workOrder.getId());
        getWorkOrderService().updateIsInspection(1, workOrder.getId());
        updateHeExceptionDataWithRemark(heExceptionDataBean,3,"单项巡检判断结束");
        updateProgress(heExceptionDataBean,3,4,"success","单项巡检判断结束，不需要触发");
        //准备汇总治理信息，进行消息同步
        asyncSummaryMessageStageTask.executeAsync(traceId,heExceptionDataBean,exceptionScenario,workOrder,randomNumber);
    }
}
