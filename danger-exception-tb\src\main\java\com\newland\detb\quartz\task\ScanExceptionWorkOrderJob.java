package com.newland.detb.quartz.task;

import com.newland.detb.common.entity.HeExceptionDataBean;
import com.newland.detb.exceptionmanage.async.AsyncFirstStepTask;
import com.newland.detb.exceptionmanage.mapper.ExceptionProgressMapper;
import com.newland.detb.exceptionmanage.service.ExceptionProgressService;
import com.newland.detb.quartz.service.HeExceptionDataService;
import com.newland.detb.util.JschUtil;
import com.newland.detb.util.TimeFormatUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.weaver.ast.Not;
import org.quartz.JobDetail;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.quartz.PersistJobDataAfterExecution;
import org.springframework.context.annotation.Lazy;
import org.springframework.lang.NonNullApi;
import org.springframework.scheduling.quartz.QuartzJobBean;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023-04-12 012 10:36:12
 * @description 定期扫描异常治理工单表，查询所有需要治理的异常工单信息
 */
@Slf4j
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@Component
@PersistJobDataAfterExecution
public class ScanExceptionWorkOrderJob extends QuartzJobBean {

    //异常治理工单表操作
    @Resource
    private HeExceptionDataService heExceptionDataService;

    //异常治理标志位判断
    @Resource
    private JschUtil jschUtil;

    @Resource
    private ExceptionProgressMapper exceptionProgressMapper;

    @Resource
    private ExceptionProgressService exceptionProgressService;

    @Lazy
    @Resource
    private AsyncFirstStepTask asyncFirstStepTask;


    @Override
    protected void executeInternal(JobExecutionContext jobExecutionContext) {
        log.info("----------------------[定时扫描异常治理工单任务执行中]----------------------");
        JobDetail jobDetail = jobExecutionContext.getJobDetail();
        log.info("定时任务名称：{}，定时任务组名：{}，要执行固定的任务描述：{}，执行的当前时间：{}",jobDetail.getKey().getName(),jobDetail.getKey().getGroup(),jobDetail.getDescription(), TimeFormatUtil.getStringTime(new Date()));
        //定期去扫描异常治理工单表中的信息，查询全部的异常治理工单信息(status为0的)，heExceptionDataBeans中是需要去进行治理的数据
        List<HeExceptionDataBean> heExceptionDataBeans = heExceptionDataService.selectExceptionDataStatus0();
        if (heExceptionDataBeans == null || heExceptionDataBeans.size() == 0) {
            log.info("定时扫描异常治理工单任务执行结束------>异常治理工单表中不存在初始状态status=0的工单");
        }else {
            //执行异步方法处理异常数据
            asyncFirstStepTask.executeAsync(heExceptionDataBeans);
        }
    }
}
