package com.newland.detb.exceptionmanage.controller;

import cn.com.nlsoft.cas.client.annotation.CasPermissionRequired;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.newland.detb.common.Result;
import com.newland.detb.exceptionmanage.entity.ScriptInfo;
import com.newland.detb.exceptionmanage.service.ScriptInfoService;
import com.newland.detb.log.annotation.SysLog;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.validator.constraints.Length;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @date 2023-05-17 017 16:30:37
 * @description
 */
@Slf4j
@RestController
@RequestMapping(value = "/scriptInfo")
@Validated
public class ExceptionScriptInfoController {

    @Resource
    private ScriptInfoService scriptInfoService;

    @SysLog("保存/更新脚本配置信息")
    @PostMapping("save")
    @CasPermissionRequired("aimt.script.change")
    public Result saveScriptInfo(@RequestBody @Validated ScriptInfo scriptInfo) {
        log.info("保存scriptInfo：{}",scriptInfo);
        if (scriptInfo.getId() == null) {
            int res = scriptInfoService.insert(scriptInfo);
            if (res == 1) {
                return Result.success("脚本信息保存成功");
            }else {
                return Result.error(560,"脚本对应信息保存失败");
            }
        }else {
            int res = scriptInfoService.update(scriptInfo);
            if (res == 1) {
                return Result.success("脚本信息更新成功");
            }else {
                return Result.error(560,"脚本对应信息更新失败");
            }
        }
    }

    @GetMapping("getAll")
    @CasPermissionRequired("aimt.script.query")
    public Result getAllScriptInfo(@RequestParam(value = "currentPage",defaultValue = "1") int currentPage,
                                   @RequestParam(value = "pageSize",defaultValue = "10") int pageSize,
                                   @RequestParam(value = "parentException",required = false,defaultValue = "") @Length(min = 0,max = 10,message = "请求的异常大类属性超长") String parentException,
                                   @RequestParam(value = "childException",required = false,defaultValue = "") @Length(min = 0,max = 10,message = "请求的异常子类属性超长") String childException) {
        PageHelper.startPage(currentPage,pageSize);
        List<ScriptInfo> scriptInfos = scriptInfoService.selectAll(parentException, childException);
        PageInfo<ScriptInfo> pageInfo = new PageInfo<>(scriptInfos);
        Map<String, Object> map = new ConcurrentHashMap<>();
        map.put("total",pageInfo.getTotal());
        map.put("list",pageInfo.getList());
        return Result.success(map);
    }

    @PostMapping("update")
    @CasPermissionRequired("aimt.script.change")
    public Result updateScriptInfo(@RequestBody ScriptInfo scriptInfo) {
        log.info("更新scriptInfo：{}",scriptInfo);
        int res = scriptInfoService.update(scriptInfo);
        if (res == 1) {
            return Result.success();
        }else {
            return Result.error(560,"脚本对应信息更新失败");
        }
    }

    @SysLog("删除脚本配置信息")
    @GetMapping("del/{id}")
    @CasPermissionRequired("aimt.script.change")
    public Result delScriptInfoById(@PathVariable("id") @Min(value = 0,message = "告警工单ID值错误") @Max(value = 9223372036854774807L,message = "告警工单ID超出最大值") Long id) {
        int res = scriptInfoService.deleteById(id);
        if (res == 1) {
            return Result.success("脚本配置信息删除成功");
        }else {
            return Result.error(560,"脚本配置信息删除失败");
        }
    }
}
