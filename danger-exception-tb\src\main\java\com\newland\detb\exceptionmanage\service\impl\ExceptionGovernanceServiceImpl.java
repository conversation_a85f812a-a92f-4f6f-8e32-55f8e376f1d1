package com.newland.detb.exceptionmanage.service.impl;

import com.newland.detb.exceptionmanage.mapper.ExceptionGovernanceMapper;
import com.newland.detb.exceptionmanage.service.ExceptionGovernanceService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2023-04-12 012 15:06:34
 * @description
 */
@Slf4j
@Service
public class ExceptionGovernanceServiceImpl implements ExceptionGovernanceService {

    @Resource
    private ExceptionGovernanceMapper exceptionGovernanceMapper;

    /**
     * 更改工单表中的治理状态
     * @param status 状态
     */
    @Override
    public void updateWorkOrderStatus(String status,Long id) {
        int res = exceptionGovernanceMapper.updateWorkOrderStatus(status, id);
        if (res < 1) {
            log.error("更改工单表状态失败");
        }
    }
}
