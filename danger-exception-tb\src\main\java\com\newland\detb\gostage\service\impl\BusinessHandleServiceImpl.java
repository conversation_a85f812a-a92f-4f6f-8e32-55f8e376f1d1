package com.newland.detb.gostage.service.impl;

import com.newland.detb.gostage.entity.BusinessHandle;
import com.newland.detb.gostage.mapper.BusinessHandleMapper;
import com.newland.detb.gostage.service.BusinessHandleService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

/**
 * 业务场景异常分析及处置方案服务实现类
 */
@Service
public class BusinessHandleServiceImpl implements BusinessHandleService {

    @Resource
    private BusinessHandleMapper businessHandleMapper;

    /**
     * 新增业务场景异常分析及处置方案
     *
     * @param businessHandle
     * @return
     */
    @Override
    public int save(BusinessHandle businessHandle) {
        return businessHandleMapper.save(businessHandle);
    }

    /**
     * 更新业务场景异常分析及处置方案
     *
     * @param businessHandle
     * @return
     */
    @Override
    public int update(BusinessHandle businessHandle) {
        return businessHandleMapper.update(businessHandle);
    }

    /**
     * 根据id删除业务场景异常分析及处置方案
     *
     * @param id
     * @return
     */
    @Override
    public int delete(Long id) {
        return businessHandleMapper.delete(id);
    }

    /**
     * 根据PID删除业务场景异常分析及处置方案
     *
     * @param id
     * @return
     */
    @Override
    public int deleteByPId(Long id) {
        return businessHandleMapper.deleteByPId(id);
    }

    /**
     * 根据id查询业务场景异常分析及处置方案
     *
     * @param id
     * @return
     */
    @Override
    public BusinessHandle queryById(Long id) {
        return businessHandleMapper.queryById(id);
    }

    /**
     * 根据父id查询业务场景异常分析及处置方案列表
     *
     * @param pId
     * @return
     */
    @Override
    public List<BusinessHandle> queryListByPId(Long pId) {
        return businessHandleMapper.queryListByPId(pId);
    }

    /**
     * 查询所有业务场景异常分析及处置方案
     *
     * @return
     */
    @Override
    public List<BusinessHandle> queryList() {
        return businessHandleMapper.queryList();
    }

    /**
     * 批量新增业务场景异常分析及处置方案
     *
     * @param businessHandleList
     * @return
     */
    @Override
    @Transactional
    public int batchSave(List<BusinessHandle> businessHandleList) {
        if (businessHandleList == null || businessHandleList.isEmpty()) {
            return 0;
        }
        return businessHandleMapper.batchSave(businessHandleList);
    }
}
