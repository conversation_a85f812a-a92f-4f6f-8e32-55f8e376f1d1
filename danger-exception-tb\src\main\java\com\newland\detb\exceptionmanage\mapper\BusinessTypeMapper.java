package com.newland.detb.exceptionmanage.mapper;

import com.newland.detb.exceptionmanage.entity.BusinessType;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【HE_BUSINESS_TYPE(业务异常场景表)】的数据库操作Mapper
* @createDate 2023-11-01 18:04:35
* @Entity com.newland.detb.exceptionmanage.entity.BusinessType
*/
@Mapper
public interface BusinessTypeMapper {

    /**
     * 根据业务场景名称查询是否存在
     * @param type
     * @return
     */
    BusinessType queryBusinessType(@Param("type") String type, @Param("parentName") String parentName, @Param("childName") String childName);

    /**
     * 查询全部业务异常集合
     * @param parentName
     * @param childName
     * @return
     */
    List<BusinessType> queryBusinessTypeList( @Param("parentName") String parentName, @Param("childName") String childName);

    /**
     * 新增业务异常场景
     * @param businessType
     * @return
     */
    int save(BusinessType businessType);

    /**
     *
     * @param businessType
     * @return
     */
    int update(BusinessType businessType);

    /**
     * 根据id删除业务异常场景
     * @param id
     * @return
     */
    int delete(@Param("id") Long id);

}




