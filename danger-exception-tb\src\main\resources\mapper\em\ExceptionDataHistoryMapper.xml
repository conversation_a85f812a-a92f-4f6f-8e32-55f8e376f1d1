<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.newland.detb.exceptionmanage.mapper.ExceptionDataHistoryMapper">

    <resultMap id="BaseResultMap" type="com.newland.detb.exceptionmanage.entity.ExceptionDataHistory">
            <id property="dataId" column="DATA_ID" jdbcType="DECIMAL"/>
            <result property="environment" column="ENVIRONMENT" jdbcType="VARCHAR"/>
            <result property="alarmType" column="ALARM_TYPE" jdbcType="VARCHAR"/>
            <result property="homeModule" column="HOME_MODULE" jdbcType="VARCHAR"/>
            <result property="processName" column="PROCESS_NAME" jdbcType="VARCHAR"/>
            <result property="exceptionDesc" column="EXCEPTION_DESC" jdbcType="VARCHAR"/>
            <result property="status" column="STATUS" jdbcType="DECIMAL"/>
            <result property="createTime" column="CREATE_TIME" jdbcType="TIMESTAMP"/>
            <result property="remark" column="REMARK" jdbcType="VARCHAR"/>
            <result property="alarmLevel" column="ALARM_LEVEL" jdbcType="VARCHAR"/>
            <result property="childProcessName" column="CHILD_PROCESS_NAME" jdbcType="VARCHAR"/>
            <result property="hostName" column="HOST_NAME" jdbcType="VARCHAR"/>
            <result property="hostIp" column="HOST_IP" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        DATA_ID,ENVIRONMENT,ALARM_TYPE,
        HOME_MODULE,PROCESS_NAME,EXCEPTION_DESC,
        STATUS,CREATE_TIME,REMARK,
        ALARM_LEVEL,CHILD_PROCESS_NAME,HOST_NAME,
        HOST_IP
    </sql>
    <insert id="insert">
        insert into he_exception_data_history(data_id,environment,alarm_type,home_module,process_name,exception_desc,status,remark,alarm_level,child_process_name,host_name,host_ip,create_time)
        values
            (#{dataId,jdbcType=BIGINT},
             #{environment,jdbcType=VARCHAR},
             #{alarmType,jdbcType=VARCHAR},
             #{homeModule,jdbcType=VARCHAR},
             #{processName,jdbcType=VARCHAR},
             #{exceptionDesc,jdbcType=VARCHAR},
             #{status,jdbcType=INTEGER},
             #{remark,jdbcType=VARCHAR},
             #{alarmLevel,jdbcType=VARCHAR},
             #{childProcessName,jdbcType=VARCHAR},
             #{hostName,jdbcType=VARCHAR},
             #{hostIp,jdbcType=VARCHAR},
             #{createTime,jdbcType=TIMESTAMP})
    </insert>
</mapper>
