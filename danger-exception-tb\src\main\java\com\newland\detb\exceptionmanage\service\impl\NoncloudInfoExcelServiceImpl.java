package com.newland.detb.exceptionmanage.service.impl;

import com.newland.detb.common.ResEnum;
import com.newland.detb.exception.ExceptionScenarioException;
import com.newland.detb.exceptionmanage.entity.ExceptionScenario;
import com.newland.detb.exceptionmanage.entity.GovernanceStrategy;
import com.newland.detb.exceptionmanage.entity.NoncloudInfo;
import com.newland.detb.exceptionmanage.mapper.NoncloudInfoMapper;
import com.newland.detb.util.mapper.ExcelBaseService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR>
 * @description 用于非云资料信息的导入操作
 */
@Slf4j
@Service
public class NoncloudInfoExcelServiceImpl  implements ExcelBaseService {

    @Resource
    private NoncloudInfoMapper noncloudInfoMapper;
    /**
     * 实现excel的保存方法
     * @param data
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer save(List<?> data) {
        AtomicInteger counter = new AtomicInteger(0);
        for (Object item : data) {
            log.info("保存前收到的非云资料信息信息：{}",item);
            NoncloudInfo noncloud = (NoncloudInfo) item;
            //判断数据库是否已经存在对应非云资料
            NoncloudInfo noncloudInfo= noncloudInfoMapper.qryNoncloudInfo(noncloud.getHostIp(),noncloud.getHostName(),noncloud.getProcessName(),noncloud.getBackupIp());
            if (noncloudInfo == null) {
                //保存非云资料信息
                int res = noncloudInfoMapper.insert(noncloud);
                if (res < 0) {
                    log.error("在保存非云资料信息时发生异常，当前数据：{}",item);
                    throw new ExceptionScenarioException(ResEnum.scenario_error_code,"导入非云资料信息保存时异常");
                }

                log.info("非云资料信息保存成功：{}",noncloud);
                counter.incrementAndGet();
            }else {
                throw new ExceptionScenarioException(ResEnum.scenario_error_code,"导入非云资料信息保存时重复，导入失败");
            }
        }
        log.info("所有非云资料信息数据已经保存完成，需要保存的条数：{}，成功保存的条数：{}",data.size(),counter.get());
        return counter.get();
    }
}
