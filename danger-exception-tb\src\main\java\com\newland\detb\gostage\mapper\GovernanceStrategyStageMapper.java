package com.newland.detb.gostage.mapper;

import com.newland.detb.exceptionmanage.entity.GovernanceStrategy;
import com.newland.detb.gostage.entity.GovernanceStrategyStage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【HE_GOVERNANCE_STRATEGY(异常治理策略信息表)】的数据库操作Mapper
* @createDate 2023-04-26 16:37:44
* @Entity com.newland.detb.exceptionmanage.entity.GovernanceStrategy
*/
@Mapper
public interface GovernanceStrategyStageMapper {

    /**
     * 插入异常治理策略
     * @param strategy
     * @return
     */
    int insert(GovernanceStrategyStage strategy);

    /**
     * 根据异常场景编码查询异常治理策略
     * @param sceneCode
     * @return
     */
    List<GovernanceStrategyStage> queryBySceneCode(@Param("sceneCode") String sceneCode);

    /**
     * 根据异常场景编码删除治理策略
     * @param sceneCode
     * @return
     */
    int delBySceneCode(@Param("sceneCode") String sceneCode);

    /**
     * 查询全部治理策略信息
     * @return
     */
    List<GovernanceStrategyStage> getAllGovernanceStrategies(@Param("remark") String remark);

    /**
     * 更新治理策略
     * @param remark
     * @return
     */
    int updateGovernanceStrategy(@Param("id") Long id,@Param("remark") String remark);

}




