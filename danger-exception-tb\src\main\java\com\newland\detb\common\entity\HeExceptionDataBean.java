package com.newland.detb.common.entity;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.newland.detb.common.converter.ExceptionStatusConverter;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.springframework.stereotype.Component;

import java.util.Date;
@Data
@AllArgsConstructor
@NoArgsConstructor
// @Accessors(chain = true)
@Component
public class HeExceptionDataBean {
    //异常数据编码(导出时忽略该字段)
    @ExcelIgnore
    private long dataId;

    //业务环境
    @ExcelProperty("环境")
    private String environment;

    //告警类型
    // @ColumnWidth(20)
    @ExcelProperty("告警类型")
    private String alarmType;

    //告警级别
    @ExcelProperty("告警级别")
    private String alarmLevel;

    //归属模块
    @ExcelProperty("归属模块")
    private String homeModule;

    //节点名称
    @ExcelProperty("节点名称")
    private String processName;

    //子节点名称
    @ExcelProperty("子节点名称")
    private String childProcessName;

    //主机名
    @ExcelProperty("主机名称")
    private String hostName;

    //主机IP
    @ExcelProperty("主机IP")
    private String hostIp;

    //异常信息描述
    @ExcelProperty("异常信息描述")
    private String exceptionDesc;

    //处理状态：0代表告警接收，1代表异常匹配，2代表生成治理策略，3代表执行异常治理，4代表信息同步
    @ExcelProperty(value = "处理状态",converter = ExceptionStatusConverter.class)
    private String status;

    //创建时间
    @ExcelProperty("创建时间")
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date createTime;

    //备注
    @ExcelProperty("备注")
    private String remark;
}
