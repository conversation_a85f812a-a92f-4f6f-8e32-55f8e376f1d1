package com.newland.detb.exceptionmanage.controller;

import cn.com.nlsoft.cas.client.annotation.CasPermissionRequired;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.newland.detb.common.Result;
import com.newland.detb.common.entity.HeExceptionDataBean;
import com.newland.detb.exceptionmanage.async.AsyncGovernanceTask;
import com.newland.detb.exceptionmanage.entity.BusinessType;
import com.newland.detb.exceptionmanage.entity.ExceptionScenario;
import com.newland.detb.exceptionmanage.entity.WorkOrder;
import com.newland.detb.exceptionmanage.mapper.ExceptionProgressMapper;
import com.newland.detb.exceptionmanage.service.BusinessTypeService;
import com.newland.detb.exceptionmanage.service.ExceptionProgressService;
import com.newland.detb.exceptionmanage.service.ExceptionScenarioService;
import com.newland.detb.exceptionmanage.service.WorkOrderService;
import com.newland.detb.gostage.async.AsyncGovernanceStageTask;
import com.newland.detb.gostage.entity.BusinessHandle;
import com.newland.detb.gostage.entity.ExceptionScenarioStage;
import com.newland.detb.gostage.service.BusinessHandleService;
import com.newland.detb.gostage.service.ExceptionScenarioStageService;
import com.newland.detb.log.annotation.SysLog;
import com.newland.detb.quartz.service.HeExceptionDataService;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023-05-16 016 18:04:07
 * @description 治理工单表相关操作
 */
@Slf4j
@RestController
@RequestMapping(value = "/workOrder")
@Validated
@Data
public class ExceptionWorkOrderController {

    @Resource
    private WorkOrderService workOrderService;

    @Lazy
    @Resource
    private AsyncGovernanceTask asyncGovernanceTask;

    @Resource
    private HeExceptionDataService heExceptionDataService;

    @Resource
    private ExceptionScenarioService exceptionScenarioService;

    @Resource
    private ExceptionProgressMapper exceptionProgressMapper;

    @Resource
    private BusinessTypeService businessTypeService;

    @Value("${business.name}")
    private String businessName;

    @Lazy
    @Resource
    private AsyncGovernanceStageTask asyncGovernanceStageTask;

    @Resource
    private BusinessHandleService  businessHandleService;

    @Resource
    private ExceptionScenarioStageService exceptionScenarioStageService;

    @SysLog("手动授权")
    @GetMapping("auth")
    @CasPermissionRequired("aimt.work_order.auth")
    public Result auth(@RequestParam("id") @Min(value = 0,message = "授权操作的治理工单ID值错误") @Max(value = 9223372036854774807L,message = "授权操作的治理工单ID超出最大值")Long id,
                       @RequestParam("auth") @Min(value = 0,message = "授权码只能是0或1或2") @Max(value = 2,message = "授权码只能是0或1或2") int auth) {
        WorkOrder workOrder = workOrderService.selectById(id);
        //判断异常数据是业务还是系统异常
        if (workOrder != null) {
            if (Objects.equals(workOrder.getSceneCode(),"BUSINESS")) {
                //说明是业务异常
                return businessAuth(auth,workOrder,id);
            } else if (Objects.equals(workOrder.getSceneCode(),businessName)) {
                //说明是封装的业务异常
                return businessNewAuth(auth,workOrder,id);
            } else {
                //系统异常
                return systemAuth(auth,workOrder,id);
            }
        }else {
            return Result.error(530,"授权数据不存在，请联系管理员查看！");
        }
    }

    /**
     * 授权执行系统异常恢复
     * @param auth
     * @param workOrder
     * @param id
     * @return
     */
    private Result systemAuth(int auth,WorkOrder workOrder,Long id) {
        //同意授权
        if (auth == 1) {
            workOrder.setIsAuth(1);
            //修改治理工单表状态
            workOrderService.updateIsAuth(1,id);
            //调用异步任务进行后续治理操作
            //根据data_ia查询工单表
            HeExceptionDataBean heExceptionDataBean = heExceptionDataService.selectById(workOrder.getDataId());
            //授权完成，修改workOrder表的治理状态
            workOrderService.updateGovernanceResInt("异常治理中",id);
            //根据异常场景编码查询异常场景库
            ExceptionScenario exceptionScenario = exceptionScenarioService.selectBySceneCode(Integer.parseInt(workOrder.getSceneCode()));
            //修改进度表3-2状态信息
            exceptionProgressMapper.updateChildProgressByDataId(heExceptionDataBean.getDataId(), 3,2,"success","授权成功",new Date());
            //执行后续异步任务(获取workOrder对象中的traceId)
            asyncGovernanceTask.executeAsync(workOrder.getTraceId(),heExceptionDataBean, exceptionScenario, workOrder);
            return Result.success();
        }else if (auth == 2) {
            //拒绝授权，治理流程直接结束
            workOrder.setIsAuth(2);
            //修改治理工单表状态
            workOrderService.updateIsAuth(2,id);
            //调用异步任务进行后续治理操作
            //根据data_ia查询工单表
            HeExceptionDataBean heExceptionDataBean = heExceptionDataService.selectById(workOrder.getDataId());
            //授权完成，修改workOrder表的治理状态
            workOrderService.updateGovernanceResInt("拒绝授权，结束治理",id);
            //更新后续明细数据
            updateProcessInfo(heExceptionDataBean);
            return Result.success("操作成功");
        }else {
            return Result.error(530,"授权操作异常，请重试");
        }
    }

    /**
     * 授权执行封装好的业务异常恢复
     * @param auth
     * @param workOrder
     * @param id
     * @return
     */
    private Result businessNewAuth(int auth,WorkOrder workOrder,Long id) {
        //同意授权
        if (auth == 1) {
            workOrder.setIsAuth(1);
            //修改治理工单表状态
            workOrderService.updateIsAuth(1,id);
            //调用异步任务进行后续治理操作
            //根据data_ia查询工单表
            HeExceptionDataBean heExceptionDataBean = heExceptionDataService.selectById(workOrder.getDataId());
            //授权完成，修改workOrder表的治理状态
            workOrderService.updateGovernanceResInt("异常治理中",id);
            //修改进度表3-2状态信息
            exceptionProgressMapper.updateChildProgressByDataId(heExceptionDataBean.getDataId(), 3,2,"success","授权成功",new Date());
            //执行后续异步任务(获取workOrder对象中的traceId)
            //businessHandleService
            List<ExceptionScenarioStage> exceptionScenarioStages = exceptionScenarioStageService.queryByAttributionAndType(heExceptionDataBean.getProcessName(), heExceptionDataBean.getChildProcessName());
            if (exceptionScenarioStages.isEmpty()) {
                return Result.error(530,"未查询到对应的业务类型，请检查");
            }
            asyncGovernanceStageTask.executeAsync(workOrder.getTraceId(),heExceptionDataBean,exceptionScenarioStages.get(0),workOrder,1);
            return Result.success();
        }else if (auth == 2) {
            //拒绝授权，治理流程直接结束
            workOrder.setIsAuth(2);
            //修改治理工单表状态
            workOrderService.updateIsAuth(2,id);
            //调用异步任务进行后续治理操作
            //根据data_ia查询工单表
            HeExceptionDataBean heExceptionDataBean = heExceptionDataService.selectById(workOrder.getDataId());
            //授权完成，修改workOrder表的治理状态
            workOrderService.updateGovernanceResInt("拒绝授权，结束治理",id);
            //更新后续明细数据
            updateProcessInfo(heExceptionDataBean);
            return Result.success("操作成功");
        }else {
            return Result.error(530,"授权操作异常，请重试");
        }
    }

    /**
     * 授权执行业务异常恢复
     * @param auth
     * @param workOrder
     * @param id
     * @return
     */
    private Result businessAuth(int auth,WorkOrder workOrder,Long id) {
        //同意授权
        if (auth == 1) {
            workOrder.setIsAuth(1);
            //修改治理工单表状态
            workOrderService.updateIsAuth(1,id);
            //调用异步任务进行后续治理操作
            //根据data_ia查询工单表
            HeExceptionDataBean heExceptionDataBean = heExceptionDataService.selectById(workOrder.getDataId());
            //授权完成，修改workOrder表的治理状态
            workOrderService.updateGovernanceResInt("异常治理中",id);
            //修改进度表3-2状态信息
            exceptionProgressMapper.updateChildProgressByDataId(heExceptionDataBean.getDataId(), 3,2,"success","授权成功",new Date());
            //执行后续异步任务(获取workOrder对象中的traceId)
            //
            BusinessType businessType = businessTypeService.queryBusinessType("BUSINESS", heExceptionDataBean.getProcessName(), heExceptionDataBean.getChildProcessName());
            if (businessType == null) {
                return Result.error(530,"未查询到对应的业务类型，请检查");
            }
            asyncGovernanceTask.executeBusinessAsync(workOrder.getTraceId(),heExceptionDataBean, workOrder,businessType);
            return Result.success();
        }else if (auth == 2) {
            //拒绝授权，治理流程直接结束
            workOrder.setIsAuth(2);
            //修改治理工单表状态
            workOrderService.updateIsAuth(2,id);
            //调用异步任务进行后续治理操作
            //根据data_ia查询工单表
            HeExceptionDataBean heExceptionDataBean = heExceptionDataService.selectById(workOrder.getDataId());
            //授权完成，修改workOrder表的治理状态
            workOrderService.updateGovernanceResInt("拒绝授权，结束治理",id);
            //更新后续明细数据
            updateProcessInfo(heExceptionDataBean);
            return Result.success("操作成功");
        }else {
            return Result.error(530,"授权操作异常，请重试");
        }
    }

    @PostMapping(value="findPage")
    @CasPermissionRequired("aimt.work_order.query")
    public Object findPage(@RequestBody @Validated WorkOrder workOrder,
                           @RequestParam(value = "pageNow",defaultValue = "1") int pageNow,
                           @RequestParam(value = "pageSize",defaultValue = "10") int pageSize) {
        Long dataId = workOrder.getDataId();
        String env = workOrder.getEnv();
        String appName = workOrder.getAppName();
        Integer isAuth = workOrder.getIsAuth();
        Integer isInspection = workOrder.getIsInspection();

        PageHelper.startPage(pageNow,pageSize);
        List<WorkOrder> page =  workOrderService.findPage(dataId,appName,env,isAuth,isInspection);
        PageInfo<WorkOrder> pageInfo=new PageInfo<>(page);
        return Result.success(pageInfo);
    }

    /**
     * 拼接生成单项巡检的页面
     * @param sceneCode
     * @return
     */
    @GetMapping("getInspectionPath")
    @CasPermissionRequired("aimt.work_order.query")
    public Result getInspectionPath(String sceneCode, HttpServletRequest request) {
        String inspectionPath = workOrderService.getInspectionPath(sceneCode,request);
        if (inspectionPath == null) {
            return Result.error(565,"此场景无需巡检");
        }
        return Result.success(inspectionPath);
    }

    /**
     * 更新进度表明细
     * @param heExceptionDataBean
     */
    private void updateProcessInfo(HeExceptionDataBean heExceptionDataBean) {
        heExceptionDataService.updateExceptionDataStatusById(heExceptionDataBean,3,"拒绝授权");
        heExceptionDataService.updateExceptionDataStatusById(heExceptionDataBean,4,"信息同步：拒绝授权，结束治理");
        heExceptionDataService.updateExceptionDataStatusById(heExceptionDataBean,5,"治理流程结束：拒绝授权，结束治理");
        exceptionProgressMapper.updateChildProgressByDataId(heExceptionDataBean.getDataId(), 3,2,"success","拒绝授权，结束治理",new Date());
        exceptionProgressMapper.updateChildProgressByDataId(heExceptionDataBean.getDataId(), 3,3,"success","执行治理：拒绝授权，结束治理",new Date());
        exceptionProgressMapper.updateChildProgressByDataId(heExceptionDataBean.getDataId(), 3,4,"success","单项巡检检查：拒绝授权，结束治理",new Date());
        exceptionProgressMapper.updateChildProgressByDataId(heExceptionDataBean.getDataId(), 4,1,"success","信息同步：拒绝授权，结束治理",new Date());
        exceptionProgressMapper.updateChildProgressByDataId(heExceptionDataBean.getDataId(), 4,2,"success","信息同步：拒绝授权，结束治理",new Date());
        exceptionProgressMapper.updateChildProgressByDataId(heExceptionDataBean.getDataId(), 4,3,"success","信息同步：拒绝授权，结束治理",new Date());
        exceptionProgressMapper.updateChildProgressByDataId(heExceptionDataBean.getDataId(), 5,1,"success","治理流程结束：拒绝授权，结束治理",new Date());
    }
}
