<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.newland.detb.exceptionmanage.mapper.ExceptionProgressMapper">

    <resultMap id="BaseResultMap" type="com.newland.detb.exceptionmanage.entity.ExceptionProgress">
            <id property="id" column="ID" jdbcType="DECIMAL"/>
            <result property="dataId" column="DATA_ID" jdbcType="DECIMAL"/>
            <result property="parentProgress" column="PARENT_PROGRESS" jdbcType="DECIMAL"/>
            <result property="childProgress" column="CHILD_PROGRESS" jdbcType="DECIMAL"/>
            <result property="execStatus" column="EXEC_STATUS" jdbcType="VARCHAR"/>
            <result property="execResult" column="EXEC_RESULT" jdbcType="VARCHAR"/>
            <result property="deleted" column="DELETED" jdbcType="DECIMAL"/>
            <result property="createTime" column="CREATE_TIME" jdbcType="TIMESTAMP"/>
            <result property="finishTime" column="FINISH_TIME" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID,DATA_ID,PARENT_PROGRESS,
        CHILD_PROGRESS,EXEC_STATUS,EXEC_RESULT,
        DELETED,CREATE_TIME,FINISH_TIME
    </sql>
    <insert id="insertInitData">
        INSERT INTO he_exception_progress
        (
        data_id,
        parent_progress,
        child_progress
        )
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{dataId}, #{item.parentProgress}, #{item.childProgress})
        </foreach>
    </insert>
    <update id="updateChildProgressByDataId">
        UPDATE he_exception_progress
        <set>
            <if test="execStatus != null">
                exec_status = #{execStatus,jdbcType=VARCHAR},
            </if>
            <if test="execResult != null">
                exec_result = #{execResult,jdbcType=VARCHAR},
            </if>
            <if test="finishTime != null">
                finish_time = #{finishTime,jdbcType=TIMESTAMP}
            </if>
        </set>
        <where>
            <if test="dataId != null">
                data_id = #{dataId}
            </if>
            <if test="parentProgress != null">
                and parent_progress = #{parentProgress}
            </if>
            <if test="parentProgress != null">
                and child_progress = #{childProgress}
            </if>
        </where>
    </update>
</mapper>
