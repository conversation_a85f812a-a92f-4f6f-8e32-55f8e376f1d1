<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.newland.detb.exceptionmanage.mapper.SceneInspectionMapper">

    <resultMap id="BaseResultMap" type="com.newland.detb.exceptionmanage.entity.SceneInspection">
            <result property="sceneCode" column="SCENE_CODE" jdbcType="DECIMAL"/>
            <result property="sceneName" column="SCENE_NAME" jdbcType="VARCHAR"/>
            <result property="inspectionCode" column="INSPECTION_CODE" jdbcType="DECIMAL"/>
            <result property="inspectionName" column="INSPECTION_NAME" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        SCENE_CODE,SCENE_NAME,INSPECTION_CODE,
        INSPECTION_NAME
    </sql>
    <insert id="insert">
        insert into he_scene_inspection(scene_code,scene_name,inspection_code) values(#{sceneCode,jdbcType=INTEGER},#{sceneName,jdbcType=VARCHAR},#{inspectionCode,jdbcType=INTEGER})
    </insert>

    <delete id="deleteBySceneCode">
        delete from he_scene_inspection where scene_code = #{sceneCode,jdbcType=INTEGER}
    </delete>
    <select id="getInspectionNameBySceneCode" resultType="com.newland.detb.exceptionmanage.entity.SceneInspection">
        select scene_code,scene_name,inspection_code,inspection_name from he_scene_inspection where scene_code = #{sceneCode,jdbcType=INTEGER}
    </select>
</mapper>
