package com.newland.detb.quartz.task;

import com.newland.detb.quartz.mapper.QuartzJobMapper;
import com.newland.detb.troubleshooting.mapper.HeBgocDataMapper;
import com.newland.detb.troubleshooting.mapper.HeCgocDataDetailMapper;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.quartz.JobDetail;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.quartz.PersistJobDataAfterExecution;
import org.springframework.scheduling.quartz.QuartzJobBean;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2023-03-17 017 10:34:09
 * @description 定时清理GOC数据表
 */
@Slf4j
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@Component
@PersistJobDataAfterExecution
public class TruncateGocDataJob extends QuartzJobBean {

    @Resource
    private HeBgocDataMapper heBgocDataMapper;

    @Resource
    private HeCgocDataDetailMapper heCgocDataDetailMapper;



    @Override
    protected void executeInternal(JobExecutionContext context) throws JobExecutionException {
        log.info("----------------------[定期清理GOC数据表任务执行中]----------------------");
        JobDetail jobDetail = context.getJobDetail();
        log.info("任务名称：{}，组名：{}，要执行固定的任务：{}",jobDetail.getKey().getName(),jobDetail.getKey().getGroup(),jobDetail.getDescription());
        //调用清理方法
        try {
            heBgocDataMapper.truncateBGocRecordTable();
            heBgocDataMapper.truncateBGocPerfTable();
            heCgocDataDetailMapper.truncateCGocTable();
            log.info("----------------------[定期清理GOC数据表任务执行完成]----------------------");
        } catch (Exception e) {
            e.printStackTrace();
            log.error("----------------------[定期清理GOC数据表任务执行异常]----------------------:{}",e.getMessage());
        }
    }
}
