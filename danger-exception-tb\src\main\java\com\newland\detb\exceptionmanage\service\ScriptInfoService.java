package com.newland.detb.exceptionmanage.service;

import com.newland.detb.exceptionmanage.entity.ScriptInfo;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【HE_SCRIPT_INFO(脚本配置信息表)】的数据库操作Service
* @createDate 2023-05-17 15:42:47
*/
public interface ScriptInfoService {

    /**
     * 保存scriptInfo
     * @param scriptInfo
     * @return
     */
    int insert(ScriptInfo scriptInfo);

    /**
     * 根据id删除
     * @param id
     * @return
     */
    int deleteById(Long id);

    /**
     * 更新scriptInfo
     * @param scriptInfo
     * @return
     */
    int update(ScriptInfo scriptInfo);

    /**
     * 根据id查询scriptInfo
     * @param id
     * @return
     */
    ScriptInfo selectById(Long id);

    /**
     * 查询全部scriptInfo
     * @return
     */
    List<ScriptInfo> selectAll(String parentException,String childException);

    /**
     * 根据child_exception查询脚本对应关系
     * @param childException
     * @return
     */
    ScriptInfo selectByChildException(String childException);

}
