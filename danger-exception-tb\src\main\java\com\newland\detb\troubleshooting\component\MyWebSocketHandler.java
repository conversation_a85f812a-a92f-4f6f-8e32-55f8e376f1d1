package com.newland.detb.troubleshooting.component;

import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.*;

import javax.websocket.Session;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @date 2024-03-14 014 15:46:51
 * @description
 */
@Slf4j
@Component
public class MyWebSocketHandler implements WebSocketHandler {

    /**存储session id 对应的数据*/
    private static final Map<String, WebSocketSession> sessionInfoMap = new ConcurrentHashMap<String, WebSocketSession>(24);

    @Override
    public void afterConnectionEstablished(@NonNull WebSocketSession session) throws Exception {
        String fingerId = (String) session.getAttributes().get("fingerId");
        log.info("===================webSocket连接建立后fingerId唯一标识信息:{},sessionId:{}",fingerId,session.getId());
        sessionInfoMap.put(fingerId,session);
        log.info("有新地址加入：{}，当前所有在线地址：{} 个",session.getId(),sessionInfoMap.size());
    }


    @Override
    public void handleMessage(WebSocketSession session, WebSocketMessage<?> message) throws Exception {
        log.info("收到前台消息：{}",message.getPayload());
        if (sessionInfoMap.containsKey((String) session.getAttributes().get("fingerId"))) {
            log.info(String.format("socket onmessage ==> 接收到信息:%s", message.getPayload()));
        }
    }

    @Override
    public void handleTransportError(WebSocketSession session, Throwable throwable) throws Exception {
        if (sessionInfoMap.containsKey(session.getId())) {
            log.info(String.format("socket异常，errorMessage:%s", throwable.getMessage()));
        }
    }

    @Override
    public void afterConnectionClosed(WebSocketSession session, CloseStatus closeStatus) throws Exception {
        String fingerId = (String) session.getAttributes().get("fingerId");
        log.info(String.format("socket已关闭，sessionid:%s, closeStatus:%s" , session.getId(), closeStatus));
        sessionInfoMap.remove(fingerId);
    }

    @Override
    public boolean supportsPartialMessages() {
        return true;
    }

    /**
     * 服务端发送消息给客户端
     * @param message 发送的消息内容
     * @param toSession 接收者的session
     */
    public void sendMessage(String message, WebSocketSession toSession) {
        try {
            log.info("服务端给客户端[{}]发送消息{}", toSession.getId(), message);
            toSession.sendMessage(new TextMessage(message));
        } catch (Exception e) {
            log.error("服务端发送消息给客户端失败", e);
        }
    }

    /**
     * 给所有的客户端发消息
     * @param message
     */
    public void sendAllMessage(String message) {
        try {
            for (WebSocketSession session : sessionInfoMap.values()) {
                log.info("服务端给客户端[{}]发送消息{}", session.getId(), message);
                session.sendMessage(new TextMessage(message));
            }
        } catch (Exception e) {
            log.error("服务端发送消息给客户端失败", e);
        }
    }

    /**
     * 获取websocket的session
     * @param fingerId
     * @return
     */
    public WebSocketSession getSession(String fingerId) {
        return sessionInfoMap.get(fingerId);
    }

    /**
     * 获取在线用户数
     * @return
     */
    public int getOnlineSum() {
        return sessionInfoMap.size();
    }
}
