package com.newland.detb.config;

import cn.com.nlsoft.cas.client.interceptor.CasAuthInterceptor;
import com.newland.detb.interceptor.IdentityAuthInterceptor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * <AUTHOR>
 * @date 2023-02-22 022 17:15:32
 * @description 拦截器配置
 */
@Configuration
public class InterceptorConfig implements WebMvcConfigurer {

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        //new CasAuthInterceptor()
        registry.addInterceptor(identityAuthInterceptor())
                .excludePathPatterns("/cas/login")
                .addPathPatterns("/**")
                .excludePathPatterns("/test/index").excludePathPatterns("/test/add");
    }

    @Bean
    public IdentityAuthInterceptor identityAuthInterceptor() {
        return new IdentityAuthInterceptor();
    }
}
