package com.newland.detb.tools.alarmMonitoring.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2022-12-01 001 16:08:44
 * @description 告警信息
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class AlarmInfo implements Serializable {

    //环境
    private String systemName;


    //类型
    private String moduleType;

    //节点
    private String nodeName;

    //子节点
    private String zinodeName;

    //异常明细
    private String warmInfo;

    //屏蔽开始时间
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;

    //屏蔽结束时间
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;

    //备注
    private String remark;
}
