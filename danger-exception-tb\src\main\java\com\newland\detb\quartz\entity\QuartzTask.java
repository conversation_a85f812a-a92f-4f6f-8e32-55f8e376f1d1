package com.newland.detb.quartz.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023-03-17 017 10:29:07
 * @description 定时任务实体类
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class QuartzTask {

    //id
    private int id;

    //定时触发的规则（cron）
    @NotNull
    @Length(min = 0,max = 16,message = "任务cron表达式超长")
    private String jobCron;

    //任务的名称
    @NotNull
    @Length(min = 0,max = 10,message = "任务名称超长")
    private String jobName;

    //任务的组
    @NotNull
    @Length(min = 0,max = 10,message = "任务分组名称超长")
    private String jobGroup;

    //任务的状态
    @NotNull
    @Length(min = 0,max = 1,message = "任务状态为运行或暂停")
    private String jobStatus;

    //任务对应的实体类名称com.newland.detb.quartz.task.ScanExceptionWorkOrderJob
    @NotNull
    @Length(min = 0,max = 80,message = "任务类路径超长")
    private String className;

    //任务的创建时间
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date createTime;
}
