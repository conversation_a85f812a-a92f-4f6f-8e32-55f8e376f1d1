package com.newland.detb.exceptionmanage.async;

import com.newland.detb.common.entity.HeExceptionDataBean;
import com.newland.detb.exceptionmanage.entity.ExceptionScenario;
import com.newland.detb.exceptionmanage.entity.WorkOrder;
import com.newland.detb.exceptionmanage.service.WorkOrderService;
import com.sun.corba.se.spi.orbutil.threadpool.Work;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2023-05-11 011 15:13:36
 * @description
 */
// @Lazy
@Slf4j
@Component
public class AsyncSingleInspectionTask extends BaseAsyncTask{

    @Lazy
    @Resource
    private AsyncSummaryMessageTask asyncSummaryMessageTask;


    /**
     * 判断是否需要触发单项巡检操作
     * @param heExceptionDataBean 异常数据工单
     * @param exceptionScenario 异常场景库
     * @param workOrder 治理工单
     */
    @Async("taskExecutor")
    @Override
    public void executeAsync(String traceId,HeExceptionDataBean heExceptionDataBean, ExceptionScenario exceptionScenario, WorkOrder workOrder) {
        asyncPrintLogInfo(traceId,"是否触发单项巡检");
        Integer isInspection = exceptionScenario.getIsInspection();
        updateHeExceptionDataWithRemark(heExceptionDataBean,3,"正在处理是否触发单项巡检");
        updateProgress(heExceptionDataBean,3,4,"doing","正在判断是否触发单项巡检");
        if (isInspection == 0) {
            asyncPrintLogInfo(traceId,3,4,"需要执行单项巡检操作");
            //调用隐患排查接口，传递巡检编码：例如2038
            //修改治理工单表状态
            // workOrderService.updateIsInspection(1, workOrder.getId());
            getWorkOrderService().updateIsInspection(0, workOrder.getId());
            updateHeExceptionDataWithRemark(heExceptionDataBean,3,"单项巡检判断结束");
            updateProgress(heExceptionDataBean,3,4,"success","单项巡检判断结束，需要触发");
            //准备汇总治理信息，进行消息同步
            asyncSummaryMessageTask.executeAsync(traceId,heExceptionDataBean,exceptionScenario,workOrder);
        }else if (isInspection == 1) {
            asyncPrintLogInfo(traceId,3,4,"不需要执行单项巡检操作");
            // workOrderService.updateIsInspection(1, workOrder.getId());
            getWorkOrderService().updateIsInspection(1, workOrder.getId());
            updateHeExceptionDataWithRemark(heExceptionDataBean,3,"单项巡检判断结束");
            updateProgress(heExceptionDataBean,3,4,"success","单项巡检判断结束，不需要触发");
            //准备汇总治理信息，进行消息同步
            asyncSummaryMessageTask.executeAsync(traceId,heExceptionDataBean,exceptionScenario,workOrder);
        }else {
            asyncPrintErrorLogInfo(traceId,3,4,"是否需要执行单项巡检操作配置信息读取失败，无法确认");
            updateHeExceptionDataWithRemark(heExceptionDataBean,3,"单项巡检判断失败");
            updateProgress(heExceptionDataBean,3,4,"fail","单项巡检判断失败");
            updateHeExceptionDataWithRemark(heExceptionDataBean,6,"单项巡检判断失败");
            updateProgress(heExceptionDataBean,5,1,"fail","单项巡检判断失败");
            updateWorkOrderWhenFailCase(heExceptionDataBean.getDataId(),"单项巡检判断失败");
        }
    }

    /**
     * 业务异常单-判断是否需要触发单项巡检操作
     * @param heExceptionDataBean 异常数据工单
     * @param workOrder 治理工单
     */
    @Async("taskExecutor")
    public void executeBusinessAsync(String traceId,HeExceptionDataBean heExceptionDataBean, WorkOrder workOrder,String msg) {
        asyncPrintLogInfo(traceId,"业务异常单-是否触发单项巡检");
        updateHeExceptionDataWithRemark(heExceptionDataBean,3,"业务异常单-正在处理是否触发单项巡检");
        updateProgress(heExceptionDataBean,3,4,"doing","业务异常单-正在判断是否触发单项巡检");
        asyncPrintLogInfo(traceId,3,4,"业务异常单-无需执行单项巡检操作");
        // workOrderService.updateIsInspection(1, workOrder.getId());
        getWorkOrderService().updateIsInspection(1, workOrder.getId());
        updateHeExceptionDataWithRemark(heExceptionDataBean,3,"业务异常单-单项巡检判断结束");
        updateProgress(heExceptionDataBean,3,4,"success","业务异常单-单项巡检判断结束，无需触发");
        //准备汇总治理信息，进行消息同步
        asyncSummaryMessageTask.executeBusinessAsync(traceId,heExceptionDataBean,workOrder,msg);
    }
}
