<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.newland.detb.gostage.mapper.BusinessHandleMapper">

    <resultMap id="BaseResultMap" type="com.newland.detb.gostage.entity.BusinessHandle">
        <id property="id" column="ID" jdbcType="BIGINT"/>
        <result property="pId" column="P_ID" jdbcType="BIGINT"/>
        <result property="index" column="INDEX" jdbcType="INTEGER"/>
        <result property="analysis" column="ANALYSIS" jdbcType="VARCHAR"/>
        <result property="handle" column="HANDLE" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID, P_ID, INDEX, ANALYSIS, HANDLE
    </sql>

    <insert id="save" useGeneratedKeys="true" keyProperty="id">
        insert into he_business_handle(P_ID, `INDEX`, ANALYSIS, HANDLE)
        values (#{pId,jdbcType=BIGINT}, #{index,jdbcType=INTEGER},
                #{analysis,jdbcType=VARCHAR}, #{handle,jdbcType=VARCHAR})
    </insert>

    <insert id="batchSave" useGeneratedKeys="true" keyProperty="id">
        insert into he_business_handle(P_ID, `INDEX`, ANALYSIS, HANDLE)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.pId,jdbcType=BIGINT}, #{item.index,jdbcType=INTEGER},#{item.analysis,jdbcType=VARCHAR}, #{item.handle,jdbcType=VARCHAR})
        </foreach>
    </insert>

    <update id="update">
        update he_business_handle
        <set>
            <if test="pId != null">
                P_ID = #{pId,jdbcType=BIGINT},
            </if>
            <if test="index != null">
                INDEX = #{index,jdbcType=INTEGER},
            </if>
            <if test="analysis != null and analysis != ''">
                ANALYSIS = #{analysis,jdbcType=VARCHAR},
            </if>
            <if test="handle != null and handle != ''">
                HANDLE = #{handle,jdbcType=VARCHAR}
            </if>
        </set>
        <where>
            ID = #{id,jdbcType=BIGINT}
        </where>
    </update>

    <delete id="delete">
        delete from he_business_handle where ID = #{id,jdbcType=BIGINT}
    </delete>
    <delete id="deleteByPId">
        delete from he_business_handle where P_id = #{pId,jdbcType=BIGINT}
    </delete>

    <select id="queryById" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from he_business_handle
        where ID = #{id,jdbcType=BIGINT}
    </select>

    <select id="queryListByPId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from he_business_handle
        where P_ID = #{pId,jdbcType=BIGINT}
        order by INDEX
    </select>

    <select id="queryList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from he_business_handle
        order by P_ID, INDEX
    </select>


</mapper>