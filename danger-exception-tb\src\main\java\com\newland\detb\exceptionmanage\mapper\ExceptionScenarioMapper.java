package com.newland.detb.exceptionmanage.mapper;

import com.newland.detb.exceptionmanage.entity.ExceptionScenario;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.poi.ss.formula.functions.T;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【HE_EXCEPTION_SCENARIO(异常场景库信息表)】的数据库操作Mapper
* @createDate 2023-04-18 14:56:30
* @Entity com.newland.detb.exceptionmanage.entity.ExceptionScenario
*/
@Mapper
public interface ExceptionScenarioMapper {

    /**
     * 保存数据
     * @param exceptionScenario
     * @return
     */
    int insert(ExceptionScenario exceptionScenario);

    /**
     * 根据id查询
     * @param id
     * @return
     */
    ExceptionScenario findById(Long id);

    /**
     * 倒序查询全部数据
     * @return
     */
    List<ExceptionScenario>  findAll();

    /**
     * 条件查询
     * @param env
     * @param type
     * @return
     */
    List<ExceptionScenario> findByCondition(@Param("type") String type,@Param("env") Integer env, @Param("subClass") String subClass,@Param("isAuth") Integer isAuth,@Param("isInspection") Integer isInspection);

    /**
     * 更新
     * @param exceptionScenario
     * @return
     */
    int update(ExceptionScenario exceptionScenario);

    /**
     * 根据id删除
     * @param id
     * @return
     */
    int delete(Long id);

    /**
     * 执行异常场景匹配操作
     * 根据三个查询条件：环境、父节点、子节点
     * @param env 环境
     * @param parentNode 父节点
     * @param childNode 子节点
     * @return
     */
    List<ExceptionScenario> findByThreeCondition(@Param("env") int env, @Param("parentNode") String parentNode, @Param("childNode") String childNode);

    /**
     * 异常场景匹配，使用tpye进行匹配，即使用异常关键字进行查询
     * @return
     */
    List<ExceptionScenario> getScenarioByType(String type);

    /**
     * 根据异常场景编码查询异常场景库
     * @param sceneCode
     * @return
     */
    ExceptionScenario selectBySceneCode(int sceneCode);

    /**
     * 根据ids查询异常场景库信息
     * @param exportList
     * @return
     */
    List<ExceptionScenario> findByIds(@Param("exportList") List<Long> exportList);

    /**
     * 获取最大的异常场景库编码号
     * @return
     */
    Integer getMaxSceneCode();

    /**
     * 根据id修改是否授权状态
     * @param id
     * @param isAuth
     * @return
     */
    int updateIsAuthById(@Param("id")Long id,@Param("isAuth")int isAuth);

    /**
     * 根据id修改是否需要触发单项巡检
     * @param id
     * @param isInspection
     * @return
     */
    int updateIsInspectionById(@Param("id")Long id,@Param("isInspection")int isInspection);

    /**
     * 根据id修改是否已经覆盖
     * @param id
     * @param isOverride
     * @return
     */
    int updateIsOverrideById(@Param("id") Long id,@Param("isOverride") int isOverride);
}
