package com.newland.detb.exceptionmanage.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-08-09 009 14:47:31
 * @description 原子操作的页面展示信息
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class AtomicInfo {

    //脚本名称
    private String fileName;

    //脚本功能说明
    private String fileMark;

    //脚本大小
    private Long fileSize;

    //脚本存储路径
    private String filePath;

    //最后一次修改时间
    private String lastModified;

    //原子信息集合
    private List<AtomicInfo> children;
}
