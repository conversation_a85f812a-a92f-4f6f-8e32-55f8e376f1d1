package com.newland.detb.common.converter;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.CellData;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.property.ExcelContentProperty;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023-04-17 017 9:47:13
 * @description 转换异常工单表中的状态（从0，1，2 -> 告警接收、异常匹配）
 */
@Slf4j
@Component
public class ExceptionStatusConverter implements Converter<String> {

    //处理状态：0代表告警接收，1代表异常匹配，2代表生成治理策略，3代表执行异常治理，4代表信息同步，5代表治理结束
    //导入
    private static final String acceptAlarm = "告警接收";
    private static final String exceptionMatching = "异常匹配";
    private static final String governanceStrategy = "生成治理策略";
    private static final String executeGovern = "执行异常治理";
    private static final String infoSync = "信息同步";
    private static final String governanceEnd = "治理结束";

    //导出数据时
    private static final String acceptAlarm2 = "0";
    private static final String exceptionMatching2 = "1";
    private static final String governanceStrategy2 = "2";
    private static final String executeGovern2 = "3";
    private static final String infoSync2 = "4";
    private static final String governanceEnd2 = "5";


    @Override
    public Class supportJavaTypeKey() {
        return String.class;
    }

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        return CellDataTypeEnum.STRING;
    }

    /**
     * 适用于导入数据，将excel中的值转换为java bean，存储到数据库中
     * @param cellData
     * @param excelContentProperty
     * @param globalConfiguration
     * @return
     * @throws Exception
     */
    @Override
    public String convertToJavaData(CellData cellData, ExcelContentProperty excelContentProperty, GlobalConfiguration globalConfiguration) throws Exception {
        //从cell中读取数据
        String value = cellData.getStringValue();
        //判断excel中的值，将其转换为预期的值
        if (Objects.equals(acceptAlarm,value)) {
            return "0";
        }else if (Objects.equals(exceptionMatching,value)) {
            return "1";
        }else if (Objects.equals(governanceStrategy,value)) {
            return "2";
        }else if (Objects.equals(executeGovern,value)) {
            return "3";
        }else if (Objects.equals(infoSync,value)) {
            return "4";
        }else if (Objects.equals(governanceEnd,value)) {
            return "5";
        }else {
            return "";
        }
    }

    /**
     * 适用于导出数据，将数据库中的数据导出到excel中
     * @param s
     * @param excelContentProperty
     * @param globalConfiguration
     * @return
     * @throws Exception
     */
    @Override
    public CellData convertToExcelData(String s, ExcelContentProperty excelContentProperty, GlobalConfiguration globalConfiguration) throws Exception {
        log.info("开始转换数据对象，参数：{}",s);
        //判断实体类中获取的值，转换为excel中预期的值，并封装为cellData对象
        if (Objects.equals(null,s) || s.length() == 0) {
            return new CellData<>("");
        }else if (Objects.equals(acceptAlarm2,s)) {
            return new CellData<>(acceptAlarm);
        }else if (Objects.equals(exceptionMatching2,s)) {
            return new CellData<>(exceptionMatching);
        }else if (Objects.equals(governanceStrategy2,s)) {
            return new CellData<>(governanceStrategy);
        }else if (Objects.equals(executeGovern2,s)) {
            return new CellData<>(executeGovern);
        }else if (Objects.equals(infoSync2,s)) {
            return new CellData<>(infoSync);
        }else if (Objects.equals(governanceEnd2,s)) {
            return new CellData<>(governanceEnd);
        }else {
            return new CellData<>("");
        }
    }
}
