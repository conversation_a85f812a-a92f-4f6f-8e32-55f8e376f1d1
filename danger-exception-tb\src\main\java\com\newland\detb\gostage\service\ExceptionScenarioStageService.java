package com.newland.detb.gostage.service;

import com.newland.detb.common.Result;
import com.newland.detb.exceptionmanage.entity.ExceptionScenario;
import com.newland.detb.gostage.entity.ExceptionScenarioStage;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【HE_EXCEPTION_SCENARIO(异常场景库信息表)】的数据库操作Service
* @createDate 2023-04-18 14:56:30
*/
public interface ExceptionScenarioStageService {

    /**
     * 保存或者更新异常场景库信息
     * @param scenario
     * @return
     */
    Result saveOrUpdate(ExceptionScenarioStage scenario);

    /**
     * 保存数据
     * @param exceptionScenari
     * @return
     */
    int insert(ExceptionScenarioStage exceptionScenari);

    /**
     * 根据id查询
     * @param id
     * @return
     */
    ExceptionScenarioStage findById(Long id);

    /**
     * 倒序查询全部数据
     * @return
     */
    List<ExceptionScenarioStage> findAll();

    /**
     * 条件查询
     * @param env
     * @param type
     * @return
     */
    List<ExceptionScenarioStage> findByCondition(String type, Integer env, String subClass, Integer isAuth,Integer isInspection);

    /**
     * 更新
     * @param exceptionScenario
     * @return
     */
    int update(ExceptionScenarioStage exceptionScenario);

    /**
     * 根据id删除
     * @param id
     * @return
     */
    int delete(Long id);

    /**
     * 根据异常场景编码查询异常场景库
     * @param sceneCode
     * @return
     */
    ExceptionScenarioStage selectBySceneCode(int sceneCode);

    /**
     * 根据id查询异常场景库信息
     * @param exportList
     * @return
     */
    List<ExceptionScenarioStage> findByIds(List<Long> exportList);

    /**
     * 获取最大的异常场景库编码号
     * @return
     */
    Integer getMaxSceneCode();

    /**
     * 根据id修改是否授权状态
     * @param id
     * @param isAuth
     * @return
     */
    int updateIsAuthById(Long id,int isAuth);

    /**
     * 根据id修改是否需要触发单项巡检
     * @param id
     * @param isInspection
     * @return
     */
    int updateIsInspectionById(Long id,int isInspection);

    /**
     * 根据id修改是否已经覆盖
     * @param id
     * @param isOverride
     * @return
     */
    int updateIsOverrideById(Long id, int isOverride);

    /**
     * 根据条件查询场景ID列表
     */
    List<Long> findScenarioIdsByCondition(String type, Integer env, String subClass, Integer isAuth, Integer isInspection);

    /**
     * 根据ID列表查询场景数据（包含处理方案）
     */
    List<ExceptionScenarioStage> findByIdList(List<Long> ids);

    /**
     * 根据归属和类型查询
     * @param attribution
     * @param type
     * @return
     */
    List<ExceptionScenarioStage> queryByAttributionAndType(String attribution, String type);
}
