package com.newland.detb.exceptionmanage.service;

import com.newland.detb.exceptionmanage.entity.SceneInspection;
import org.apache.ibatis.annotations.Param;

/**
* <AUTHOR>
* @description 针对表【HE_SCENE_INSPECTION(单项巡检和异常场景关联配置表)】的数据库操作Service
* @createDate 2023-06-13 14:21:37
*/
public interface SceneInspectionService {

    /**
     * 保存关联数据(巡检指标名称手动进行配置)
     * @param sceneInspection
     * @return
     */
    int insert(SceneInspection sceneInspection);

    /**
     * 根据sceneCode删除
     * @param sceneCode
     * @return
     */
    int deleteBySceneCode(@Param("sceneCode") int sceneCode);

}
