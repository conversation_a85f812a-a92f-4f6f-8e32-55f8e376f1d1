package com.newland.detb.exceptionmanage.controller;

import cn.com.nlsoft.cas.client.annotation.CasPermissionRequired;
import cn.hutool.core.util.IdUtil;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.newland.detb.common.ExcelListener;
import com.newland.detb.common.ResEnum;
import com.newland.detb.common.Result;
import com.newland.detb.exception.ExceptionScenarioException;
import com.newland.detb.exception.FileCheckException;
import com.newland.detb.exceptionmanage.entity.ExceptionScenario;
import com.newland.detb.exceptionmanage.entity.GovernanceStrategy;
import com.newland.detb.exceptionmanage.service.ExceptionScenarioService;
import com.newland.detb.exceptionmanage.service.GovernanceStrategyService;
import com.newland.detb.exceptionmanage.service.impl.ExceptionScenarioExcelServiceImpl;
import com.newland.detb.log.annotation.SysLog;
import com.newland.detb.troubleshooting.entity.SingleInspectionBean;
import com.newland.detb.util.CheckFileSuffixUtil;
import com.newland.detb.util.ExcelUtil;
import com.newland.detb.util.TimeFormatUtil;
import com.newland.detb.util.mapper.ExcelBaseService;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.poifs.filesystem.FileMagic;
import org.hibernate.validator.constraints.Length;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.Max;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @date 2023-04-18 018 17:15:04
 * @description
 */
@Slf4j
@RestController
@RequestMapping("/exceptionScenario")
@Validated
public class ExceptionScenarioController {

    @Resource
    private ExceptionScenarioService exceptionScenarioService;

    @Resource
    private ExceptionScenarioExcelServiceImpl exceptionScenarioExcelService;

    @Resource
    private GovernanceStrategyService governanceStrategyService;

    /**
     * 保存异常场景库信息
     * @param exceptionScenario
     * @return
     */
    @SysLog("保存/更新异常场景库信息")
    @PostMapping("save")
    @CasPermissionRequired("aimt.scenario.change")
    public Result insertExceptionScenario(@RequestBody @Validated ExceptionScenario exceptionScenario) {
        log.info("接收到的异常场景库信息：{}",exceptionScenario);
        return exceptionScenarioService.saveOrUpdate(exceptionScenario);
    }

    @GetMapping("/getMaxSceneCode")
    @CasPermissionRequired("aimt.scenario.query")
    public Result getMaxSceneCode() {
        Integer maxSceneCode = exceptionScenarioService.getMaxSceneCode();
        if (maxSceneCode == null) {
            maxSceneCode = 1000;
        }
        return Result.success(maxSceneCode);
    }

    /**
     * 根据id查询异常场景库信息
     * @param id
     * @return
     */
    @SysLog("查询单条异常场景库信息")
    @GetMapping("query/{id}")
    @CasPermissionRequired("aimt.scenario.query")
    public Result queryExceptionScenarioById(@PathVariable("id") @Max(value = 9223372036854774807L,message = "异常场景库ID超出最大值") Long id) {
        ExceptionScenario exceptionScenario = exceptionScenarioService.findById(id);
        if (!Optional.ofNullable(exceptionScenario).isPresent()) {
            log.info("根据id：{}，查询异常场景库信息不存在！",id);
            throw new ExceptionScenarioException(ResEnum.scenario_error_code,"查询异常场景库信息不存在！");
        }else {
            return Result.success(exceptionScenario);
        }
    }

    /**
     * 分页查询全部的异常场景库信息
     * @return
     */
    @GetMapping("queryAll")
    @CasPermissionRequired("aimt.scenario.query")
    public Result queryAllExceptionScenario(@RequestParam(value = "currentPage",defaultValue = "1") int currentPage,
                                            @RequestParam(value = "pageSize",defaultValue = "10") int pageSize,
                                            @RequestParam(value = "type",required = false,defaultValue = "") @Length(min = 0,max = 20,message = "请求的类型参数错误") String type,
                                            @RequestParam(value = "env",required = false) @Max(value = 1,message = "请求的环境参数错误") Integer env,
                                            @RequestParam(value = "subClass",required = false,defaultValue = "") @Length(min = 0,max = 100,message = "请求的异常子类参数错误") String subClass,
                                            @RequestParam(value = "isAuth",required = false) @Max(value = 1,message = "请求的授权参数错误") Integer isAuth,
                                            @RequestParam(value = "isInspection",required = false) @Max(value = 1,message = "请求的巡检参数错误") Integer isInspection) {
        //开启分页
        PageHelper.startPage(currentPage, pageSize);
        List<ExceptionScenario> scenarioList = exceptionScenarioService.findByCondition(type, env, subClass, isAuth, isInspection);
        PageInfo<ExceptionScenario> pageInfo = new PageInfo<>(scenarioList);
        Map<String, Object> map = new ConcurrentHashMap<>();
        map.put("total", pageInfo.getTotal());
        map.put("list", pageInfo.getList());
        return  Result.success(map);
    }

    /**
     * 条件查询
     * @param env
     * @param type
     * @return
     */
    @GetMapping("queryByCondition")
    @CasPermissionRequired("aimt.scenario.query")
    public Result queryExceptionScenarioByCondition(@RequestParam(value = "type",required = false)String type,
                                                    @RequestParam(value = "env",required = false)int env,
                                                    @RequestParam(value = "subClass",required = false)String subClass,
                                                    @RequestParam(value = "isAuth",required = false)int isAuth,
                                                    @RequestParam(value = "isInspection",required = false)int isInspection) {
        List<ExceptionScenario> condition = exceptionScenarioService.findByCondition(type, env, subClass,isAuth,isInspection);
        return Result.success(condition);
    }

    /**
     * 根据id删除异常场景库信息
     * @param id
     * @return
     */
    @SysLog("删除异常场景库信息")
    @GetMapping("delete/{id}")
    @CasPermissionRequired("aimt.scenario.change")
    public Result deleteExceptionScenario(@PathVariable("id") @Max(value = 9223372036854774807L,message = "异常场景库ID超出最大值") Long id) {
        int res = exceptionScenarioService.delete(id);
        if (res < 0) {
            log.info("根据id：{}，删除异常场景库信息失败",id);
            throw new ExceptionScenarioException(ResEnum.scenario_error_code,"删除异常场景库失败");
        }else {
            return Result.success("异常场景库信息删除成功");
        }
    }

    /**
     * 导出异常场景库信息
     * @param response
     */
    @SysLog("导出异常场景库信息")
    @GetMapping("export")
    @CasPermissionRequired("aimt.scenario.change")
    public void exportExceptionScenario(HttpServletResponse response,@RequestParam List<Long> exportList) {
        System.out.println("exportList:" + exportList);
        List<ExceptionScenario> scenarioList;
        if (exportList.size() == 0) {
            scenarioList = exceptionScenarioService.findAll();
        }else {
            scenarioList = exceptionScenarioService.findByIds(exportList);
        }
        // List<ExceptionScenario> all = exceptionScenarioService.findAll();
        if (Optional.ofNullable(scenarioList).isPresent()) {
            log.info("开始导出异常场景库内容，共计条数：{}",scenarioList.size());
            ExcelUtil.exportDataToExcel(response,"导出异常场景库信息","异常场景库信息_" + TimeFormatUtil.getDate(new Date()),"场景库明细",ExceptionScenario.class,scenarioList);
        }else {
            log.info("异常场景库信息为空不需要导出");
        }
    }

    /**
     * 导入操作
     * @param file
     * @return
     */
    @SysLog("导入异常场景库信息")
    @PostMapping("import")
    @CasPermissionRequired("aimt.scenario.change")
    public Result importExceptionScenario(MultipartFile file) {
        //校验文件格式
        boolean excelFile = false;
        try {
            excelFile = CheckFileSuffixUtil.isExcelFile(file.getInputStream());
            //避免出现，文件是excel文件，但是后缀不是xls或xlsx后缀
            if (!Objects.requireNonNull(file.getOriginalFilename()).endsWith(".xls") && !file.getOriginalFilename().endsWith(".xlsx")) {
                excelFile = false;
            }
        } catch (IOException e) {
            e.printStackTrace();
            throw new FileCheckException(ResEnum.file_check_error_code,"校验文件后缀获取文件流失败");
        }
        if (excelFile) {
            if (Optional.ofNullable(file).isPresent()) {
                log.info("开始导入异常场景库信息，文件大小：{}KB", file.getSize() / 1024);
                ExcelUtil.importData(file,ExceptionScenario.class,"异常场景库信息导入",new ExcelListener<>(exceptionScenarioExcelService));
                log.info("异常场景库信息导入完成");
                return Result.success("异常场景库信息导入成功");
            }else {
                log.info("上传的异常场景库信息为空");
                return Result.error(ResEnum.scenario_error_code,"异常场景库信息导入失败");
            }
        }else {
            return Result.error(ResEnum.scenario_error_code,"只能上传文件格式为.xls或.xlsx");
        }
    }

    /**
     * 更新是否授权
     * @param id
     * @param isAuth
     * @return
     */
    @GetMapping("updateIsAuth")
    @CasPermissionRequired("aimt.scenario.auth")
    public Result updateScenarioIsAuth(@RequestParam("id") Long id,@RequestParam("isAuth")Integer isAuth) {
        int res = exceptionScenarioService.updateIsAuthById(id, isAuth);
        if (res < 1) {
            return Result.error(500,"授权信息更新失败");
        }
        return Result.success("授权信息更新成功");
    }

    /**
     * 更新是否需要触发单项巡检
     * @param id
     * @param isInspection
     * @return
     */
    @GetMapping("updateIsInspection")
    @CasPermissionRequired("aimt.scenario.change")
    public Result updateIsInspection(@RequestParam("id") Long id,@RequestParam("isInspection")Integer isInspection) {
        int res = exceptionScenarioService.updateIsInspectionById(id, isInspection);
        if (res < 1) {
            return Result.error(500,"巡检信息更新失败");
        }
        return Result.success("巡检信息更新成功");
    }

    /**
     * 更新是否覆盖
     * @param id
     * @param isOverride
     * @return
     */
    @GetMapping("updateIsOverride")
    @CasPermissionRequired("aimt.scenario.change")
    public Result updateIsOverride(@RequestParam("id") Long id,@RequestParam("isOverride")Integer isOverride) {
        int res = exceptionScenarioService.updateIsOverrideById(id, isOverride);
        if (res < 1) {
            return Result.error(500,"覆盖信息更新失败");
        }
        return Result.success("覆盖信息更新成功");
    }
}
