package com.newland.detb.exceptionmanage.entity;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023-06-02 002 15:34:27
 * @description
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class EchartsData implements Serializable {

    private static final long serialVersionUID = 1L;

    @ExcelProperty("数据项名称")
    private String name;

    @ExcelProperty("数据项个数")
    private Long value;

}
