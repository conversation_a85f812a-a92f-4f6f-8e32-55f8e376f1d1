package com.newland.detb.common.converter;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.CellData;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.property.ExcelContentProperty;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023-05-11 011 10:08:43
 * @description 是否需要授权和是否需要触发单项巡检
 */
public class ExceptionScenarioIsAuthOrIsInspectionConverter implements Converter<Integer> {

    //0表示需要，1表示不需要
    private static final String need = "需要";
    private static final String nonNeed = "不需要";

    private static final Integer needNum = 0;
    private static final Integer nonNeedNum = 1;

    @Override
    public Class supportJavaTypeKey() {
        return Integer.class;
    }

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        return CellDataTypeEnum.STRING;
    }

    @Override
    public Integer convertToJavaData(CellData cellData, ExcelContentProperty excelContentProperty, GlobalConfiguration globalConfiguration) throws Exception {
        String value = cellData.getStringValue();
        if (Objects.equals(value,need)) {
            return needNum;
        }else if (Objects.equals(value,nonNeed)) {
            return nonNeedNum;
        }else {
            return null;
        }
    }

    @Override
    public CellData convertToExcelData(Integer integer, ExcelContentProperty excelContentProperty, GlobalConfiguration globalConfiguration) throws Exception {
        if (Objects.equals(integer,needNum)) {
            return new CellData(need);
        }else if (Objects.equals(integer,nonNeedNum)) {
            return new CellData(nonNeed);
        }else {
            return new CellData("");
        }
    }
}
