package com.newland.detb.quartz.service.impl;

import com.cronutils.model.CronType;
import com.cronutils.model.definition.CronDefinition;
import com.cronutils.model.definition.CronDefinitionBuilder;
import com.cronutils.parser.CronParser;
import com.newland.detb.exception.QuartzException;
import com.newland.detb.quartz.entity.QuartzTask;
import com.newland.detb.quartz.mapper.QuartzJobMapper;
import com.newland.detb.quartz.service.QuartzJobService;
import lombok.extern.slf4j.Slf4j;
import org.quartz.*;
import org.springframework.scheduling.quartz.QuartzJobBean;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-03-17 017 10:33:34
 * @description
 */
@Slf4j
@Service
public class QuartzJobServiceImpl implements QuartzJobService {

    @Resource
    private QuartzJobMapper quartzJobMapper;

    @Resource
    private Scheduler scheduler;

    @Override
    public List<QuartzTask> selectAllTasks(String jobName,String jobGroup,String jobStatus) {
        return quartzJobMapper.selectAllTasks(jobName, jobGroup, jobStatus);
    }

    @Override
    public QuartzTask selectTaskById(int id) {
        return quartzJobMapper.selectById(id);
    }

    @Override
    @Transactional
    public int insertTask(QuartzTask quartzTask) {
        //进行插入操作
        // quartzTask.setCreateTime(new Date());
        //校验cron表达式是否合法
        boolean checkCronValid = checkCronValid(quartzTask.getJobCron());
        if (!checkCronValid) {
            throw new QuartzException(515,"定时任务cron表达式格式错误，请重新输入");
        }
        //判断是不是com.newland.detb.quartz.task.包下的任务类
        if (!quartzTask.getClassName().startsWith("com.newland.detb.quartz.task.")) {
            throw new QuartzException(515,"类路径输入错误，请检查后重新提交");
        }
        //com.newland.detb.quartz.task.TaskJob
        Class<? extends Job> className;
        try {
            className = (Class<? extends QuartzJobBean>) Class.forName(quartzTask.getClassName());
        } catch (ClassNotFoundException e) {
            e.printStackTrace();
            throw new QuartzException(515,"找不到对应类路径");
        }
        int task = quartzJobMapper.insertTask(quartzTask);
        if (task < 1) {
            throw new QuartzException(515,"新增定时任务失败");
        }

        JobDetail jobDetail = JobBuilder.newJob(className)
                .withDescription("quartz定时任务,从02分开始扫描，每5分钟一扫")
                .withIdentity(quartzTask.getJobName(), quartzTask.getJobGroup())
                .storeDurably()
                .usingJobData("jobId", quartzTask.getId())
                .build();
        //定义一个触发器
        CronTrigger cronTrigger = TriggerBuilder.newTrigger()
                .forJob(jobDetail)
                .withIdentity(quartzTask.getJobName(), quartzTask.getJobGroup())
                .withSchedule(CronScheduleBuilder.cronSchedule(quartzTask.getJobCron()))
                .build();

        //执行job任务
        try {
            scheduler.scheduleJob(jobDetail,cronTrigger);
        } catch (SchedulerException e) {
            e.printStackTrace();
            throw new QuartzException(515,"新增的定时任务执行失败");
        }
        return task;
    }

    /**
     * 校验cron表达式
     * @param cron
     * @return
     */
    public boolean checkCronValid(String cron) {
        try {
            //SPRING应该是使用最广泛的类型,但假若任务调度依赖于xxl-job平台,则需要调整为CronType.QUARTZ
            CronDefinition cronDefinition = CronDefinitionBuilder.instanceDefinitionFor(CronType.SPRING);
            CronParser cronParser = new CronParser(cronDefinition);
            cronParser.parse(cron);
            log.info("cron表达式格式正确：{}",cron);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("cron表达式格式错误：{}，校验不通过",cron);
            return false;
        }
        return true;
    }

    @Override
    public int deleteTaskById(int id) {
        int res = 0;
        QuartzTask quartzTask = quartzJobMapper.selectById(id);
        if (quartzTask == null) {
            throw new QuartzException(515,"该定时任务不存在");
        }
        JobKey jobKey = JobKey.jobKey(quartzTask.getJobName(), quartzTask.getJobGroup());

        try {
            boolean exists = scheduler.checkExists(jobKey);
            if (!exists) {
                // throw new RuntimeException("该定时任务不存在，无法删除");
                log.error("删除定时任务时，定时任务在quartz调度器中不存在，所以将直接从数据库中删除");
            }else {
                scheduler.deleteJob(jobKey);
            }
            res = quartzJobMapper.deleteTaskById(quartzTask.getId());
        } catch (SchedulerException e) {
            e.printStackTrace();
            throw new QuartzException(515,"删除定时任务失败");
        }
        return res;
    }

    @Override
    @Transactional
    public void pause(int id) {
        QuartzTask quartzTask = quartzJobMapper.selectById(id);
        if (quartzTask == null) {
            throw new QuartzException(515,"该定时任务不存在");
        }
        JobKey jobKey = JobKey.jobKey(quartzTask.getJobName(), quartzTask.getJobGroup());

        try {
            boolean exists = scheduler.checkExists(jobKey);
            if (!exists) {
                throw new QuartzException(515,"该定时任务不存在，无法暂停");
            }
            scheduler.pauseJob(jobKey);
            //修改数据库中定时任务状态
            int res = quartzJobMapper.updateJobStatus("1", quartzTask.getId());
            if (res == 1) {
                log.info("定时任务暂停成功，修改状态成功");
            }else {
                log.error("定时任务暂停成功，修改状态失败");
            }
        } catch (SchedulerException e) {
            e.printStackTrace();
            throw new QuartzException(515,"暂停定时任务失败");
        }
    }

    @Override
    public void resume(int id) {
        QuartzTask quartzTask = quartzJobMapper.selectById(id);
        if (quartzTask == null) {
            throw new QuartzException(515,"该定时任务不存在");
        }
        JobKey jobKey = JobKey.jobKey(quartzTask.getJobName(), quartzTask.getJobGroup());

        try {
            boolean exists = scheduler.checkExists(jobKey);
            if (!exists) {
                throw new QuartzException(515,"该定时任务不存在，无法重新恢复");
            }
            scheduler.resumeJob(jobKey);
            //修改数据库中定时任务状态
            int res = quartzJobMapper.updateJobStatus("0", quartzTask.getId());
            if (res == 1) {
                log.info("定时任务重启成功，修改状态成功");
            }else {
                log.error("定时任务重启成功，修改状态失败");
            }
        } catch (SchedulerException e) {
            e.printStackTrace();
            throw new QuartzException(515,"恢复定时任务失败");
        }
    }

    @Override
    public int updateTaskCron(String cron, int id) {
        return quartzJobMapper.updateTaskCron(cron,id);
    }

    /**
     * 更新相当于是把job重新复制一份儿，然后删除掉原来的job，把新建的job再次插入到数据库表中
     * 修改cron表达式
     * @param cron
     * @param id
     */
    @Override
    public void updateQuartzJobCron(String cron, int id) {
        QuartzTask quartzTask = quartzJobMapper.selectById(id);
        if (quartzTask == null) {
            throw new QuartzException(515,"该定时任务不存在");
        }
        //准备插入的job
        QuartzTask newQuartzTask = new QuartzTask();
        newQuartzTask.setJobCron(cron).setJobName(quartzTask.getJobName()).setJobGroup(quartzTask.getJobGroup()).setJobStatus(quartzTask.getJobStatus()).setClassName(quartzTask.getClassName());
        //从scheduler调度器中删除掉原来的job
        JobKey jobKey = JobKey.jobKey(quartzTask.getJobName(), quartzTask.getJobGroup());
        try {
            boolean exists = scheduler.checkExists(jobKey);
            if (!exists) {
                throw new QuartzException(515,"该定时任务不存在，无法删除");
            }
            scheduler.deleteJob(jobKey);
        } catch (SchedulerException e) {
            e.printStackTrace();
            throw new QuartzException(515,"检查定时任务是否存在发生异常");
        }
        //从自建数据库中删除
        int res = quartzJobMapper.deleteTaskById(id);
        if (res < 1) {
            throw new QuartzException(515,"数据库中删除定时任务失败");
        }
        //最后再重新插入一条数据
        int insertResult = quartzJobMapper.insertTask(newQuartzTask);
        if (insertResult < 1) {
            throw new QuartzException(515,"数据库中新增定时任务失败");
        }
        //交给scheduler调度器执行
        Class<? extends Job> className;
        try {
            className = (Class<? extends QuartzJobBean>) Class.forName(quartzTask.getClassName());
        } catch (ClassNotFoundException e) {
            e.printStackTrace();
            throw new QuartzException(515,"找不到对应类路径");
        }

        JobDetail jobDetail = JobBuilder.newJob(className)
                .withDescription("quartz定时任务")
                .withIdentity(quartzTask.getJobName(), quartzTask.getJobGroup())
                .storeDurably()
                .build();
        //定义一个触发器
        CronTrigger cronTrigger = TriggerBuilder.newTrigger()
                .forJob(jobDetail)
                .withIdentity(newQuartzTask.getJobName(), newQuartzTask.getJobGroup())
                .withSchedule(CronScheduleBuilder.cronSchedule(newQuartzTask.getJobCron()))
                .build();
        try {
            scheduler.scheduleJob(jobDetail,cronTrigger);
        } catch (SchedulerException e) {
            e.printStackTrace();
            throw new QuartzException(515,"更新cron表达式时，新增job定时任务失败");
        }
    }

}
