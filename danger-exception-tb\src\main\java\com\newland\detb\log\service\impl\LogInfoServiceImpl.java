package com.newland.detb.log.service.impl;

import com.newland.detb.log.entity.LogInfo;
import com.newland.detb.log.mapper.LogInfoMapper;
import com.newland.detb.log.service.LogInfoService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-04-06 006 10:33:48
 * @description
 */
@Service
public class LogInfoServiceImpl implements LogInfoService {

    @Resource
    private LogInfoMapper logInfoMapper;

    /**
     * 保存日志
     *
     * @param logInfo
     * @return
     */
    @Override
    public int save(LogInfo logInfo) {
        return logInfoMapper.save(logInfo);
    }

    /**
     * 根据id删除
     *
     * @param id
     * @return
     */
    @Override
    public int deleteLogInfoById(Long id) {
        return logInfoMapper.deleteLogInfoById(id);
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @Override
    public int deleteLogInfoBatch(Long[] ids) {
        return logInfoMapper.deleteLogInfoBatch(ids);
    }

    /**
     * 根据id查询日志
     *
     * @param id
     * @return
     */
    @Override
    public LogInfo queryLogInfoById(Long id) {
        return logInfoMapper.queryLogInfoById(id);
    }

    /**
     * 查询全部日志信息
     *
     * @return
     */
    @Override
    public List<LogInfo> queryLogInfo(String operation) {
        return logInfoMapper.queryLogInfo(operation);
    }
}
