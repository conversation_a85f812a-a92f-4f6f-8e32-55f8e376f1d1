<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.newland.detb.exceptionmanage.mapper.ScriptInfoMapper">

    <resultMap id="BaseResultMap" type="com.newland.detb.exceptionmanage.entity.ScriptInfo">
            <id property="id" column="ID" jdbcType="DECIMAL"/>
            <result property="parentException" column="PARENT_EXCEPTION" jdbcType="VARCHAR"/>
            <result property="parentCode" column="PARENT_CODE" jdbcType="VARCHAR"/>
            <result property="childException" column="CHILD_EXCEPTION" jdbcType="VARCHAR"/>
            <result property="childCode" column="CHILD_CODE" jdbcType="VARCHAR"/>
            <result property="deleted" column="DELETED" jdbcType="DECIMAL"/>
            <result property="createTime" column="CREATE_TIME" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID,PARENT_EXCEPTION,PARENT_CODE,
        CHILD_EXCEPTION,CHILD_CODE,DELETED,
        CREATE_TIME
    </sql>
    <insert id="insert">
        INSERT INTO he_script_info
        (
        parent_exception,
        parent_code,
        child_exception,
        child_code)
        values
        (
        #{parentException,jdbcType=VARCHAR},
        #{parentCode,jdbcType=VARCHAR},
        #{childException,jdbcType=VARCHAR},
        #{childCode,jdbcType=VARCHAR}
        )
    </insert>
    <update id="update">
        update he_script_info
        <set>
            <if test="parentException!=null">parent_exception = #{parentException,jdbcType=VARCHAR},</if>
            <if test="parentCode!=null">parent_code = #{parentCode,jdbcType=VARCHAR},</if>
            <if test="childException!=null">child_exception = #{childException,jdbcType=VARCHAR},</if>
            <if test="childCode!=null">child_code = #{childCode,jdbcType=VARCHAR}</if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <delete id="deleteById">
        delete from he_script_info where id = #{id}
    </delete>
    <select id="selectById" resultType="com.newland.detb.exceptionmanage.entity.ScriptInfo">
        select
            id,
            parent_exception,
            parent_code,
            child_exception,
            child_code,
            deleted,
            create_time
        from he_script_info where id = #{id}
    </select>
    <select id="selectAll" resultType="com.newland.detb.exceptionmanage.entity.ScriptInfo">
        select
            id,
            parent_exception,
            parent_code,
            child_exception,
            child_code,
            deleted,
            create_time
        from he_script_info
        <where>
            <if test="parentException != null and parentException != ''">
                parent_exception like concat('%',#{parentException},'%')
            </if>
            <if test="childException != null and childException != ''">
                and child_exception like concat('%',#{childException},'%')
            </if>
        </where>
        order by id desc
    </select>
    <select id="selectByChildException" resultType="com.newland.detb.exceptionmanage.entity.ScriptInfo">
        select
            id,
            parent_exception,
            parent_code,
            child_exception,
            child_code,
            deleted,
            create_time
        from he_script_info
        where child_exception = #{childException}
    </select>
</mapper>
