package com.newland.detb.exceptionmanage.async;

import com.newland.detb.common.entity.HeExceptionDataBean;
import com.newland.detb.exceptionmanage.entity.BusinessType;
import com.newland.detb.exceptionmanage.entity.MessageConfig;
import com.newland.detb.exceptionmanage.entity.MessageInfo;
import com.newland.detb.exceptionmanage.mapper.BusinessExceptionOrderMapper;
import com.newland.detb.exceptionmanage.service.MessageConfigService;
import com.newland.detb.exceptionmanage.service.MessageInfoService;
import com.newland.detb.exceptionmanage.util.SnowFlakeGeneratorId;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @date 2023-11-06 006 18:20:33
 * @description 业务异常单处理逻辑
 */
@Slf4j
@Component
public class AsyncBusinessExceptionTask extends BaseAsyncTask{

    @Resource
    private BusinessExceptionOrderMapper businessExceptionOrderMapper;

    //雪花生成id
    @Resource
    private SnowFlakeGeneratorId snowFlakeGeneratorId;

    @Lazy
    @Resource
    private AsyncSecondaryConfirmTask asyncSecondaryConfirmTask;

    @Resource
    private MessageConfigService messageConfigService;

    @Resource
    private MessageInfoService messageInfoService;

    @Async("taskExecutor")
    public void executeAsync(HeExceptionDataBean bean, BusinessType businessType) {
        //获取唯一id
        String snowflakeId = String.valueOf(snowFlakeGeneratorId.snowflakeId());
        asyncPrintLogInfo(snowflakeId,1,1,"业务异常单匹配成功-开始处理------------------------------");
        //根据businessType进行判断
        if (businessType.getParentName().equals("反向工单") && businessType.getChildName().equals("【EBOSS云业务】反向工单失败量")) {
            asyncPrintLogInfo(snowflakeId,1,2,"业务异常单[反向工单]-正在处理的异常场景匹配操作的工单id：" + bean.getDataId());
            //调用巡检存储过程
            Map<String, Object> paramsMap = new ConcurrentHashMap<>();
            //反向工单就传：bb_operation
            paramsMap.put("p_order_table", "bb_operation");
            paramsMap.put("p_warning_id", String.valueOf(bean.getDataId()));
            businessExceptionOrderMapper.invokeInspection(paramsMap);
            //执行完成后，获取p_exec_msg结果
            String execMsg = (String) paramsMap.get("p_exec_msg");
            //判断结果：p_exec_msg 巡检是否成功标记|当前巡检异常订单数
            Map<String, Object> resultMap = procedureResultCheck(execMsg,snowflakeId, bean.getDataId());
            boolean checkResult = false;
            try {
                checkResult = (Boolean) resultMap.get("flag") && (Integer.parseInt((String) resultMap.get("count"))  > 0);
                asyncPrintLogInfo(snowflakeId,1,2,"业务异常单-[异常匹配模块]调用巡检存储过程[成功]，状态码：" + resultMap.get("flag") + ",巡检异常订单数：" + Integer.parseInt((String) resultMap.get("count")));
            } catch (NumberFormatException e) {
                e.printStackTrace();
                checkResult = false;
                asyncPrintErrorLogInfo(snowflakeId,1,2,"业务异常单-[异常匹配模块]调用巡检存储过程[失败]，状态码：" + resultMap.get("flag") + ",巡检异常订单数：" + Integer.parseInt((String) resultMap.get("count")) + ";异常原因：" + e.getMessage());
            }
            //继续执行后续流程
            getWorkOrderService().updateTraceIdByDataId(snowflakeId, bean.getDataId());
            asyncPrintLogInfo(snowflakeId,1,2,"业务异常单-正在处理的异常场景匹配操作的工单id：" + bean.getDataId());
            updateHeExceptionDataWithRemark(bean,1,"业务异常单-异常匹配模块开始处理");
            updateProgress(bean,1,2,"success","业务异常单-异常场景匹配成功");
            //进行二次确认操作（跳过执行，但需要修改对应的进度表状态）
            asyncSecondaryConfirmTask.executeBusinessAsync(snowflakeId,bean,businessType);
        }
//        else if (businessType.getParentName().equals("反向工单") && businessType.getChildName().equals("【EBOSS云业务】反向工单失败量")) {
//            asyncPrintLogInfo(snowflakeId,1,2,"业务异常单[反向工单]-正在处理的异常场景匹配操作的工单id：" + bean.getDataId());
//            //调用巡检存储过程
//            Map<String, Object> paramsMap = new ConcurrentHashMap<>();
//            //反向工单就传：bb_operation
//            paramsMap.put("p_order_table", "bb_operation");
//            paramsMap.put("p_warning_id", String.valueOf(bean.getDataId()));
//            businessExceptionOrderMapper.invokeInspection(paramsMap);
//            //执行完成后，获取p_exec_msg结果
//            String execMsg = (String) paramsMap.get("p_exec_msg");
//            //判断结果：p_exec_msg 巡检是否成功标记|当前巡检异常订单数
//            Map<String, Object> resultMap = procedureResultCheck(execMsg,snowflakeId, bean.getDataId());
//            boolean checkResult = false;
//            try {
//                checkResult = (Boolean) resultMap.get("flag") && (Integer.parseInt((String) resultMap.get("count"))  > 0);
//                asyncPrintLogInfo(snowflakeId,1,2,"业务异常单-[异常匹配模块]调用巡检存储过程[成功]，状态码：" + resultMap.get("flag") + ",巡检异常订单数：" + Integer.parseInt((String) resultMap.get("count")));
//            } catch (NumberFormatException e) {
//                e.printStackTrace();
//                checkResult = false;
//                asyncPrintErrorLogInfo(snowflakeId,1,2,"业务异常单-[异常匹配模块]调用巡检存储过程[失败]，状态码：" + resultMap.get("flag") + ",巡检异常订单数：" + Integer.parseInt((String) resultMap.get("count")) + ";异常原因：" + e.getMessage());
//            }
//            //继续执行后续流程
//            getWorkOrderService().updateTraceIdByDataId(snowflakeId, bean.getDataId());
//            asyncPrintLogInfo(snowflakeId,1,2,"业务异常单-正在处理的异常场景匹配操作的工单id：" + bean.getDataId());
//            updateHeExceptionDataWithRemark(bean,1,"业务异常单-异常匹配模块开始处理");
//            updateProgress(bean,1,2,"success","业务异常单-异常场景匹配成功");
//            //进行二次确认操作（跳过执行，但需要修改对应的进度表状态）
//            asyncSecondaryConfirmTask.executeBusinessAsync(snowflakeId,bean,businessType);
//        }
        else {
            asyncPrintLogInfo(snowflakeId,1,2,"业务异常单["+ businessType.getParentName() +"]-正在处理的异常场景匹配操作的工单id：" + bean.getDataId());
            getWorkOrderService().updateTraceIdByDataId(snowflakeId, bean.getDataId());
            asyncPrintLogInfo(snowflakeId,1,2,"业务异常单-正在处理的异常场景匹配操作的工单id：" + bean.getDataId());
            updateHeExceptionDataWithRemark(bean,1,"业务异常单-异常匹配模块开始处理");
            updateProgress(bean,1,2,"success","业务异常单-异常场景匹配成功");
            //进行二次确认操作（跳过执行，但需要修改对应的进度表状态）
            asyncSecondaryConfirmTask.executeBusinessAsync(snowflakeId,bean,businessType);
//            updateHeExceptionDataWithRemark(bean,5,"业务异常单-分配异常场景处理器失败，请检查业务类型是否正确");
//            updateProgress(bean,5,1,"success","异常治理流程结束");
        }
    }

    /**
     * 执行存储过程后，对结果进行校验，判断是否需要继续执行后续流程
     * @param msg
     * @param traceId 唯一id
     * @return
     */
    private Map<String, Object> procedureResultCheck(String msg,String traceId,Long dataId) {
        Map<String, Object> parametersMap = new ConcurrentHashMap<>();
        // 解析 p_exec_msg，以获取执行是否成功(flag为0是正常，99为异常) 0|7
        boolean flag = false;
        String countFlag = "";
        if (msg != null && msg.matches("\\d+\\|\\d+")) {
            String[] parts = msg.split("\\|");
            String successFlag = parts[0];
            countFlag = parts[1];
            asyncPrintLogInfo("异常序号：" + dataId + "," + traceId + "业务异常单-[巡检存储过程]调用返回状态码：" + successFlag + "|异常订单数：" + countFlag);
            // asyncPrintLogInfo("业务异常单-[巡检存储过程]调用返回异常订单数：" + countFlag);
            // 如果巡检成功标记是 "0"，则将 inspectionSuccess 设置为 true
            flag = "0".equals(successFlag);
        }
        parametersMap.put("flag", flag);
        parametersMap.put("count", countFlag);
        return parametersMap;
    }

    /**
     * 业务异常单-拼接生成内部messageInfo对象
     * @param heExceptionDataBean
     * @param innerMessage
     * @return
     */
    private void spliceInnerMessageInfo(HeExceptionDataBean heExceptionDataBean, String innerMessage,String traceId) {
        asyncPrintLogInfo(traceId,1,2,"业务异常单巡检模块过程调用完成:" + innerMessage);
        //查询短信配置表中的需要发送的内部负责人信息
        List<MessageConfig> messageConfigs = messageConfigService.selectInnerList();
        if (messageConfigs == null) {
            return;
        }
        for (MessageConfig messageConfig : messageConfigs) {
            //拼接内部短信信息
            MessageInfo messageInfo = new MessageInfo();
            messageInfo.setDataId(heExceptionDataBean.getDataId());
            messageInfo.setName(messageConfig.getName());
            messageInfo.setPhone(messageConfig.getPhone());
            messageInfo.setEmail(messageConfig.getEmail());
            messageInfo.setGovernLevel("1".equals(heExceptionDataBean.getAlarmLevel()) ? "严重告警" : "重要告警");
            messageInfo.setMessage("异常告警id：" + heExceptionDataBean.getDataId() + ",环境：" + (Objects.equals(heExceptionDataBean.getEnvironment(), "0") ? "云环境" : "非云环境") + ",告警关键字：" + heExceptionDataBean.getAlarmType() + ",业务异常单恢复结束治理：" + innerMessage + "【异常治理工具消息同步】");
            messageInfo.setIsInner(1);
            //保存
            int res = messageInfoService.insertInner(messageInfo);
            asyncPrintLogInfo(traceId,1,2,"业务异常单-巡检模块未发异常单，拼接发送内部短信:" + innerMessage + (res == 1 ? "[短信保存成功]" : "[短信保存失败]"));
        }
    }
}
