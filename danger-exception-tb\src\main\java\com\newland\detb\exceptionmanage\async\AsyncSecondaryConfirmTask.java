package com.newland.detb.exceptionmanage.async;

import com.newland.detb.common.entity.HeExceptionDataBean;
import com.newland.detb.exceptionmanage.entity.*;
import com.newland.detb.exceptionmanage.mapper.BusinessExceptionOrderMapper;
import com.newland.detb.exceptionmanage.mapper.ExceptionProgressMapper;
import com.newland.detb.exceptionmanage.service.DictInfoService;
import com.newland.detb.exceptionmanage.service.MessageConfigService;
import com.newland.detb.exceptionmanage.service.MessageInfoService;
import com.newland.detb.exceptionmanage.service.ScriptInfoService;
import com.newland.detb.exceptionmanage.util.ExecuteScriptUtil;
import com.newland.detb.exceptionmanage.util.ProcessUtil;
import com.newland.detb.quartz.mapper.HeExceptionDataMapper;
import com.newland.detb.util.JschScriptUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @date 2023-04-25 025 11:33:56
 * @description 异常二次确认异步任务
 */
// @Lazy
@Slf4j
@Component
public class AsyncSecondaryConfirmTask extends BaseAsyncTask{

    @Resource
    private ExecuteScriptUtil executeScriptUtil;

    @Resource
    private JschScriptUtil jschScriptUtil;

    @Resource
    private ScriptInfoService scriptInfoService;

    @Lazy
    @Resource
    private AsyncQueryGovernanceStrategyTask asyncQueryGovernanceStrategyTask;

    @Resource
    private DictInfoService dictInfoService;

    @Resource
    private BusinessExceptionOrderMapper businessExceptionOrderMapper;

    @Resource
    private MessageConfigService messageConfigService;

    @Resource
    private MessageInfoService messageInfoService;

    /**
     * 执行异常二次确认操作
     * @param heExceptionDataBean 工单表数据
     * @param exceptionScenario 异常场景库数据
     */
    @Async("taskExecutor")
    @Override
    public void executeAsync(String traceId,HeExceptionDataBean heExceptionDataBean, ExceptionScenario exceptionScenario) {
        asyncPrintLogInfo(traceId,"异常二次确认操作");
        updateProgress(heExceptionDataBean,1,3,"doing","正在执行异常二次确认操作");
        //执行异常二次确认操作，调用工具类，去执行脚本
        //先查询脚本配置信息
        ScriptInfo scriptInfo = scriptInfoService.selectByChildException(heExceptionDataBean.getAlarmType());
        if (scriptInfo != null) {
            asyncPrintLogInfo(traceId,1,3,"查询异常二次确认脚本名称：" + scriptInfo);
            //根据告警类型名称查询字典表
            DictInfo dictInfo = dictInfoService.selectDictInfo(heExceptionDataBean.getAlarmType());
            if (dictInfo != null) {
                //xx.sh 应用名 type 环境 机器
                boolean flag = false;
                // flag = jschScriptUtil.executeSCScript(command);
                //改为本地调用
                // flag = ProcessUtil.executeScript(command);
                //针对日志关键字的二次确认脚本需要另外执行 Objects.equals(heExceptionDataBean.getAlarmType(),"日志关键字")
                if (isIndependentScene(heExceptionDataBean.getAlarmType())) {
                    flag = invokeLogKeyWordsScript(heExceptionDataBean,scriptInfo,dictInfo,traceId);
                    // flag = false;
                }else {
                    flag = invokeGeneralScript(heExceptionDataBean, scriptInfo, dictInfo,traceId);
                }
                asyncPrintLogInfo(traceId,1,3,"二次确认结果获取成功：" + flag);
                if (flag) {
                    //异常二次确认结果为真，继续进行后续治理流程
                    updateHeExceptionDataWithRemark(heExceptionDataBean,1,"异常二次确认成功，结果:继续治理");
                    updateProgress(heExceptionDataBean,1,3,"success","异常二次确认成功，结果:继续治理");

                    //开始查询异常治理策略
                    asyncPrintLogInfo(traceId,1,3,"异常二次确认结束，等待查询异常治理策略");

                    updateHeExceptionDataWithRemark(heExceptionDataBean,1,"异常场景确认成功");
                    updateProgress(heExceptionDataBean,1,4,"success","异常场景确认成功");
                    //查询治理策略异步任务
                    asyncQueryGovernanceStrategyTask.executeAsync(traceId,heExceptionDataBean,exceptionScenario);
                }else {
                    asyncPrintLogInfo(traceId,1,3,"异常二次确认结束，不需要后续治理，异常治理结束");
                    //异常确认结果为false，异常治理流程结果(status=5)
                    updateHeExceptionDataWithRemark(heExceptionDataBean,5,"异常二次确认结束，无需后续治理，异常治理结束");
                    updateProgress(heExceptionDataBean,1,3,"success","异常二次确认成功，结果:结束治理");
                    updateProgress(heExceptionDataBean,5,1,"success","异常二次确认成功，无需后续治理");
                    updateWorkOrderWhenFailCase(heExceptionDataBean.getDataId(),"异常二次确认成功，无需后续治理");
                }
            }else {
                asyncPrintErrorLogInfo(traceId,1,3,"字典表查询不到对应名称，异常治理结束");
                //异常确认结果为false，异常治理流程结果(status=5)
                updateHeExceptionDataWithRemark(heExceptionDataBean,5,"异常二次确认结束，字典表查询不到对应名称，异常治理结束");
                updateProgress(heExceptionDataBean,1,3,"fail","字典表查询不到对应名称");
                updateProgress(heExceptionDataBean,5,1,"fail","异常二次无法确认，字典表查询不到对应名称");
                updateWorkOrderWhenFailCase(heExceptionDataBean.getDataId(),"字典表查询不到对应名称，异常治理结束");
            }
        }else {
            asyncPrintLogInfo(traceId,1,3,"查询异常二次确认脚本名称为空");
            updateHeExceptionDataWithRemark(heExceptionDataBean,5,"异常二次确认结束，查询异常二次确认脚本名称为空");
            updateProgress(heExceptionDataBean,1,3,"fail","查询异常二次确认脚本名称为空");
            updateProgress(heExceptionDataBean,5,1,"fail","查询异常二次确认脚本名称为空");
        }
    }

    /**
     * 进行业务异常单处理
     * @param traceId
     * @param heExceptionDataBean
     */
    @Async("taskExecutor")
    public void executeBusinessAsync(String traceId,HeExceptionDataBean heExceptionDataBean,BusinessType businessType) {
        asyncPrintLogInfo(traceId,"业务异常单-异常二次确认操作");
        if (businessType.getParentName().equals("反向工单") && businessType.getChildName().equals("【EBOSS云业务】反向工单失败量")) {
            //二次确认模块进行分析存储过程的调用，用于判断是否需要继续处理后续流程
            Map<String, Object> paramsMap = new ConcurrentHashMap<>();
            //反向工单就传：bb_operation
            paramsMap.put("p_warning_id", String.valueOf(heExceptionDataBean.getDataId()));
            businessExceptionOrderMapper.invokeAnalyze(paramsMap);
            //执行完成后，获取p_exec_msg结果
            String execMsg = (String) paramsMap.get("p_exec_msg");
            //判断结果：p_exec_msg 巡检是否成功标记|当前巡检异常订单数
            Map<String, Object> resultMap = procedureResultCheck(execMsg,traceId, heExceptionDataBean.getDataId());
            boolean checkResult = false;
            try {
                checkResult = (Boolean) resultMap.get("flag") && (Integer.parseInt((String) resultMap.get("count"))  > 0);
                asyncPrintLogInfo(traceId,1,2,"业务异常单-[二次确认模块]调用分析存储过程[成功]，状态码：" + resultMap.get("flag") + ",分析异常订单数：" + Integer.parseInt((String) resultMap.get("count")));
            } catch (NumberFormatException e) {
                e.printStackTrace();
                checkResult = false;
                asyncPrintErrorLogInfo(traceId,1,2,"业务异常单-[二次确认模块]调用分析存储过程[失败]，状态码：" + resultMap.get("flag") + ",分析异常订单数：" + Integer.parseInt((String) resultMap.get("count")) + ";异常原因：" + e.getMessage());
            }
            if (checkResult) {
                //继续执行后续流程
                //更改二次确认进度表中状态
                updateHeExceptionDataWithRemark(heExceptionDataBean,1,"业务异常单-正在执行二次确认");
                updateProgress(heExceptionDataBean,1,3,"success","业务异常单-二次确认完成，继续治理");

                //开始查询异常治理策略
                asyncPrintLogInfo(traceId,1,3,"业务异常单-二次确认完成，继续治理");

                updateHeExceptionDataWithRemark(heExceptionDataBean,1,"业务异常单-场景确认成功");
                updateProgress(heExceptionDataBean,1,4,"success","业务异常单-场景确认成功");
                //查询治理策略异步任务
                asyncQueryGovernanceStrategyTask.executeBusinessAsync(traceId,heExceptionDataBean,businessType);
            }else {
                //终止执行后续流程，对后续流程进度表进行更新
                asyncPrintLogInfo(traceId,1,3,"业务异常单-未发现待治理异常单，结束治理");
                //异常确认结果为false，异常治理流程结果(status=5)
                updateHeExceptionDataWithRemark(heExceptionDataBean,5,"业务异常单-未发现待治理异常单，异常治理结束");
                updateProgress(heExceptionDataBean,1,3,"success","业务异常单-未发现待治理异常单，异常治理结束");
                updateProgress(heExceptionDataBean,5,1,"success","业务异常单-未发现待治理异常单，异常治理结束");
                updateWorkOrderWhenFailCase(heExceptionDataBean.getDataId(),"业务异常单-未发现待治理异常单，异常治理结束");
                spliceInnerMessageInfo(heExceptionDataBean,"分析后未发现待恢复异常单",traceId);
            }
        }else {
            //更改二次确认进度表中状态
            updateHeExceptionDataWithRemark(heExceptionDataBean,1,"业务异常单-正在执行二次确认");
            updateProgress(heExceptionDataBean,1,3,"success","业务异常单-二次确认完成，继续治理");

            //开始查询异常治理策略
            asyncPrintLogInfo(traceId,1,3,"业务异常单-二次确认完成，继续治理");

            updateHeExceptionDataWithRemark(heExceptionDataBean,1,"业务异常单-场景确认成功");
            updateProgress(heExceptionDataBean,1,4,"success","业务异常单-场景确认成功");
            //查询治理策略异步任务
            asyncQueryGovernanceStrategyTask.executeBusinessAsync(traceId,heExceptionDataBean,businessType);
        }
    }

    /**
     * 执行存储过程后，对结果进行校验，判断是否需要继续执行后续流程
     * @param msg
     * @return
     */
    private Map<String, Object> procedureResultCheck(String msg,String traceId,Long dataId) {
        Map<String, Object> parametersMap = new ConcurrentHashMap<>();
        // 解析 p_exec_msg，以获取执行是否成功(flag为0是正常，99为异常) 0|7
        boolean flag = false;
        String countFlag = "";
        if (msg != null && msg.matches("\\d+\\|\\d+")) {
            String[] parts = msg.split("\\|");
            String successFlag = parts[0];
            countFlag = parts[1];
            asyncPrintLogInfo("异常序号：" + dataId + "," + traceId + "业务异常单-[分析存储过程]调用返回状态码：" + successFlag + "|异常订单数：" + countFlag);
            // asyncPrintLogInfo("业务异常单-[巡检存储过程]调用返回异常订单数：" + countFlag);
            // 如果巡检成功标记是 "0"，则将 inspectionSuccess 设置为 true
            flag = "0".equals(successFlag);
        }
        parametersMap.put("flag", flag);
        parametersMap.put("count", countFlag);
        return parametersMap;
    }

    /**
     * 业务异常单-拼接生成内部messageInfo对象
     * @param heExceptionDataBean
     * @param innerMessage
     * @return
     */
    private void spliceInnerMessageInfo(HeExceptionDataBean heExceptionDataBean, String innerMessage,String traceId) {
        asyncPrintLogInfo(traceId,1,2,"业务异常单分析模块调用完成:" + innerMessage);
        //查询短信配置表中的需要发送的内部负责人信息
        List<MessageConfig> messageConfigs = messageConfigService.selectInnerList();
        if (messageConfigs == null) {
            return;
        }
        for (MessageConfig messageConfig : messageConfigs) {
            //拼接内部短信信息
            MessageInfo messageInfo = new MessageInfo();
            messageInfo.setDataId(heExceptionDataBean.getDataId());
            messageInfo.setName(messageConfig.getName());
            messageInfo.setPhone(messageConfig.getPhone());
            messageInfo.setEmail(messageConfig.getEmail());
            messageInfo.setGovernLevel("1".equals(heExceptionDataBean.getAlarmLevel()) ? "严重告警" : "重要告警");
            messageInfo.setMessage("异常告警id：" + heExceptionDataBean.getDataId() + ",环境：" + (Objects.equals(heExceptionDataBean.getEnvironment(), "0") ? "云环境" : "非云环境") + ",告警关键字：" + heExceptionDataBean.getAlarmType() + ",业务异常单恢复结束治理：" + innerMessage + "【异常治理工具消息同步】");
            messageInfo.setIsInner(1);
            //保存
            int res = messageInfoService.insertInner(messageInfo);
            asyncPrintLogInfo(traceId,1,2,"业务异常单-分析模块未发异常单，拼接发送内部短信:" + innerMessage + (res == 1 ? "[短信保存成功]" : "[短信保存失败]"));
        }
    }

    /**
     * 执行通用场景二次脚本
     * @return
     */
    private Boolean invokeGeneralScript(HeExceptionDataBean bean,ScriptInfo scriptInfo,DictInfo dictInfo,String traceId) {
        //使用String数组的形式作为参数
        String command = scriptInfo.getChildCode() + " " + bean.getHomeModule() + " " + dictInfo.getValue() + " " + bean.getEnvironment() + " " + (bean.getHostName() == null ? "" : bean.getHostName());
        asyncPrintLogInfo(traceId,1,3,"[" + traceId + "]-执行通用异常二次确认脚本命令：" + command);
        return getProcessUtil().executeConfirmScript(command,traceId);
    }

    /**
     * 通过告警关键字判断是否是独立生成二次确认脚本的场景
     * 日志关键字、PULSAR业务、目录告警（目录积压）、activemq消息队列
     * @param alarmTypeName
     * @return
     */
    private Boolean isIndependentScene(String alarmTypeName) {
        return Objects.equals(alarmTypeName, "日志关键字") || Objects.equals(alarmTypeName, "PULSAR业务") || Objects.equals(alarmTypeName, "目录告警") || Objects.equals(alarmTypeName, "activemq消息队列");
    }

    /**
     * 执行日志关键字二次确认脚本
     * @param bean
     * @param scriptInfo
     * @param dictInfo
     * @param traceId
     * @return
     */
    private Boolean invokeLogKeyWordsScript(HeExceptionDataBean bean,ScriptInfo scriptInfo,DictInfo dictInfo,String traceId) {
        //使用String数组的形式作为参数
        //日志关键字二次确认脚本返回的值为0|1，其中0为失败，1为成功；脚本调用参数只有一个为子节点名称
        String command = scriptInfo.getChildCode() + " " + "\"" + bean.getChildProcessName() + "\"";
        asyncPrintLogInfo(traceId,1,3,"[" + traceId + "]-执行[" + bean.getAlarmType() + "]异常二次确认脚本命令：" + command);
        return getProcessUtil().executeLogKeyWordsConfirmScript(command,traceId,bean.getAlarmType());
    }
}
