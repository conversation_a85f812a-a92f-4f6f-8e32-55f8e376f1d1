server:
  port: 9088

# 配置文件加密
jasypt:
  encryptor:
    password: RnR1y5oBsxXX0kWf3k6dJc3LQ1isuDrw

# mybatis配置
mybatis:
  mapper-locations: classpath:mapper/*/*.xml
  configuration:
    map-underscore-to-camel-case: true
#    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

spring:
  profiles:
    active: dev

logging:
  config: classpath:logback-spring.xml
  level:
    # 设置Mapper接口所在包的日志级别为DEBUG，会打印SQL语句
    com.newland.detb: debug