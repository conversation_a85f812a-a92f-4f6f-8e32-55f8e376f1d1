package com.newland.detb.util;

import com.newland.detb.lock.DatabaseLock;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2023-12-14 014 1:00:58
 * @description 测试加锁方法类
 */
@Slf4j
@Component
public class TestLock {

    @Resource
    private DatabaseLock databaseLock;

    /**
     * 打印信息
     * @param name
     */
    public void printInfo(String name) {
        try {
            databaseLock.lock("测试","testPrint");
            TimeUnit.SECONDS.sleep(5);
            log.info("[正在执行的任务名称]：{}",name);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("打印方法出错：{}",e.getMessage());
        } finally {
            databaseLock.unlock("测试","testPrint");
        }
    }
}
