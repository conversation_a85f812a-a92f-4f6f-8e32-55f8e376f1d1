package com.newland.detb.troubleshooting.controller;

import cn.hutool.json.JSONObject;
import com.alibaba.fastjson.JSONArray;
import com.newland.detb.common.Result;
import com.newland.detb.exceptionmanage.entity.WorkOrder;
import com.newland.detb.exceptionmanage.mapper.DynamicDataSourceTestMapper;
import com.newland.detb.troubleshooting.component.MyWebSocketHandler;
import com.newland.detb.troubleshooting.component.WebSocketServer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.socket.WebSocketSession;

import javax.annotation.Resource;
import javax.websocket.Session;
import java.io.*;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Random;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.ReentrantLock;

/**
 * <AUTHOR>
 * @date 2023-02-22 022 14:43:39
 * @description 测试controller
 */
@Slf4j
@RestController
@RequestMapping("/test")
public class TestController {

    @Resource
    private DynamicDataSourceTestMapper dynamicDataSourceTestMapper;

    @Autowired
    private RestTemplate restTemplate;

    // @Resource
    private WebSocketServer webSocketServer;

    @Resource
    private MyWebSocketHandler myWebSocketHandler;

    private static int counter = 0;


    // @RequestMapping("dynamic")
    public Result testDynamic() {
        List<WorkOrder> workOrderList = dynamicDataSourceTestMapper.getWorkOrderList();
        return Result.success(workOrderList);
    }

    @RequestMapping("index")
    public Result testIndex() throws InterruptedException {
        ReentrantLock lock = new ReentrantLock();
        lock.lock();
        TimeUnit.SECONDS.sleep(1);
        lock.unlock();
        return Result.success("访问成功" + counter++);
    }

    /**
     * 测试websocket
     * @return
     * @throws InterruptedException
     */
    // @RequestMapping("add")
    public Result testAdd(@RequestParam(value = "fingerId",defaultValue = "")String fingerId) throws InterruptedException {
        ReentrantLock lock = new ReentrantLock();
        lock.lock();
        // Session session = WebSocketServer.sessionMap.get("kafkaData");
        // WebSocketSession session = myWebSocketHandler.getSession(fingerId);
        // System.out.println("session:" + session);
        //随机时间
        long shortTime = 0;
        long longTime = 0;
        for (int i = 0; i < 10; i++) {
            TimeUnit.SECONDS.sleep(1);
            JSONObject jsonObject3 = new JSONObject();
            jsonObject3.set("type","bgocCount");
            jsonObject3.set("message",i);
            // myWebSocketHandler.sendMessage(jsonObject3.toString(),session);
            // myWebSocketHandler.sendAllMessage(jsonObject3.toString());

            JSONObject jsonObject4 = new JSONObject();
            jsonObject4.set("type","cgocCount");
            jsonObject4.set("message",i);
            // myWebSocketHandler.sendMessage(jsonObject4.toString(),session);

            String message = "kafka-2收到C-GOC侧消息,当前日期:2023-07-10---{\"resourceId\":\"463f076173ed413fa3f473db66537223\",\"kpiValue\":\"0\",\"modelId\":\"host_fs\",\"module\":\"SYSM_PM\",\"kpiId\":\"host_fs_inode_free\",\"kpiName\":\"主机文件系\n" +
                    "统I-Node空闲数\",\"mainModelId\":\"pcserver\",\"resourceName\":\"cm-kvm-batch3-35:/lilulu\",\"dn\":\"e75cfec6bb784a3c82a8378e8c73e65f.host_fs./lilulu\",\"belongToSystem\":\"network_wide_customer_center\",\"ip_address\":\"**************\",\"modelName\":\"主机文件系统\",\"mainModelName\":\"PC服务器\",\"tenantId\":\"ityj\",\"s\n" +
                    "tartTime\":\"2023-07-07 15:28:00\",\"endTime\":\"2023-07-07 15:28:00\",\"dimension\":{},\"key\":\"\"}" + i;
            JSONObject jsonObject = new JSONObject();
            jsonObject.set("type","message");
            jsonObject.set("message",message);
            // myWebSocketHandler.sendMessage(jsonObject.toString(),session);


            // TimeUnit.SECONDS.sleep(1);
            long time = Duration.between(generateRandomDateTime(), LocalDateTime.now()).toMillis();
            shortTime = shortTime == 0 ? time : Math.min(time,shortTime);
            longTime = Math.max(longTime, time);
            JSONObject jsonObject1 = new JSONObject();
            jsonObject1.set("type","miniTime");
            jsonObject1.set("message",shortTime);
            JSONObject jsonObject2 = new JSONObject();
            jsonObject2.set("type","maxTime");
            jsonObject2.set("message",longTime);
            // myWebSocketHandler.sendMessage(jsonObject1.toString(),session);
            // myWebSocketHandler.sendMessage(jsonObject2.toString(),session);

            if (myWebSocketHandler.getOnlineSum() > 0) {
                log.info("当前在线人数：{} ，发送动态采集过程",myWebSocketHandler.getOnlineSum());
                myWebSocketHandler.sendAllMessage(jsonObject.toString());
                myWebSocketHandler.sendAllMessage(jsonObject1.toString());
                myWebSocketHandler.sendAllMessage(jsonObject2.toString());
                myWebSocketHandler.sendAllMessage(jsonObject3.toString());
                myWebSocketHandler.sendAllMessage(jsonObject4.toString());
            }
        }
        lock.unlock();
        return Result.success("访问成功" + counter++);
    }

    /**
     * 随机生成一个LocalDateTime时间
     * @return
     */
    private static LocalDateTime generateRandomDateTime() {
        Random random = new Random();

        int year = random.nextInt(2023 - 1970 + 1) + 1970; // 1970年到2023年之间的随机年份
        int month = random.nextInt(12) + 1; // 1到12之间的随机月份
        int day = random.nextInt(28) + 1; // 1到28之间的随机日期
        int hour = random.nextInt(24); // 0到23之间的随机小时
        int minute = random.nextInt(60); // 0到59之间的随机分钟
        int second = random.nextInt(60); // 0到59之间的随机秒

        return LocalDateTime.of(year, month, day, hour, minute, second);
    }

    // @RequestMapping(value = "origin",method = RequestMethod.GET)
    public List<Object> getOrigin() {
        String getUrl = "http://192.168.10.102:8089/blog/selectAll";
        // Map<String, String> map = new HashMap<>();
        // map.put("name","");
        // String param = JSON.toJSONString(map);
        // //创建restTemplate
        RestTemplate restTemplate = new RestTemplate();
        // //设置restTemplate请求头和请求体参数
        // HttpHeaders headers = new HttpHeaders();
        // MediaType mediaType = MediaType.parseMediaType("application/json;charset=UTF-8");
        // headers.setContentType(mediaType);
        // headers.add("Accept","application/json");
        // HttpEntity<String> entity = new HttpEntity<>(param,headers);
        //使用restTemplate发起请求和接收返回值
        String resultData = restTemplate.getForObject(getUrl, String.class);
        log.info("从服务端返回的结果：{}",resultData);
        List<Object> list = JSONArray.parseArray(resultData, Object.class);

        return list;
    }

    // @RequestMapping(value = "gethttp",method = RequestMethod.GET)
    public String getHttp() {
        String getUrl = "http://47.93.247.196:8089/blog/selectAll";
        // RestTemplate restTemplate = new RestTemplate();
        ResponseEntity<List> entity = restTemplate.getForEntity(getUrl, List.class);
        //200 ok
        System.out.println(entity.getStatusCode().toString());
        //200
        System.out.println(entity.getStatusCode().value());
        return "请求成功";
    }

    // @RequestMapping(value = "hello",method = RequestMethod.GET)
    public Result hello() throws InterruptedException {

        return Result.success("欢迎访问本系统");
    }

    // @RequestMapping("main")
    public Result testMain() throws FileNotFoundException {
        return Result.success();
    }

    /**
     * 使用Runtime执行linux命令
     * @throws IOException
     * @throws InterruptedException
     */
    public void testRuntime() throws IOException, InterruptedException {
        String command = "";
        Process process = Runtime.getRuntime().exec(command);
        //获取命令行执行的结果
        InputStream inputStream = process.getInputStream();
        BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(inputStream));
        String line;
        while ((line = bufferedReader.readLine()) != null) {
            System.out.println(line);
        }

        //获取命令执行结果状态
        int waitFor = process.waitFor();
        if (waitFor == 0) {
            System.out.println("成功");
        }else {
            System.out.println("失败");
        }

    }
}
