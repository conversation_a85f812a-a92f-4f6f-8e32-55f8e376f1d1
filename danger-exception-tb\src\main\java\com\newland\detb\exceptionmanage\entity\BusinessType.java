package com.newland.detb.exceptionmanage.entity;

import java.io.Serializable;
import java.util.Date;

import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 业务异常场景表
 * @TableName HE_BUSINESS_TYPE
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class BusinessType implements Serializable {
    /**
     * id
     */
    private Long id;

    /**
     * 业务异常场景名称
     */
    private String name;

    /**
     * 父节点名称
     */
    private String parentName;

    /**
     * 子节点名称
     */
    private String childName;

    /**
     * 是否需要授权：0|1，默认：0代表需要授权
     */
    private Integer isAuth;

    /**
     * 修复类型，0：存储过程，1：脚本；默认0
     */
    private Integer repairType;

    /**
     * 修复方式：存储过程或者脚本
     * 存储过程：完整的存储过程名称
     * 脚本：完整的脚本路径及名称
     */
    private String repairWay;

    /**
     * 默认0代表未删除，1代表删除
     */
    private Integer deleted;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date createTime;

    private static final long serialVersionUID = 1L;
}