package com.newland.detb.troubleshooting.component;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.websocket.OnClose;
import javax.websocket.OnMessage;
import javax.websocket.OnOpen;
import javax.websocket.Session;
import javax.websocket.server.PathParam;
import javax.websocket.server.ServerEndpoint;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @date 2023-07-07 007 15:58:00
 * @description websocket服务(双通道连接)
 */
@Slf4j
// @ServerEndpoint(value = "/scrolling/{address}")
// @Component
public class WebSocketServer {

    //记录当前在线的连接数
    public static final Map<String, Session> sessionMap = new ConcurrentHashMap<>();

    /**
     * 连接建立成功时调用的方法
     * @param session
     * @param address
     */
    @OnOpen
    public void onOpen(Session session, @PathParam("address") String address) {
        sessionMap.put(address,session);
        log.info("有新地址加入：{}，当前所有在线地址：{} 个",address,sessionMap.size());

        // JSONObject result = new JSONObject();
        // JSONArray array = new JSONArray();
        // result.set("users",array);
        // for (Object key : sessionMap.keySet()) {
        //     JSONObject jsonObject = new JSONObject();
        //     jsonObject.set("username",key);
        //     array.add(jsonObject);
        // }
        //最后组装成的格式：   {"users":[{"username":"lihua"},{"username":"admin"}]}
        //后台给所有的客户端发送消息
        sendAllMessage(JSONUtil.toJsonStr(""));
    }

    /**
     * 后台收到客户端发送过来的消息
     * onMessage是一个消息的中转站
     * @param message
     * @param session
     * @param address
     */
    @OnMessage
    public void onMessage(String message,Session session,@PathParam("address") String address) {
        log.info("服务端收到地址：{} 的消息：{}",address,message);
        JSONObject obj = JSONUtil.parseObj(message);
        //to 表示发送给哪个用户，比如某某某发送给admin用户
        String toUsername = obj.getStr("to");
        //text 表示发送的消息文本
        String text = obj.getStr("text");
        //根据toUsername来获取session，再通过session发送消息文本
        Session toSession = sessionMap.get(toUsername);
        if (toSession != null) {
            //服务器端重新组装消息，组装之后的消息包含发送人和发送的文本内容
            JSONObject jsonObject = new JSONObject();
            jsonObject.set("from",address);
            jsonObject.set("text",text);
            this.sendMessage(jsonObject.toString(),toSession);
            log.info("发送给用户 username={}，消息：{}",toUsername,jsonObject.toString());
        }else {
            log.info("发送失败，未找到用户username={} 的session",toUsername);
        }
    }

    /**
     * 服务端发送消息给客户端
     * @param message 发送的消息内容
     * @param toSession 接收者的session
     */
    public void sendMessage(String message, Session toSession) {
        try {
            log.info("服务端给客户端[{}]发送消息{}", toSession.getId(), message);
            toSession.getBasicRemote().sendText(message);
        } catch (Exception e) {
            log.error("服务端发送消息给客户端失败", e);
        }
    }

    /**
     * 服务端给所以客户端发消息
     * @param message 发送的消息内容
     */
    public void sendAllMessage(String message) {
        try {
            for (Session session : sessionMap.values()) {
                log.info("服务端给客户端[{}]发送消息{}", session.getId(), message);
                session.getBasicRemote().sendText(message);
            }
        } catch (Exception e) {
            log.error("服务端发送消息给客户端失败", e);
        }
    }

    /**
     * 连接关闭时调用的方法
     * @param session
     * @param address
     */
    @OnClose
    public void onClosed(Session session,@PathParam("address") String address) {
        sessionMap.remove(address);
        log.info("有一连接关闭，移除地址：{} 的session信息，当前在线地址数为：{}",address,sessionMap.size());
    }

    /**
     * 获取websocket的session
     * @param address
     * @return
     */
    public static Session getSession(String address) {
        return sessionMap.get(address);
    }
}
