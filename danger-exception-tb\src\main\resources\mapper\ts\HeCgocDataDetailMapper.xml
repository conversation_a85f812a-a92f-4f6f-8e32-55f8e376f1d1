<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.newland.detb.troubleshooting.mapper.HeCgocDataDetailMapper">

    <insert id="insertHeCgocDataDetailMapper">
insert into he_cgoc_data_detail (DEV_TYPE, RESOURCE_ID, SUB_RES_NAME, INSTANCE, METRIC_NAME, CI_CODE, BMC_IPV4,
REGION_NAME, BUSINESS_NAME, RESOURCE_POOL_NAME, MGR_IPV4, CLASS_ID, TENANT_NAME, AREA_NAME, METRIC, VENDOR, OS_TYPE,
POD_NAME, BUSI_IPV4, DEVICE_MODEL, TIMESTAMP, VAL)
values
(
                #{devType,jdbcType=VARCHAR},
                #{resourceId,jdbcType=VARCHAR},
                #{subResName,jdbcType=VARCHAR},
                #{instance,jdbcType=VARCHAR},
                #{metricName,jdbcType=VARCHAR},
                #{ciCode,jdbcType=VARCHAR},
                #{bmcIpv4,jdbcType=VARCHAR},
                #{regionName,jdbcType=VARCHAR},
                #{businessName,jdbcType=VARCHAR},
                #{resourcePoolName,jdbcType=VARCHAR},
                #{mgrIpv4,jdbcType=VARCHAR},
                #{classId,jdbcType=VARCHAR},
                #{tenantName,jdbcType=VARCHAR},
                #{areaName,jdbcType=VARCHAR},
                #{metric,jdbcType=VARCHAR},
                #{vendor,jdbcType=VARCHAR},
                #{osType,jdbcType=VARCHAR},
                #{podName,jdbcType=VARCHAR},
                #{busiIpv4,jdbcType=VARCHAR},
                #{deviceModel,jdbcType=VARCHAR},
                #{timestamp,jdbcType=DATE},
                #{val,jdbcType=DOUBLE}
)
    </insert>
    <update id="updateCgocDataDetailMapper">
        UPDATE he_cgoc_data_detail
        SET
            DEV_TYPE = #{cgocDataDetailBean.devType},
            INSTANCE = #{cgocDataDetailBean.instance},
            METRIC_NAME = #{cgocDataDetailBean.metricName},
            CI_CODE = #{cgocDataDetailBean.ciCode},
            BMC_IPV4 = #{cgocDataDetailBean.bmcIpv4},
            REGION_NAME = #{cgocDataDetailBean.regionName},
            BUSINESS_NAME = #{cgocDataDetailBean.businessName},
            RESOURCE_POOL_NAME = #{cgocDataDetailBean.resourcePoolName},
            MGR_IPV4 = #{cgocDataDetailBean.mgrIpv4},
            CLASS_ID = #{cgocDataDetailBean.classId},
            TENANT_NAME = #{cgocDataDetailBean.tenantName},
            AREA_NAME = #{cgocDataDetailBean.areaName},
            VENDOR = #{cgocDataDetailBean.vendor},
            OS_TYPE = #{cgocDataDetailBean.osType},
            POD_NAME = #{cgocDataDetailBean.podName},
            BUSI_IPV4 = #{cgocDataDetailBean.busiIpv4},
            DEVICE_MODEL = #{cgocDataDetailBean.deviceModel},
            TIMESTAMP = #{cgocDataDetailBean.timestamp},
            VAL = #{cgocDataDetailBean.val}
        WHERE
            RESOURCE_ID = #{cgocDataDetailBean.resourceId}
          AND SUB_RES_NAME = #{cgocDataDetailBean.subResName}
          AND METRIC = #{cgocDataDetailBean.metric}
          AND DATE_FORMAT(CREATE_TIME, '%Y%m%d') = #{time}
    </update>
    <update id="truncateCGocTable">
        TRUNCATE TABLE he_cgoc_data_detail
    </update>
    <select id="ifExistsCgocData" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM he_cgoc_data_detail
        WHERE
            resource_id = #{cgocDataDetailBean.resourceId}
          AND sub_res_name = #{cgocDataDetailBean.subResName}
          AND metric = #{cgocDataDetailBean.metric}
          AND DATE_FORMAT(create_time, '%Y%m%d') = #{time}
    </select>
</mapper>