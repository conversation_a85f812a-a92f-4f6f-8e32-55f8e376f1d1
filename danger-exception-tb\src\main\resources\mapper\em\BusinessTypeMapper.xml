<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.newland.detb.exceptionmanage.mapper.BusinessTypeMapper">

    <resultMap id="BaseResultMap" type="com.newland.detb.exceptionmanage.entity.BusinessType">
            <id property="id" column="ID" jdbcType="DECIMAL"/>
            <result property="name" column="NAME" jdbcType="VARCHAR"/>
            <result property="deleted" column="DELETED" jdbcType="DECIMAL"/>
            <result property="createTime" column="CREATE_TIME" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID,NAME,DELETED,
        CREATE_TIME
    </sql>
    <insert id="save">
        insert into he_business_type(name, parent_name, child_name, is_auth, repair_type, repair_way)
        values (#{name,jdbcType=VARCHAR}, #{parentName,jdbcType=VARCHAR}, #{childName,jdbcType=VARCHAR},
                #{isAuth,jdbcType=INTEGER}, #{repairType,jdbcType=INTEGER}, #{repairWay,jdbcType=VARCHAR})
    </insert>
    <update id="update">
        update he_business_type
        <set>
            <if test="name != null and name != ''">
                name = #{name,jdbcType=VARCHAR},
            </if>
            <if test="parentName != null and parentName != ''">
                parent_name = #{parentName,jdbcType=VARCHAR},
            </if>
            <if test="childName != null and childName != ''">
                child_name = #{childName,jdbcType=VARCHAR},
            </if>
            <if test="isAuth != null">
                is_auth = #{isAuth,jdbcType=INTEGER},
            </if>
            <if test="repairType != null">
                repair_type = #{repairType,jdbcType=INTEGER},
            </if>
            <if test="repairWay != null and repairWay != ''">
                repair_way = #{repairWay,jdbcType=VARCHAR}
            </if>
        </set>
        <where>
            id = #{id,jdbcType=BIGINT}
        </where>
    </update>
    <delete id="delete">
        delete from he_business_type where id = #{id,jdbcType=BIGINT}
    </delete>

    <select id="queryBusinessType" resultType="com.newland.detb.exceptionmanage.entity.BusinessType">
        select id, name, parent_name, child_name, is_auth, create_time, repair_type, repair_way
        from he_business_type
        where name = #{type,jdbcType=VARCHAR}
          and parent_name = #{parentName,jdbcType=VARCHAR}
          and child_name = #{childName,jdbcType=VARCHAR}
    </select>
    <select id="queryBusinessTypeList" resultType="com.newland.detb.exceptionmanage.entity.BusinessType">
        select id, name, parent_name, child_name, is_auth, repair_type, repair_way, create_time
        from he_business_type
        <where>
            <if test="parentName != null and parentName != ''">
                parent_name like concat('%', #{parentName,jdbcType=VARCHAR}, '%')
            </if>
            <if test="childName != null and childName != ''">
                child_name like concat('%', #{childName,jdbcType=VARCHAR}, '%')
            </if>
            <!-- 固定条件 -->
            deleted = 0
        </where>
        order by create_time desc
    </select>
</mapper>
